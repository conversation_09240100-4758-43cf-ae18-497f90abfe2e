package point.common.model.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import java.util.Date;
import lombok.*;
import point.common.constant.OrderSide;
import point.common.constant.OrderType;
import point.common.serializer.BigDecimalSerializer;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PosTradeResponse {

    // 約定日時
    private Date createdAt;

    // 通貨ペア
    private Long symbolId;

    // 売買
    private OrderSide orderSide;

    // 注文種別
    private OrderType orderType;

    // 約定価格 (円)
    private BigDecimal price;

    // 約定数量 (BTC)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal amount;

    // 約定金額 (円)
    private BigDecimal assetAmount;

    // 約定番号
    private Long id;

    // 収益額
    private BigDecimal income;
    // メモ
    private String notes;

    // same as userCropStatus.growthStageId
    private String treeLevel;
}
