package point.app.invest.controller;

import io.swagger.v3.oas.annotations.Hidden;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.common.component.CsvDownloadManager;
import point.common.constant.Currency;
import point.common.constant.ErrorCode;
import point.common.constant.HistoryType;
import point.common.constant.ViewVariables;
import point.common.exception.CustomException;
import point.common.model.response.CryptoHistoryData;
import point.common.model.response.CryptoHistoryData.CryptoHistoryElement;
import point.common.model.response.CryptoHistoryReportData;
import point.common.model.response.PageData;
import point.common.service.UserService;
import point.common.util.DateUnit;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

@Hidden
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/crypto-history")
public class V1CryptoHistoryRestController {

    private final UserService userService;
    private final CsvDownloadManager<CryptoHistoryReportData> downloadManager;

    static class SortByDate implements Comparator<CryptoHistoryData.CryptoHistoryElement> {
        @Override
        public int compare(
                CryptoHistoryData.CryptoHistoryElement a,
                CryptoHistoryData.CryptoHistoryElement b) {
            return b.getDate().compareTo(a.getDate());
        }
    }

    @GetMapping
    public ResponseEntity<PageData<CryptoHistoryElement>> get(
            @AuthenticationPrincipal UserPrincipal user,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "currency", required = false) Currency currency,
            @RequestParam(value = "historyType", required = false) HistoryType historyType,
            @RequestParam(value = "address", required = false) String address,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {

        CryptoHistoryData cryptoHistoryData = new CryptoHistoryData();
        Long count = Long.valueOf(0);

        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

        List<CryptoHistoryData.CryptoHistoryElement> cryptoHistoryElements =
                new ArrayList<CryptoHistoryData.CryptoHistoryElement>(
                        cryptoHistoryData.getCryptoHistories());
        Collections.sort(cryptoHistoryElements, new SortByDate());

        if (cryptoHistoryElements != null) {
            count += cryptoHistoryElements.size();
        }

        return ResponseEntity.ok(createPageData(cryptoHistoryElements, count, number, size));
    }

    @GetMapping("/download")
    public String download(
            HttpServletResponse response,
            @AuthenticationPrincipal UserPrincipal user,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "currency", required = false) Currency currency,
            @RequestParam(value = "historyType", required = false) HistoryType historyType,
            @RequestParam(value = "address", required = false) String address)
            throws Exception {
        if (userService.findOne(user.getId()) == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
        }

        CryptoHistoryData cryptoHistoryData = new CryptoHistoryData();

        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

        List<CryptoHistoryData.CryptoHistoryElement> cryptoHistoryElements =
                new ArrayList<CryptoHistoryData.CryptoHistoryElement>(
                        cryptoHistoryData.getCryptoHistories());
        Collections.sort(cryptoHistoryElements, new SortByDate());

        List<CryptoHistoryReportData> cryptoHistoryReports =
                new ArrayList<CryptoHistoryReportData>();

        for (CryptoHistoryElement cryptoHistoryElement : cryptoHistoryElements) {
            cryptoHistoryReports.add(
                    new CryptoHistoryReportData().setProperties(cryptoHistoryElement));
        }

        String reportPreFix =
                "crypto_report_"
                        + FormatUtil.formatJst(new Date(dateFrom), FormatPattern.YYYYMMDD)
                        + "-"
                        + (dateTo != null
                                ? FormatUtil.formatJst(new Date(dateTo), FormatPattern.YYYYMMDD)
                                : "");

        downloadManager.download(response, cryptoHistoryReports, reportPreFix, true);

        return null;
    }

    private PageData<CryptoHistoryElement> createPageData(
            List<CryptoHistoryElement> content, Long count, Integer number, Integer size) {
        List<CryptoHistoryElement> pageContents = new ArrayList<CryptoHistoryElement>();

        int maxSize =
                (number * size + size) > content.size() ? content.size() : (number * size + size);

        for (int i = number * size; i < maxSize; i++) {
            pageContents.add(content.get(i));
        }

        return new PageData<CryptoHistoryElement>(number, size, count, pageContents);
    }
}
