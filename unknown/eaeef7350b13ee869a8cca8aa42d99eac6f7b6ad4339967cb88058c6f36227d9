package point.app.component;

import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import point.app.component.model.UserPrincipal;
import point.app.component.model.UserWrapper;
import point.common.constant.CommonConstants;
import point.common.constant.ErrorCode;
import point.common.constant.UserIdType;
import point.common.entity.PointPartner;
import point.common.entity.PointUser;
import point.common.entity.User;
import point.common.exception.CustomException;
import point.common.exception.PontaAuthenticationException;
import point.common.service.PontaUserCallbackService;

/** Operate user login logic handler */
@RequiredArgsConstructor
public class PontaAuthenticationProviderImpl implements AuthenticationProvider {

    private final PontaUserCallbackService pontaUserCallbackService;

    @Override
    public Authentication authenticate(Authentication authentication)
            throws AuthenticationException {
        @SuppressWarnings("unchecked")
        Map<String, String> principal = (Map<String, String>) authentication.getPrincipal();
        String secretCode = principal.get(CommonConstants.PONTA_SECRET_CODE);
        String partnerNumber = principal.get(CommonConstants.PONTA_PARTNER_NUMBER);
        // the login source
        String source = principal.get(CommonConstants.SOURCE);
        // the login client
        String client = principal.get(CommonConstants.CLIENT);

        PointPartner pointPartner = this.getPointPartner(partnerNumber, source);

        Map<String, String> memberInfo = this.getMemberInfo(secretCode);
        String memberId = memberInfo.get(CommonConstants.PONTA_MEMBER_ID);
        String accessToken = memberInfo.get(CommonConstants.ACCESS_TOKEN);

        try {
            PointUser pointUser =
                    pontaUserCallbackService.createIfNotExists(
                            memberId, pointPartner.getId(), accessToken);
            User investUser = null;
            if (Objects.nonNull(pointUser.getUserId())) {
                investUser = new User();
                investUser.setId(pointUser.getUserId());
            }

            return new UsernamePasswordAuthenticationToken(
                    UserPrincipal.from(
                            pointUser.getId(),
                            UserIdType.Operate,
                            pointUser.getPartnerMemberId(),
                            source,
                            client,
                            partnerNumber,
                            UserWrapper.builder().pointUser(pointUser).user(investUser).build()),
                    null,
                    new ArrayList<>());
        } catch (Exception e) {
            if (e instanceof CustomException ce) {
                throw new PontaAuthenticationException(ce.getErrorCode());
            }
            throw new PontaAuthenticationException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }

    private PointPartner getPointPartner(String partnerNumber, String source) {
        try {
            return pontaUserCallbackService.getWithValidatePointPartner(partnerNumber, source);
        } catch (CustomException e) {
            throw new AuthenticationServiceException(String.valueOf(e.getErrorCode().getCode()));
        } catch (Exception e) {
            throw new AuthenticationServiceException(
                    String.valueOf(ErrorCode.COMMON_ERROR_SYSTEM_ERROR.getCode()));
        }
    }

    private Map<String, String> getMemberInfo(String secretCode) {
        try {
            return pontaUserCallbackService.getPartnerMemberId(secretCode);
        } catch (CustomException e) {
            throw new AuthenticationServiceException(String.valueOf(e.getErrorCode().getCode()));
        }
    }
}
