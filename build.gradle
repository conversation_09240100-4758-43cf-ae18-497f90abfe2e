plugins {
    id 'java'
    id 'java-library'
    id 'org.flywaydb.flyway' version '8.2.0'
    id 'com.diffplug.spotless' version '6.25.0'
}

ext {
  project_name_prefix = 'bs-'
}

// 全プロジェクト共通設定
allprojects {
  apply plugin: 'java'
  apply plugin: 'java-library'
  apply plugin:'com.diffplug.spotless'
  sourceCompatibility = '17'
  targetCompatibility = '17'

  dependencies {
    implementation 'org.mariadb.jdbc:mariadb-java-client:2.7.4'
  }

  repositories {
    maven {
      url 'https://s3.amazonaws.com/redshift-maven-repository/release'
    }
    mavenCentral()
  }

  spotless {
    java {
      importOrder()
      removeUnusedImports()
      trimTrailingWhitespace()
      formatAnnotations()
      googleJavaFormat('1.23.0').aosp()
      endWithNewline()
    }
  }
  compileJava.dependsOn 'spotlessApply'
}

subprojects {
  if(it.name != 'bs-point-common') {
    def pontaCertsSourceDir = file('../ponta/certs')

    tasks.register('validateAndCopyCerts') {
      doFirst {
        if (!pontaCertsSourceDir.exists()) {
          throw new GradleException("The Ponta certificates dir not exists: ${pontaCertsSourceDir.absolutePath}")
        }
      }
    }

    tasks.register('copyCerts', Copy) {
      dependsOn('validateAndCopyCerts')
      from pontaCertsSourceDir
      into 'src/main/resources/ponta'
      include '**/*'
    }

    processResources.dependsOn('copyCerts')
  }
}

flyway {
  configFiles = ['docker/flyway-mysql/conf/flyway.conf']
}