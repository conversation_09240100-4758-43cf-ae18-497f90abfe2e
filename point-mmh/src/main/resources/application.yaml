async:
  core-pool-size: 60
#  queue-capacity: 60
#  max-pool-size: 60
aws:
  credentials:
    access-key:
    secret-key:
    salt:
  ses:
    host: email-smtp.ap-northeast-1.amazonaws.com
    port: 587
    username:
    password:
  s3:
    kyc-bucket:
      name:
cloud:
  aws:
    credentials:
      access-key:
      secret-key:
      use-default-aws-credentials-chain: false
    region:
      auto: false
      static: ap-northeast-1
    stack:
      auto: false
common:
  log:
    outputRequestDetails: true
    console:
      appender: CONSOLE_DEFAULT
management:
  server:
    port: 8082
  endpoint:
    health:
      group:
        liveness:
          include: "livenessState"
        readiness:
          include: "readinessState,manual"
      probes:
        enabled: true
  endpoints:
    metrics:
      enabled: false
    prometheus:
      enabled: false
    web:
      base-path: /actuator
      exposure:
        include: "*"
  metrics:
    export:
      cloudwatch:
        batchSize: 20
        enabled: false
        namespace: exchange-worker
        step: 1m
      prometheus:
        enabled: false
    web:
      server:
        request:
          autotime:
            # https://docs.spring.io/spring-boot/docs/current/reference/html/production-ready-features.html#production-ready-metrics-spring-mvc
            enabled: true
          metric-name: http.server.requests
server:
  port: 8080
  shutdown: graceful
spring:
  config:
    domain: localhost:8080
    environment: local
  data:
    redis:
      host: point-001.9srtan.0001.apne1.cache.amazonaws.com
      port: 6379
  datasource:
    master:
      driver-class-name: org.mariadb.jdbc.Driver
      connection-test-query: select 1
      auto-commit: false
      hibernate-dialect: org.hibernate.dialect.MySQL5Dialect
      packages-to-scan: point.common.entity,point.pos.entity,point.operate.entity
      url: # migrated to AWS Secrets Manager
      username: point
      password:
      minimum-idle: 10
      maximum-pool-size: 66
      max-lifetime: 600000
      idle-timeout: 500000
      leak-detection-threshold: 10000
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
      connection-test-query: select 1
      auto-commit: false
      hibernate-dialect: org.hibernate.dialect.PostgreSQL82Dialect
      packages-to-scan: point.admin.entity,point.common.entity,point.pos.entity
      url: # migrated to AWS Secrets Manager
      username: point
      password:
      minimum-idle: 10
      maximum-pool-size: 66
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
    show-sql: true
  main:
    banner-mode: off
point-pos:
  best-price:
    amber:
      api-host:
      api-path: /api/v2/trade/rfq
      access-key:  # migrated to AWS Secrets Manager
      access-secret: # migrated to AWS Secrets Manager
  base-trade:
    amber:
      api-host:
      api-path: /api/v2/trade/orders/spot
      get-order-api-path: /api/v2/trade/orders/client/
      access-key: # migrated to AWS Secrets Manager
      access-secret: # migrated to AWS Secrets Manager
exchange-websocket:
  redis-pubsub-cache:
    enabled: true # true: cache the message to avoid duplicate sending
    expire-in-minutes: 5 # cache will be expired in the specified period, the message will be sent even duplicate
logging:
  level:
    com:
      amazonaws:
        internal:
          InstanceMetadataServiceResourceFetcher: error
        util:
          EC2MetadataUtils: error

# store secrets in AWS Secrets Manager
ponta:
  keystore-password:
  keystore-biz-password: