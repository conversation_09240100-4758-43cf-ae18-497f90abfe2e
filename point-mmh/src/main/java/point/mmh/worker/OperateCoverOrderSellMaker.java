package point.mmh.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.constant.OrderSide;
import point.common.entity.Symbol;
import point.mmh.component.Worker;
import point.operate.service.OperateCoverOrderService;

/**
 * Worker class for executing SELL-side cover order operations
 *
 * @see OperateCoverOrderService
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateCoverOrderSellMaker extends Worker {

    private final OperateCoverOrderService operateCoverOrderService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info(
                "----------------execute start operate cover order sell worker--------------------");
        operateCoverOrderService.execute(symbol, OrderSide.SELL);
        log.info("----------------execute end  ---------------------");
    }
}
