package point.mmh.component;

import com.amazonaws.services.sns.util.Topics;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClient;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.amazonaws.services.sqs.model.CreateQueueRequest;
import com.amazonaws.services.sqs.model.CreateQueueResult;
import com.amazonaws.services.sqs.model.GetQueueAttributesResult;
import com.amazonaws.services.sqs.model.Message;
import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageResult;
import com.amazonaws.services.sqs.model.SetQueueAttributesRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.component.SnsManager;
import point.common.config.AwsConfig;
import point.common.util.JsonUtil;

@RequiredArgsConstructor
@Slf4j
@Component
public class SqsManager {

    private static final String MESSAGE_RETENTION_PERIOD = "60";

    private static final String RECEIVE_MESSAGE_WAIT_TIME_SECONDS = "20";

    private static final String VISIBILITY_TIMEOUT = "120";

    private static final int WAIT_TIME_SECONDS = 20;

    private final AwsConfig awsConfig;

    private final SnsManager snsManager;

    private final SqsCallback sqsCallback;

    private AmazonSQS sqsClient;

    private Map<String, String> queueUrlMap = new HashMap<>();

    private boolean shutdown = false;

    @PostConstruct
    private void initialize() {
        sqsClient =
                AmazonSQSClientBuilder.standard()
                        .withCredentials(awsConfig.getCredentialsProvider())
                        .withRegion(awsConfig.getRegion().getName())
                        .build();
    }

    private String getQueueUrl(String queueName) {
        String queueUrl = queueUrlMap.get(queueName);

        if (queueUrl == null) {
            CreateQueueResult createQueueResult =
                    sqsClient.createQueue(
                            new CreateQueueRequest(queueName)
                                    .addAttributesEntry(
                                            "MessageRetentionPeriod", MESSAGE_RETENTION_PERIOD)
                                    .addAttributesEntry(
                                            "ReceiveMessageWaitTimeSeconds",
                                            RECEIVE_MESSAGE_WAIT_TIME_SECONDS)
                                    .addAttributesEntry("VisibilityTimeout", VISIBILITY_TIMEOUT));
            queueUrl = createQueueResult.getQueueUrl();
            queueUrlMap.put(queueName, queueUrl);
        }

        return queueUrl;
    }

    private Map<String, String> createAttributes(String queueUrl, String topicArn) {
        List<String> attributeNames = new ArrayList<>();
        attributeNames.add("QueueArn");
        GetQueueAttributesResult getQueueAttributesResult =
                sqsClient.getQueueAttributes(queueUrl, attributeNames);

        Map<String, String> principal = new HashMap<>();
        principal.put("AWS", "*");

        Map<String, String> arnEquals = new HashMap<>();
        arnEquals.put("aws:SourceArn", topicArn);

        Map<String, Object> condition = new HashMap<>();
        condition.put("ArnEquals", arnEquals);

        Map<String, Object> statement = new HashMap<>();
        statement.put("Effect", "Allow");
        statement.put("Principal", principal);
        statement.put("Action", "SQS:SendMessage");
        statement.put("Resource", getQueueAttributesResult.getAttributes().get("QueueArn"));
        statement.put("Condition", condition);

        List<Map<String, Object>> statements = new ArrayList<>();
        statements.add(statement);

        Map<String, Object> policy = new HashMap<>();
        policy.put("Statement", statements);

        Map<String, String> attributes = new HashMap<>();
        attributes.put("Policy", JsonUtil.encode(policy));
        return attributes;
    }

    private void receiveMessage(String queueName, String queueUrl, int maxNumberOfMessages) {
        log.debug("receiveMessage: start, queueUrl=" + queueUrl);
        ReceiveMessageResult receiveMessageResult =
                sqsClient.receiveMessage(
                        new ReceiveMessageRequest(queueUrl)
                                .withMaxNumberOfMessages(maxNumberOfMessages)
                                .withWaitTimeSeconds(WAIT_TIME_SECONDS));

        log.debug("receiveMessage: end. messages=" + receiveMessageResult.getMessages().size());

        for (Message message : receiveMessageResult.getMessages()) {
            try {
                sqsCallback.callback(queueName, message.getBody());
            } catch (Exception e) {
                log.error("error, queueName=" + queueName, e);
            } finally {
                sqsClient.deleteMessage(queueUrl, message.getReceiptHandle());
            }

            if (shutdown) {
                break;
            }
        }
    }

    public void subscribe(String queueName, int maxNumberOfMessages) {
        log.info("subscribe: start, queueName=" + queueName);

        String topicArn = snsManager.getTopicArn(queueName);
        log.info("subscribe: topicArn=" + topicArn);

        String queueUrl = getQueueUrl(queueName);
        log.info("subscribe: queueUrl=" + queueUrl);

        sqsClient.setQueueAttributes(
                new SetQueueAttributesRequest(queueUrl, createAttributes(queueUrl, topicArn)));
        String subscribedArn =
                Topics.subscribeQueue(snsManager.getSnsClient(), sqsClient, topicArn, queueUrl);
        log.info("subscribe: subscribedArn=" + subscribedArn);

        while (true) {
            try {
                receiveMessage(queueName, queueUrl, maxNumberOfMessages);

                if (shutdown) {
                    break;
                }
            } catch (IllegalStateException e) {
                log.info("subscribe: catch shutdown event");
                break;
            } catch (Exception e) {
                log.error("error in loop: queueName=" + queueName, e);
            }
        }

        log.info("subscribe: end");
    }

    public void shutdown() {
        shutdown = true;
        ((AmazonSQSClient) sqsClient).shutdown();
    }
}
