package point.common.component;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ActiveProfiles;
import point.app.Application;
import point.common.config.SesConfig;
import point.common.config.SpringConfig;
import point.common.entity.User;
import point.common.service.UserService;

@SpringBootTest(classes = {Application.class})
@RequiredArgsConstructor(onConstructor = @__({@Autowired}))
@AutoConfigureWebTestClient
@ActiveProfiles({"local"})
public class SesManagerTest {

    @SpyBean SesConfig awsSesConfig;

    @SpyBean SpringConfig springConfig;

    @Autowired SesManager sesManager;

    @SpyBean UserService userService;

    @Test
    public void sendTest() throws Exception {
        var user = new User("null", "null");
        user.setTradeUncapped(false);
        doReturn(user).when(userService).findByEmail(any());
        // set valid email address when you run this test
        // sesManager.send("<EMAIL>", "<EMAIL>", "test", "test");
        TimeUnit.SECONDS.sleep(2);
    }

    @Test
    public void sendTestTradeUncapped() throws Exception {
        var user = new User("null", "null");
        user.setTradeUncapped(true);
        doReturn(user).when(userService).findByEmail(any());
        sesManager.send("<EMAIL>", "<EMAIL>", "test", "test");
    }
}
