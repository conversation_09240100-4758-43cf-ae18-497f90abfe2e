package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.common.constant.Course;
import point.common.constant.Currency;
import point.common.constant.CurrencyPair;
import point.common.constant.TradeType;
import point.common.entity.Asset;
import point.common.entity.Symbol;
import point.common.entity.UserCropStatus;
import point.common.entity.UserMonsterInfo;
import point.common.model.response.FarmInfoInvestResponse;
import point.common.service.*;
import point.pos.model.PosBestPriceData;
import point.pos.service.PosBestPriceService;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/invest/farm-info")
@Timed
public class FarmInfoInvestController {

    private final AssetService assetService;
    private final SymbolService symbolService;
    private final PosBestPriceService posBestPriceService;
    private final UserMonsterInfoService userMonsterInfoService;
    private final UserCropStatusService userCropStatusService;

    @GetMapping("/select")
    @Operation(
            summary = "get Invest farm-info",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "200",
                        description = "Success"),
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "400",
                        description = "Bad Request")
            })
    public ResponseEntity<FarmInfoInvestResponse> get(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user)
            throws Exception {

        FarmInfoInvestResponse farmInfoInvestResponse = new FarmInfoInvestResponse();
        farmInfoInvestResponse.setUserId(user.getId());

        List<Course> courseList = new ArrayList<>();
        List<Asset> userAssets = assetService.findUserAssets(user.getId());
        BigDecimal profitLossAmtTotal = BigDecimal.ZERO;
        BigDecimal incomeTotal = BigDecimal.ZERO;

        for (Asset asset : userAssets) {
            if (Currency.JPY.getName().equals(asset.getCurrency().getName())
                    && asset.getOnhandAmount() != null) {
                // asset.getCurrency() == Currency.JPY
                farmInfoInvestResponse.setJpyBalance(
                        asset.getOnhandAmount().setScale(0, RoundingMode.DOWN));
            } else if (Currency.POINT.getName().equals(asset.getCurrency().getName())
                    && asset.getOnhandAmount() != null) {
                // asset.getCurrency() == Currency.POINT
                farmInfoInvestResponse.setInvestBalancePoints(
                        asset.getOnhandAmount().setScale(0, RoundingMode.DOWN));
            }

            Symbol symbol =
                    symbolService.findByCondition(
                            TradeType.INVEST,
                            CurrencyPair.valueOf(asset.getCurrency(), Currency.JPY));
            log.info("[Invest] asset symbol: {}", symbol);
            if (Objects.isNull(symbol)) {
                continue;
            }

            PosBestPriceData bestPrice = posBestPriceService.getBestPrice(symbol.getId());
            BigDecimal nowPrice =
                    Optional.ofNullable(bestPrice)
                            .map(PosBestPriceData::getBestBid)
                            .orElse(BigDecimal.ZERO);
            BigDecimal onhandAmount =
                    Optional.ofNullable(asset.getOnhandAmount()).orElse(BigDecimal.ZERO);
            BigDecimal avgAcqUnitPrice =
                    Optional.ofNullable(asset.getAvgAcqUnitPrice())
                            .orElse(nowPrice.setScale(3, RoundingMode.HALF_UP));

            if (onhandAmount.compareTo(BigDecimal.ZERO) == 0) {
                avgAcqUnitPrice = BigDecimal.ZERO;
            }

            BigDecimal profitLossAmount =
                    onhandAmount
                            .multiply(nowPrice.subtract(avgAcqUnitPrice))
                            .setScale(2, RoundingMode.HALF_UP);

            // TODO: Improve this, save the user crop when the asset changed better
            Integer growthStageId =
                    userCropStatusService
                            .save(user.getId(), asset.getId(), TradeType.INVEST, profitLossAmount)
                            .map(UserCropStatus::getGrowthStageId)
                            .orElse(NumberUtils.INTEGER_ZERO);

            Course course = new Course();
            course.setTreeLevel(growthStageId.toString());
            course.setCourse(asset.getCurrency().getName());
            profitLossAmtTotal = profitLossAmtTotal.add(profitLossAmount);
            incomeTotal = incomeTotal.add(onhandAmount.multiply(avgAcqUnitPrice));
            course.setSymbolId(symbol.getId());
            courseList.add(course);
        }

        // ADD evalProfitLossAmtTotal&evalProfitLossAmtRateTotal
        farmInfoInvestResponse.setSumEvalProfitLossAmt(
                profitLossAmtTotal.setScale(2, RoundingMode.HALF_UP));
        farmInfoInvestResponse.setSumevalProfitLossAmtRate(BigDecimal.ZERO);
        if (incomeTotal.compareTo(BigDecimal.ZERO) != 0) {
            farmInfoInvestResponse.setSumevalProfitLossAmtRate(
                    profitLossAmtTotal.divide(incomeTotal, 4, RoundingMode.HALF_UP));
        }

        farmInfoInvestResponse.setSumInvestPoints(
                incomeTotal.add(profitLossAmtTotal).setScale(2, RoundingMode.HALF_UP));
        farmInfoInvestResponse.setCurrencyList(courseList);

        // add monster level
        UserMonsterInfo monsterLevel = userMonsterInfoService.findByUserId(user.getId());
        if (monsterLevel != null) {
            farmInfoInvestResponse.setMonsterLevel(monsterLevel.getLevel());
        }
        return ResponseEntity.ok(farmInfoInvestResponse);
    }
}
