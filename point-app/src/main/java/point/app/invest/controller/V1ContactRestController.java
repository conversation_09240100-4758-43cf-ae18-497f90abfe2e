package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Hidden;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.RecaptchaManager;
import point.app.config.PointAppConfig;
import point.app.invest.model.request.ContactForm;
import point.common.component.RedisManager;
import point.common.component.SesManager;
import point.common.config.SpringConfig;
import point.common.constant.ErrorCode;
import point.common.constant.LineFeed;
import point.common.exception.CustomException;
import point.common.util.StringUtil;

@Hidden
@RequestMapping("/app/v1/contact")
@RequiredArgsConstructor
@RestController
@Timed
public class V1ContactRestController {

    private static final long EXPIRED_MINUTE = 5;

    private final PointAppConfig exchangeAppConfig;

    private final RecaptchaManager recaptchaManager;

    private final RedisManager redisManager;

    private final SesManager sesManager;

    private final SpringConfig springConfig;

    @PostMapping
    public ResponseEntity<Object> post(
            HttpServletRequest request, @Valid @RequestBody ContactForm form) throws Exception {
        recaptchaManager.verify(form.getRecaptchaToken());

        // get redis keyValue
        String value = redisManager.get(generateKey(form.getEmail(), request.getRemoteAddr()));

        if (!StringUtils.isEmpty(value)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_SEND_REPEATED_CONTACT);
        }

        // set redis keyValue
        redisManager.set(
                generateKey(form.getEmail(), request.getRemoteAddr()), "1", EXPIRED_MINUTE);

        // validation
        if (StringUtils.isEmpty(form.getName())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_NAME);
        }

        if (StringUtils.isEmpty(form.getEmail())
                || (!springConfig.isPrd() && !StringUtil.validateEmail(form.getEmail()))
                || (springConfig.isPrd()
                        && !StringUtil.validateEmailExceptAlias(form.getEmail()))) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_EMAIL);
        }

        if (StringUtils.isEmpty(form.getSubject())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_SUBJECT);
        }

        if (StringUtils.isEmpty(form.getContent())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_CONTANT);
        }

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("名前：").append(form.getName()).append(LineFeed.HTML.getValue());
        stringBuilder.append("メールアドレス：").append(form.getEmail()).append(LineFeed.HTML.getValue());
        stringBuilder.append("件名：").append(form.getSubject()).append(LineFeed.HTML.getValue());
        stringBuilder.append("内容：").append(form.getContent());

        sesManager.send(
                exchangeAppConfig.getNoReplyEmail(),
                exchangeAppConfig.getSupportEmail(),
                form.getSubject(),
                stringBuilder.toString());

        return ResponseEntity.ok().build();
    }

    private String generateKey(String email, String ipAddress) {
        return String.format("contact:%s:%s", email, ipAddress);
    }
}
