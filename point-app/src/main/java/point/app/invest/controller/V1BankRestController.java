package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Hidden;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.common.entity.Bank;
import point.common.model.response.BankAllNameData;
import point.common.model.response.BankBranchNameData;
import point.common.service.BankService;

@Hidden
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/bank")
@Timed
public class V1BankRestController {

    private final BankService bankService;

    @GetMapping
    public ResponseEntity<List<Bank>> get(
            @AuthenticationPrincipal UserPrincipal user,
            @RequestParam(value = "bankName", required = false) String bankName,
            @RequestParam(value = "bankCode", required = false) Integer bankCode,
            @RequestParam(value = "branchName", required = false) String branchName,
            @RequestParam(value = "branchCode", required = false) Integer branchCode)
            throws Exception {
        return ResponseEntity.ok(
                bankService.findAllByCondition(bankName, bankCode, branchName, branchCode));
    }

    @GetMapping("/bank-name")
    public ResponseEntity<List<BankAllNameData>> getBankName(
            @AuthenticationPrincipal UserPrincipal user) throws Exception {

        List<BankAllNameData> responseBankAllNameData = new ArrayList<>();
        List<Integer> bankAllCode = new ArrayList<>();

        for (Bank bank : bankService.findAllBankName()) {
            if (!bankAllCode.contains(bank.getBankCode())) {
                bankAllCode.add(bank.getBankCode());
                BankAllNameData bankAllNameData = new BankAllNameData();
                bankAllNameData.setBankCode(bank.getBankCode());
                bankAllNameData.setBankName(bank.getBankName());
                responseBankAllNameData.add(bankAllNameData);
            }
        }

        return ResponseEntity.ok(responseBankAllNameData);
    }

    @GetMapping("/branch-name")
    public ResponseEntity<List<BankBranchNameData>> getBranchName(
            @AuthenticationPrincipal UserPrincipal user,
            @RequestParam(value = "bankCode", required = false) Integer bankCode)
            throws Exception {

        List<BankBranchNameData> responseBankBranchNameData = new ArrayList<>();

        for (Bank bank : bankService.findAllByCondition("", bankCode, "", null)) {
            BankBranchNameData bankBranchNameData = new BankBranchNameData();
            bankBranchNameData.setBankId(bank.getId());
            bankBranchNameData.setBranchCode(bank.getBranchCode());
            bankBranchNameData.setBranchName(bank.getBranchName());
            responseBankBranchNameData.add(bankBranchNameData);
        }

        return ResponseEntity.ok(responseBankBranchNameData);
    }
}
