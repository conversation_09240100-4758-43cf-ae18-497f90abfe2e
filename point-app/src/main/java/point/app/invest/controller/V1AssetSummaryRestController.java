package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Hidden;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.common.constant.Currency;
import point.common.constant.TradeType;
import point.common.entity.AssetSummary;
import point.common.entity.CurrencyConfig;
import point.common.service.AssetSummaryService;
import point.common.service.CurrencyConfigService;

@Hidden
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/asset-summary")
@Timed
public class V1AssetSummaryRestController {

    private final AssetSummaryService assetSummaryService;
    private final CurrencyConfigService currencyConfigService;

    @GetMapping
    public ResponseEntity<List<AssetSummary>> get(
            @AuthenticationPrincipal UserPrincipal user,
            @RequestParam(value = "currency", required = false) Currency currency,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {
        Date dateFromDate = dateFrom != null ? new Date(dateFrom) : null;
        Date dateToDate = dateTo != null ? new Date(dateTo) : null;

        List<AssetSummary> assetSummaryList = new ArrayList<>();

        // 入力のcurrencyが有効な(enabled=true)通貨かチェック
        List<CurrencyConfig> currencyConfigs =
                currencyConfigService.findAllByCondition(TradeType.INVEST, currency, true);

        if (CollectionUtils.isEmpty(currencyConfigs)) {
            return ResponseEntity.ok(assetSummaryList);
        }

        assetSummaryList =
                assetSummaryService.findByCondition(
                        user.getId(), currency, dateFromDate, dateToDate);
        return ResponseEntity.ok(assetSummaryList);
    }
}
