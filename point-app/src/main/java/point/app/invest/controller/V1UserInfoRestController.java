package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Hidden;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.MfaManager;
import point.app.component.model.UserPrincipal;
import point.app.invest.model.request.UserInfoPhoneNumberOtpauthPutForm;
import point.app.invest.model.request.UserInfoPhoneNumberPutForm;
import point.common.component.CustomTransactionManager;
import point.common.component.SesManager;
import point.common.constant.*;
import point.common.constant.ReportLabel.UserStatus;
import point.common.entity.User;
import point.common.entity.UserEkyc;
import point.common.entity.UserInfo;
import point.common.entity.UserKyc;
import point.common.exception.CustomException;
import point.common.model.request.UpdateUserInfoForm;
import point.common.model.request.UserInfoForm;
import point.common.model.response.MfaTypeData;
import point.common.model.response.ValidateData;
import point.common.repos.UserEkycRepository;
import point.common.service.MailNoreplyService;
import point.common.service.UserAuthorityService;
import point.common.service.UserEkycService;
import point.common.service.UserInfoService;
import point.common.service.UserKycService;
import point.common.service.UserService;
import point.common.util.StringUtil;

@Hidden
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/user-info")
@Timed
@Slf4j
public class V1UserInfoRestController {

    private final MfaManager mfaManager;

    private final UserAuthorityService userAuthorityService;

    private final UserInfoService userInfoService;

    private final UserService userService;

    private final UserEkycService userEkycService;

    private final UserKycService userKycService;

    private final MailNoreplyService mailNoreplyService;

    private final SesManager sesManager;

    private final CustomTransactionManager customTransactionManager;

    //
    // partsCheck: ture => null以外のformパラメータのバリデーションを行う
    // partsCheck: false => 全formパラメータのバリデーションを行う
    //
    private ValidateData validateUserInfoForm(User user, UserInfoForm form, boolean partsCheck) {
        ValidateData validateData = new ValidateData();

        final var isEditing = user.getUserInfo() != null;

        if (userAuthorityService.findAuthority(user.getId(), Authority.PERSONAL) == null) {
            validateData
                    .getErrors()
                    .add(ErrorCode.REQUEST_ERROR_USER_INFO_INCORRECT_AUTHORITY.getCode());
            return validateData;
        }

        if (!partsCheck || form.getFirstName() != null) {
            if (!form.validateFirstName(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_FIRST_NAME.getCode());
            }
        }

        if (!partsCheck || form.getFirstKana() != null) {
            if (!form.validateFirstKana(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_FIRST_KANA.getCode());
            }
        }

        if (!partsCheck || form.getLastName() != null) {
            if (!form.validateLastName(false)) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_LAST_NAME.getCode());
            }
        }

        if (!partsCheck || form.getLastKana() != null) {
            if (!form.validateLastKana(false)) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_LAST_KANA.getCode());
            }
        }

        if (!partsCheck || form.getNationality() != null) {
            if (!form.validateNationality(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_NATIONALITY.getCode());
            }
        }

        if (!partsCheck || form.getZipCode() != null) {
            if (!form.validateZipCode(false)) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_ZIP_CODE.getCode());
            }
        }

        if (!partsCheck || form.getPrefecture() != null) {
            if (!form.validatePrefecture(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_PREFECTURE.getCode());
            }
        }

        if (!partsCheck || form.getCity() != null) {
            if (!form.validateCity(false)) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_CITY.getCode());
            }
        }

        if (!partsCheck || form.getAddress() != null) {
            if (!form.validateAddress(false)) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_ADDRESS.getCode());
            }
        }

        if (!partsCheck || form.getBuilding() != null) {
            if (!form.validateBuilding(true)) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_BUILDING.getCode());
            }
        }

        if (!partsCheck || form.getBirthday() != null) {
            if (!form.validateBirthday(false)) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_BIRTHDAY.getCode());
            }
        }

        if (!partsCheck || form.getGender() != null) {
            if (!form.validateGender(false)) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_GENDER.getCode());
            }
        }

        if (!partsCheck || form.getPhoneNumber() != null) {
            if (!form.validatePersonalPhoneNumber(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_INVALID_PERSONAL_PHONE_NUMBER.getCode());
            }
            Long countByPhoneNumber = 0L;
            if (form.validatePersonalPhoneNumber(false)) {
                List<UserInfo> res =
                        (List<UserInfo>)
                                userInfoService.findOneByPhoneNumber(form.getPhoneNumber());
                for (UserInfo userInfo : res) {
                    User users = (User) userService.findById(userInfo.getUserId());
                    if (!UserStatus.LEFT.name().equals(users.getUserStatus().name())) {
                        countByPhoneNumber = countByPhoneNumber + 1;
                    }
                }
                if (countByPhoneNumber == 1 && isEditing) {
                    if (!user.getUserInfo().getPhoneNumber().equals(form.getPhoneNumber())) {
                        // 編集中でも自身の番号の場合はエラーとする
                        validateData
                                .getErrors()
                                .add(
                                        ErrorCode.REQUEST_ERROR_USER_INFO_PHONE_NUMBER_EXISTS
                                                .getCode());
                    }
                } else if (countByPhoneNumber != 0) {
                    validateData
                            .getErrors()
                            .add(ErrorCode.REQUEST_ERROR_USER_INFO_PHONE_NUMBER_EXISTS.getCode());
                }
            }
        }

        if (!partsCheck || form.getOccupation() != null) {
            if (!form.validateOccupation(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_OCCUPATION.getCode());
            }

            if (!form.validateIndustry(form.notEmptyOccupation(false))) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_INDUSTRY.getCode());
            }

            if (!form.validateWorkPlace(form.canEmptyOccupation(false))) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_WORKPLACE.getCode());
            }
            if (!form.validatePosition(form.canEmptyOccupation(false))) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_POSITION.getCode());
            }
        }

        if (!partsCheck || form.getIncome() != null) {
            if (!form.validateIncome(false)) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_INCOME.getCode());
            }
        }

        if (!partsCheck || form.getFinancialAssets() != null) {
            if (!form.validateFinancialAssets(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_FINANCIAL_ASSETS.getCode());
            }
        }

        if (!partsCheck || form.getPurpose() != null) {
            if (!form.validatePurpose(false)) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_PURPOSE.getCode());
            }
        }

        if (!partsCheck || form.getInvestmentPurposes() != null) {
            if (!form.validateInvestmentPurposes(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_INVESTMENT_PURPOSE.getCode());
            }
        }

        if (!partsCheck || form.getCryptoExperience() != null) {
            if (!form.validateCryptoExperience(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_CRYPTO_EXPERIENCE.getCode());
            }
        }

        if (!partsCheck || form.getStocksExperience() != null) {
            if (!form.validateStocksExperience(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_STOCKS_EXPERIENCE.getCode());
            }
        }

        if (!partsCheck || form.getFundExperience() != null) {
            if (!form.validateFundExperience(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_FUND_EXPERIENCE.getCode());
            }
        }

        if (!partsCheck || form.getApplicationHistory() != null) {
            if (!form.validateApplicationHistory(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_APPLICATION_HISTORY.getCode());
            }

            if (!form.validateApplicationHistoryOther(!form.IsApplicationHistoryOther(false))) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_APPLICATION_HISTORY_OTHER.getCode());
            }
        }

        if (!partsCheck || form.getForeignPeps() != null) {
            if (!form.validateForeignPeps(false)) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_FOREIGN_PEPS.getCode());
            }
        }

        if (!partsCheck || form.getCountry() != null) {
            if (!form.validateCountry(false)) {
                validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_COUNTRY.getCode());
            }
        }

        if (!partsCheck || form.getResidenceStatus() != null) {
            if (!form.validateResidenceStatus(
                    Country.JP.getLabel().equals(form.getNationality()))) {
                validateData
                        .getErrors()
                        .add(ErrorCode.REQUEST_ERROR_USER_INFO_RESIDENCE_STATUS_VALIDATE.getCode());
            }
        }

        if (!partsCheck || form.getResidenceCardExpiredAt() != null) {
            if (!form.validateResidenceCardExpiredAt(true)) {
                validateData
                        .getErrors()
                        .add(
                                ErrorCode.REQUEST_ERROR_USER_INFO_RESIDENCE_CARD_EXPIRED_AT_VALIDATE
                                        .getCode());
            }
        }
        return validateData;
    }

    @GetMapping("/personal")
    public ResponseEntity<UserInfo> getPersonal(@AuthenticationPrincipal UserPrincipal user)
            throws Exception {
        User currentUser = userService.findOne(user.getId());
        return ResponseEntity.ok(userInfoService.findOne(currentUser.getUserInfoId()));
    }

    private final UserEkycRepository repository;

    @PostMapping("/personal")
    public ResponseEntity<ValidateData> postPersonal(
            @AuthenticationPrincipal UserPrincipal user, @RequestBody UserInfoForm form)
            throws Exception {
        ValidateData validateData =
                validateUserInfoForm(user.getUserWrapper().getUser(), form, false);

        if (validateData.getErrors().size() > 0) {
            return ResponseEntity.badRequest().body(validateData);
        }

        KycStatus status = user.getUserWrapper().getUser().getKycStatus();
        if (KycStatus.DOCUMENT_REJECTED == user.getUserWrapper().getUser().getKycStatus()) {
            List<UserEkyc> res = userEkycService.findOneByUserId(user.getId());
            for (UserEkyc userEkyc : res) {
                Date createTime =
                        Date.from(
                                Instant.ofEpochMilli(userEkyc.getCreatedAt().getTime())
                                        .minus(4, ChronoUnit.HOURS));
                userEkyc.setCreatedAt(createTime);
                userEkyc.setUpdatedAt(createTime);
                repository.save(userEkyc);
            }
        }

        User userForUpdate = userService.findOne(user.getId());
        final var isNew = userForUpdate.getUserInfo() == null;

        customTransactionManager.execute(
                entityManager -> {
                    UserInfo userInfo = new UserInfo(user.getId());
                    userInfo.setInsider(form.getInsider());
                    if ("9".equals(form.getOccupation())
                            || "10".equals(form.getOccupation())
                            || "11".equals(form.getOccupation())) {
                        form.setPosition(null);
                        form.setWorkPlace(null);
                    } else {
                        form.setPriceFrom(null);
                    }
                    if (!("1".equals(form.getOccupation())
                            || "2".equals(form.getOccupation())
                            || "7".equals(form.getOccupation()))) {
                        form.setIndustry(null);
                    }
                    userInfo = userInfoService.save(userInfo.setProperties(form), entityManager);
                    final var newUserKyc = new UserKyc(userForUpdate.getId());
                    newUserKyc.setKycStatus(KycStatus.DOCUMENT_WAITING_APPLY);
                    newUserKyc.setOperator(CommonConstants.APP);
                    newUserKyc.setUserInfoId(userInfo.getId());
                    userKycService.save(newUserKyc, entityManager);
                    userForUpdate.setUserInfoId(userInfo.getId());
                    userForUpdate.setUserKycId(newUserKyc.getId());
                    userForUpdate.setKycStatus(newUserKyc.getKycStatus());
                    if (form.getInsider() != null) {
                        userForUpdate.setInsider(form.getInsider());
                    }
                    userService.saveWithAuthenticationPrincipal(userForUpdate, entityManager);
                });

        // Ekycのデータ作成、メール送信
        if (isNew) {
            userEkycService.createEkyc(userForUpdate.getId());
        }

        return ResponseEntity.ok().build();
    }

    @PostMapping("/personal/validate")
    public ResponseEntity<ValidateData> postPersonalValidate(
            @AuthenticationPrincipal UserPrincipal user, @RequestBody UserInfoForm form)
            throws Exception {
        ValidateData validateData =
                validateUserInfoForm(user.getUserWrapper().getUser(), form, true);

        if (validateData.getErrors().size() > 0) {
            return ResponseEntity.badRequest().body(validateData);
        }

        return ResponseEntity.ok().build();
    }

    private void validatePhoneNumber(User user, UserInfoPhoneNumberOtpauthPutForm form)
            throws Exception {
        if (userAuthorityService.findAuthority(user.getId(), Authority.PERSONAL) == null) {
            if (!StringUtil.validatePhoneNumber(form.getPhoneNumber())) {
                throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_PHONE_NUMBER);
            }
        } else {
            if (!StringUtil.validatePersonalPhoneNumber(form.getPhoneNumber())) {
                throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_PERSONAL_PHONE_NUMBER);
            }
        }
    }

    @PutMapping("/phone-number/otpauth")
    public ResponseEntity<MfaTypeData> putPhoneNumberOtpauth(
            @AuthenticationPrincipal UserPrincipal user,
            @Valid @RequestBody UserInfoPhoneNumberOtpauthPutForm form)
            throws Exception {
        validatePhoneNumber(user.getUserWrapper().getUser(), form);
        return mfaManager.responseMfaTypeData(user.getId(), user.getEmail());
    }

    @PutMapping("/phone-number")
    public ResponseEntity<Object> putPhoneNumber(
            @AuthenticationPrincipal UserPrincipal user,
            @Valid @RequestBody UserInfoPhoneNumberPutForm form)
            throws Exception {
        mfaManager.authenticate(user.getId(), user.getEmail(), form.getMfaCode());
        validatePhoneNumber(user.getUserWrapper().getUser(), form);

        User userForUpdate = userService.findOne(user.getId());
        if (userForUpdate.getUserInfo() != null) {
            userForUpdate.getUserInfo().setPhoneNumber(form.getPhoneNumber());
            userForUpdate.getUserInfo().setCountry(Country.valueOfName(form.getCountry()));
            userInfoService.save(userForUpdate.getUserInfo());
        }
        return ResponseEntity.ok().build();
    }

    @PostMapping("/updatePersonal")
    public ResponseEntity<Object> postUpdatePersonal(
            @AuthenticationPrincipal UserPrincipal user, @RequestBody UpdateUserInfoForm form)
            throws Exception {
        // verify the form information
        ValidateData validateData =
                validateUserInfoForm(user.getUserWrapper().getUser(), form, false);
        Integer errorCode = ErrorCode.REQUEST_ERROR_USER_INFO_PHONE_NUMBER_EXISTS.getCode();
        boolean isPhoneNumber =
                validateData.getErrors().stream().anyMatch(i -> i.equals(errorCode));
        if (validateData.getErrors().size() > 0 && !isPhoneNumber) {
            return ResponseEntity.badRequest().body(validateData);
        }
        // query user information
        User userForUpdate = userService.findOne(user.getId());

        // check if the user info is null
        if (userInfoService.findOne(userForUpdate.getUserInfoId()) == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_USER_INFO_NOT_FOUND);
        }
        if ("9".equals(form.getOccupation())
                || "10".equals(form.getOccupation())
                || "11".equals(form.getOccupation())) {
            form.setPosition(null);
            form.setWorkPlace(null);
        } else {
            form.setPriceFrom(null);
        }
        if (!("1".equals(form.getOccupation())
                || "2".equals(form.getOccupation())
                || "7".equals(form.getOccupation()))) {
            form.setIndustry(null);
        }
        userInfoService.updateUserInfo(userForUpdate, form);
        return ResponseEntity.ok(HttpStatus.OK);
    }
}
