package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.common.constant.CandlestickType;
import point.common.controller.AbstractRestController;
import point.common.entity.CurrencyPairConfig;
import point.common.entity.Symbol;
import point.common.model.response.PosCandlestickData;
import point.common.service.CurrencyPairConfigService;
import point.common.service.SymbolService;
import point.pos.entity.PosCandlestick;
import point.pos.service.PosCandlestickService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/pos/candlestick")
@Timed
public class V1PosCandlestickRestController extends AbstractRestController {

    private final SymbolService symbolService;
    private final CurrencyPairConfigService currencyPairConfigService;
    private final PosCandlestickService posCandlestickService;

    @GetMapping
    public ResponseEntity<PosCandlestickData> get(
            HttpServletResponse response,
            @RequestParam(value = "symbolId") Long symbolId,
            @RequestParam(value = "candlestickType") String paramCandlestickType,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {
        setCacheControlForPublic(response);
        List<PosCandlestick> candlestick = new ArrayList<>();
        Symbol symbol = symbolService.findOne(symbolId);

        if (symbol == null) {
            return ResponseEntity.ok(new PosCandlestickData(symbolId, candlestick));
        }

        // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
        List<CurrencyPairConfig> currencyPairConfigs =
                currencyPairConfigService.findAllByCondition(
                        symbol.getTradeType(), symbol.getCurrencyPair(), true);

        if (CollectionUtils.isEmpty(currencyPairConfigs)) {
            return ResponseEntity.ok(new PosCandlestickData(symbolId, candlestick));
        }

        CandlestickType candlestickType = CandlestickType.valueOf(paramCandlestickType);

        // 最新の足(降順)から500件固定で取得し、昇順に戻す
        candlestick =
                posCandlestickService.findByCondition(
                        symbolId,
                        candlestickType,
                        dateFrom != null ? new Date(dateFrom) : null,
                        dateTo != null ? new Date(dateTo) : null,
                        500);
        Collections.reverse(candlestick);
        return ResponseEntity.ok(new PosCandlestickData(symbolId, candlestick));
    }
}
