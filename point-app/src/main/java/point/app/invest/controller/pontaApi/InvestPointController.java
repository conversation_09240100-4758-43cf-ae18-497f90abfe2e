package point.app.invest.controller.pontaApi;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import point.app.component.model.UserPrincipal;
import point.common.constant.*;
import point.common.entity.PointPartner;
import point.common.entity.PointTransfer;
import point.common.exception.GameException;
import point.common.model.request.PointForm;
import point.common.model.response.GlobalApiResponse;
import point.common.model.response.PageData;
import point.common.model.response.PointTransferResponse;
import point.common.ponta.PontaBizInVokerApi;
import point.common.service.PointTransferService;

@RestController
@RequestMapping("/app/v1/invest/point")
@RequiredArgsConstructor
public class InvestPointController {

    private final PointTransferService pointTransferService;
    private static final String SUCCESS_CODE_VALUE = "N000000000";
    private static final String ERROR_CODE_N058 = "N058";
    private static final String ERROR_CODE_G000 = "G000";

    /** ポイント残高 */
    @PostMapping("/ponta-to-investiment")
    @Operation(
            summary = "Convert points to investment",
            security = @SecurityRequirement(name = "x-auth"),
            requestBody =
                    @io.swagger.v3.oas.annotations.parameters.RequestBody(
                            description = "Point form data",
                            required = true,
                            content = @Content(schema = @Schema(implementation = PointForm.class))),
            responses = {@ApiResponse(responseCode = "200", description = "Success")})
    public ResponseEntity<GlobalApiResponse<Object>> pontaToInvestiment(
            @AuthenticationPrincipal UserPrincipal user, @RequestBody PointForm pointForm)
            throws Exception {
        if (user == null) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    ErrorCode.GAME_USER_NOT_FOUND.getCode(),
                                    ErrorCode.GAME_USER_NOT_FOUND.getMessage()));
        }
        // pointAmount が null かどうかを検証
        if (pointForm.getPointAmount() == null) {
            throw new GameException(
                    ErrorCode.PONTA_POINTS_CANNOT_BE_NULL,
                    ErrorCode.PONTA_POINTS_CANNOT_BE_NULL.getMessage());
        }
        // pointAmount が負の数かどうかを検証
        if (pointForm.getPointAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new GameException(
                    ErrorCode.PONTA_INVALID_NEGATIVE_PARAMETER_ERROR,
                    ErrorCode.PONTA_INVALID_NEGATIVE_PARAMETER_ERROR.getMessage());
        }
        KycStatus kycStatus = user.getUserWrapper().getUser().getKycStatus();
        if (!KycStatus.ACCOUNT_OPENING_DONE.equals(kycStatus)) {
            throw new GameException(
                    ErrorCode.PONTA_INVEST_ACCOUNT_OPENING_DONE,
                    ErrorCode.PONTA_INVEST_ACCOUNT_OPENING_DONE.getMessage());
        }
        PointPartner pointPartner =
                user.getUserWrapper().getUser().getPointUser().getPointPartner();
        if (pointPartner == null) {
            throw new GameException(
                    ErrorCode.PARTNER_INFO_NOT_FOUND,
                    ErrorCode.PARTNER_INFO_NOT_FOUND.getMessage());
        } else if (!pointPartner.isValidate()) {
            throw new GameException(
                    ErrorCode.PARTNER_INFO_NOT_VALID,
                    ErrorCode.PARTNER_INFO_NOT_VALID.getMessage());
        }
        // 変換ロジックを実行
        String responseCode =
                pointTransferService.transferPontaToInvestiment(
                        user.getId(),
                        pointPartner.getId(),
                        user.getUserIdType(),
                        pointForm.getPointAmount(),
                        PointTransferTypeEnum.IN);

        if (SUCCESS_CODE_VALUE.equals(responseCode)) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    HttpStatus.OK.value(),
                                    BizCode.PONTA_POINT_EXCHANGE_SUCCESS.getMessage()));
        } else if (responseCode.startsWith(ERROR_CODE_N058)
                || responseCode.startsWith(ERROR_CODE_G000)) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.PONTA_RESPONSE_ERROR_CODE.getMessage()));
        } else {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.PONTA_RESPONSE_ERROR_CODE.getMessage(
                                            PontaBizInVokerApi.extractErrorPrefix(responseCode))));
        }
    }

    @GetMapping("/charge-history")
    @Operation(
            summary = "Get charge history",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {@ApiResponse(responseCode = "200", description = "Success")})
    public ResponseEntity<GlobalApiResponse<PageData<PointTransferResponse>>> getExchangeHistory(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {
        List<PointTransferResponse> pointTransferResponseList = new ArrayList<>();
        PageData<PointTransfer> pageData =
                pointTransferService.findByConditionPage(user.getId(), number, size);
        pageData.getContent()
                .forEach(
                        pointTransfer -> {
                            pointTransferResponseList.add(
                                    PointTransferResponse.builder()
                                            .id(pointTransfer.getId())
                                            .userId(pointTransfer.getUserId())
                                            .userIdType(pointTransfer.getUserIdType().toString())
                                            .transferType(
                                                    pointTransfer.getTransferType().toString())
                                            .amount(pointTransfer.getAmount())
                                            .status(pointTransfer.getStatus().getCode())
                                            .requestTime(pointTransfer.getRequestTime())
                                            .transferTime(pointTransfer.getTransferTime())
                                            .createdAt(pointTransfer.getCreatedAt())
                                            .build());
                        });
        return ResponseEntity.ok()
                .body(
                        new GlobalApiResponse<>(
                                HttpStatus.OK.value(),
                                new PageData<>(
                                        number,
                                        size,
                                        pageData.getTotalElements(),
                                        pointTransferResponseList)));
    }
}
