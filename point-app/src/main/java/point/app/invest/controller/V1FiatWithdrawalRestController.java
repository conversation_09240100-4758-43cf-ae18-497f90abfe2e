package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Hidden;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.MfaManager;
import point.app.component.model.UserPrincipal;
import point.app.invest.model.request.FiatWithdrawalOtpauthPostForm;
import point.app.invest.model.request.FiatWithdrawalPostForm;
import point.common.component.SesManager;
import point.common.constant.*;
import point.common.entity.Asset;
import point.common.entity.CurrencyConfig;
import point.common.entity.FiatWithdrawal;
import point.common.entity.MailNoreply;
import point.common.exception.CustomException;
import point.common.model.response.MfaTypeData;
import point.common.service.AssetService;
import point.common.service.BankAccountService;
import point.common.service.CurrencyConfigService;
import point.common.service.FiatWithdrawalService;
import point.common.service.MailNoreplyService;

@Hidden
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/fiat-withdrawal")
@Timed
@Slf4j
public class V1FiatWithdrawalRestController {

    private final AssetService assetService;

    private final BankAccountService bankAccountService;

    private final CurrencyConfigService currencyConfigService;

    private final FiatWithdrawalService fiatWithdrawalService;

    private final MailNoreplyService mailNoreplyService;

    private final MfaManager mfaManager;

    private final SesManager sesManager;

    @Value("${customer.fiat-withdrawal.user-daily-withdrawal-limit:********}")
    private String userDailyWithdrawalLimit;

    private void validatePost(Long userId, FiatWithdrawalOtpauthPostForm form) throws Exception {
        if (bankAccountService.findOne(form.getBankAccountId()) == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_BANK_ACCOUNT_NOT_FOUND);
        }

        CurrencyConfig currencyConfig =
                currencyConfigService.findByCurrency(Currency.JPY, TradeType.INVEST);

        if (!currencyConfig.isWithdrawable() || !currencyConfig.isEnabled()) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_IS_NOT_WITHDRAWABLE);
        }

        if (form.getAmount().compareTo(currencyConfig.getMinWithdrawalAmount()) == -1) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_AMOUNT_OUT_OF_RANGE);
        }

        Asset asset = assetService.findOne(userId, Currency.JPY);

        if (asset.getOnhandAmount().subtract(asset.getLockedAmount()).compareTo(form.getAmount())
                == -1) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_AMOUNT_OUT_OF_RANGE);
        }

        isOverUserDailyWithdrawalLimit(userId, form.getAmount());
    }

    /**
     * 利用者ごとの合計一日出金上限額を超えるかどうかをチェック
     *
     * @param id
     * @param amount
     * @throws CustomException
     */
    private void isOverUserDailyWithdrawalLimit(Long id, BigDecimal amount) throws CustomException {

        BigDecimal sumTodayAmount = fiatWithdrawalService.getSumToday(id);
        // 今回の出金額+今日既に登録した出金額の合計 > 利用者ごとの合計一日出金上限額
        if (amount.add(sumTodayAmount).compareTo(new BigDecimal(userDailyWithdrawalLimit)) > 0) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_AMOUNT_OUT_OF_RANGE_USER_TODAY);
        }
    }

    @PostMapping("/otpauth")
    public ResponseEntity<MfaTypeData> postOtpauth(
            @AuthenticationPrincipal UserPrincipal user,
            @Valid @RequestBody FiatWithdrawalOtpauthPostForm form)
            throws Exception {
        validatePost(user.getId(), form);
        return mfaManager.responseMfaTypeData(user.getId(), user.getEmail());
    }

    @PostMapping
    public ResponseEntity<FiatWithdrawal> post(
            @AuthenticationPrincipal UserPrincipal user,
            @Valid @RequestBody FiatWithdrawalPostForm form)
            throws Exception {
        mfaManager.authenticate(user.getId(), user.getEmail(), form.getMfaCode());
        validatePost(user.getId(), form);

        FiatWithdrawal fiatWithdrawal = new FiatWithdrawal();
        fiatWithdrawal.setUserId(user.getId());
        fiatWithdrawal.setBankAccountId(form.getBankAccountId());
        fiatWithdrawal.setAmount(form.getAmount());
        fiatWithdrawal.setFee(getFee(form.getAmount().intValue()));
        fiatWithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.APPROVING);
        fiatWithdrawal.setUpdatedBy("USER");
        FiatWithdrawal fiatWithdrawalRes = fiatWithdrawalService.request(fiatWithdrawal);
        // 出金申請受付のお知らせを送信
        MailNoreply mailNoreply = mailNoreplyService.findOne(MailNoreplyType.FIAT_WITHDRAWAL_APPLY);
        boolean send =
                sesManager.send(
                        mailNoreply.getFromAddress(),
                        user.getEmail(),
                        mailNoreply.getTitle(),
                        mailNoreply.getContents());
        if (!send) {
            log.info("register user invoke the email service Exception");
            throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
        }
        return ResponseEntity.ok(fiatWithdrawalRes);
    }

    // 手数料
    private BigDecimal getFee(Integer amount) {
        List<CurrencyConfig> currencyConfigList =
                currencyConfigService.findAllByCondition(TradeType.INVEST, Currency.JPY, true);
        Integer resultFee = 0;
        if (currencyConfigList != null && currencyConfigList.size() > 0) {
            resultFee = currencyConfigList.get(0).getWithdrawalFee().intValue();
        }

        return BigDecimal.valueOf(resultFee);
    }
}
