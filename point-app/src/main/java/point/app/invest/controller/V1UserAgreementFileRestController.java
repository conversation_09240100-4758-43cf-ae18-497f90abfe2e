package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Hidden;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.common.entity.UserAgreementFile;
import point.common.service.UserAgreementFileService;

@Hidden
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/user-agreement-file")
@Timed
public class V1UserAgreementFileRestController {

    private final UserAgreementFileService userAgreementFileService;

    @GetMapping
    public ResponseEntity<List<UserAgreementFile>> get() {
        return ResponseEntity.ok(userAgreementFileService.findAll());
    }
}
