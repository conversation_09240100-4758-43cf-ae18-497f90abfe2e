package point.app.invest.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RecaptchaResponse implements Serializable {

    private static final long serialVersionUID = 150196569704075377L;

    @Getter @Setter private boolean success;

    @Getter
    @Setter
    @JsonProperty(value = "challenge_ts")
    private String challengeTs;

    @Getter @Setter private String hostname;

    @Getter
    @Setter
    @JsonProperty(value = "error-codes")
    private String[] errorCodes;
}
