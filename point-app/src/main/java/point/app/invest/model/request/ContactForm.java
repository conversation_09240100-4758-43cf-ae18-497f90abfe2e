package point.app.invest.model.request;

import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import point.common.model.request.EmailForm;

@Getter
@Setter
public class ContactForm extends EmailForm implements IRecaptchaTokenForm {

    @NotNull private String name;

    @NotNull private String subject;

    @NotNull private String content;

    @NotNull private String recaptchaToken;
}
