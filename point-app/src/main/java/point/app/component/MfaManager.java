package point.app.component;

import com.warrenstrange.googleauth.GoogleAuthenticator;
import com.warrenstrange.googleauth.GoogleAuthenticatorKey;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import point.common.component.RedisManager;
import point.common.component.SesManager;
import point.common.component.SnsManager;
import point.common.config.SpringConfig;
import point.common.constant.Country;
import point.common.constant.ErrorCode;
import point.common.constant.LineFeed;
import point.common.constant.MailNoreplyType;
import point.common.constant.MfaType;
import point.common.entity.MailNoreply;
import point.common.entity.User;
import point.common.entity.UserAuthority;
import point.common.entity.UserInfo;
import point.common.entity.UserMfa;
import point.common.exception.CustomException;
import point.common.model.response.MfaTypeData;
import point.common.model.response.SNSResponse;
import point.common.service.*;
import point.common.util.FormatUtil;
import point.common.util.RandomUtil;
import point.common.util.StringUtil;

@Component
@RequiredArgsConstructor
public class MfaManager {

    private static final long MFA_CODE_EXPIRED_MINUTES = 5;

    private static final int MAX_AUTHENTICATION_TRIALS = 4;

    private static String getMfaCodeKey(String email) {
        return "mfa:code:" + email;
    }

    private static String getAuthenticationTrialsKey(String email) {
        return "mfa:authenticationTrials:" + email;
    }

    private static String createMfaCode() {
        return FormatUtil.formatZeroPadding(6, RandomUtil.nextInt(999999));
    }

    private final CredentialRepository credentialRepository;

    private final MailNoreplyService mailNoreplyService;

    private final RedisManager redisManager;

    private final SesManager sesManager;

    private final SnsManager snsManager;

    private final SpringConfig springConfig;

    private final UserAuthorityService userAuthorityService;

    private final UserInfoService userInfoService;

    private final UserMfaService userMfaService;

    private final UserService userService;

    private final SNSService snsService;

    private final SMSAttemptService smsAttemptService;

    @Bean
    public GoogleAuthenticator googleAuthenticator() {
        GoogleAuthenticator googleAuthenticator = new GoogleAuthenticator();
        googleAuthenticator.setCredentialRepository(credentialRepository);
        return googleAuthenticator;
    }

    private long getAuthenticationTrialsExpiredMinutes() {
        if (springConfig.isPrd()) {
            return 60;
        } else {
            return 1;
        }
    }

    public String getOtpauthUri(User user) throws Exception {
        GoogleAuthenticatorKey googleAuthenticatorKey =
                googleAuthenticator().createCredentials(user.getEmail());
        return StringUtil.createOtpauthUri(
                springConfig.getDomain(), user.getEmail(), googleAuthenticatorKey.getKey());
    }

    public void setToServer(String email, MfaType mfaType) throws Exception {
        if (overMaxAuthenticationTrials(email)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_CODE_SENDING_FREQUENCY_LIMIT);
        }

        User user = userService.findByEmail(email);
        String mfaCode = createMfaCode();
        MailNoreplyType mailNoreplyType =
                MfaType.SMS.equals(mfaType) ? MailNoreplyType.SMS_CODE : MailNoreplyType.MFA_CODE;
        MailNoreply mailNoreply = mailNoreplyService.findOne(mailNoreplyType);

        switch (mfaType) {
            case GOOGLE:
                break;
            case SMS:
                String phoneNubmer;
                Country country;
                UserAuthority userAuthority = userAuthorityService.findByUserId(user.getId());

                if (userAuthority.isPersonal()) {
                    UserInfo userInfo = userInfoService.findOne(user.getUserInfoId());

                    if (userInfo == null) {
                        throw new CustomException(ErrorCode.REQUEST_ERROR_USER_INFO_IS_NULL);
                    }

                    phoneNubmer = userInfo.getPhoneNumber();
                    country = userInfo.getCountry();
                } else {
                    throw new CustomException(ErrorCode.REQUEST_ERROR_NOTSETUP_PHONE_NUMBER);
                }

                redisManager.set(getMfaCodeKey(email), mfaCode, MFA_CODE_EXPIRED_MINUTES);
                Optional<SNSResponse> snsResponse =
                        snsService.postSenSNS(
                                mailNoreply.getContents(
                                        user.getAntiPhishingCode(), mfaCode, LineFeed.TEXT),
                                phoneNubmer,
                                mfaCode);
                break;
            default:
                redisManager.set(getMfaCodeKey(email), mfaCode, MFA_CODE_EXPIRED_MINUTES);
                sesManager.send(
                        mailNoreply.getFromAddress(),
                        email,
                        mailNoreply.getTitle(),
                        mailNoreply.getContents(
                                user != null ? user.getAntiPhishingCode() : null,
                                mfaCode,
                                LineFeed.HTML));
                break;
        }
    }

    private void authenticateByGoogle(Long userId, String email, String mfaCode) throws Exception {
        try {
            if (!NumberUtils.isCreatable(mfaCode)) {
                throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_MFA_CODE);
            }

            if (!googleAuthenticator().authorizeUser(email, Integer.valueOf(mfaCode))) {
                throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_MFA_CODE);
            }

            UserMfa userMfa = userMfaService.findByCondition(userId, MfaType.GOOGLE);

            if (userMfa == null) {
                throw new CustomException(ErrorCode.REQUEST_ERROR_USER_MFA_NOT_FOUND);
            }

            if (!userMfa.isAuthenticated()) {
                userMfa.setAuthenticated(true);
                userMfaService.save(userMfa);
            }

            deleteAuthenticationTrials(email);
        } catch (Exception e) {
            countAuthenticationTrials(email);
            throw e;
        }
    }

    public void authenticateBySmsOrEmail(String email, String mfaCode) throws Exception {
        try {
            String mfaCodekey = getMfaCodeKey(email);
            String mfaCodeAtServer = redisManager.get(mfaCodekey);

            if (StringUtils.isEmpty(mfaCodeAtServer)) {
                throw new CustomException(ErrorCode.REQUEST_ERROR_MFA_CODE_IS_EXPIRED);
            }

            if (!mfaCodeAtServer.equals(mfaCode)) {
                throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_MFA_CODE);
            }

            deleteAuthenticationTrials(email);
            deleteMfaCode(email);
        } catch (Exception e) {
            countAuthenticationTrials(email);
            throw e;
        }
    }

    public void authenticate(Long userId, String email, MfaType mfaType, String mfaCode)
            throws Exception {
        switch (mfaType) {
            case GOOGLE -> authenticateByGoogle(userId, email, mfaCode);
            default -> authenticateBySmsOrEmail(email, mfaCode);
        }
    }

    public void authenticate(Long userId, String email, String mfaCode) throws Exception {
        authenticate(userId, email, userMfaService.getMfaType(userId), mfaCode);
    }

    public ResponseEntity<MfaTypeData> responseMfaTypeData(String email, MfaType mfaType)
            throws Exception {
        setToServer(email, mfaType);
        if (springConfig.isNotPrd() && MfaType.SMS.equals(mfaType)) {
            String snsCode = redisManager.get(getMfaCodeKey(email));
            return ResponseEntity.ok(
                    MfaTypeData.builder().mfaType(mfaType).snsCode("SMS: " + snsCode).build());
        } else if (springConfig.isNotPrd()) {
            String snsCode = redisManager.get(getMfaCodeKey(email));
            return ResponseEntity.ok(
                    MfaTypeData.builder().mfaType(mfaType).snsCode("email: " + snsCode).build());
        }
        return ResponseEntity.ok(MfaTypeData.builder().mfaType(mfaType).build());
    }

    public ResponseEntity<MfaTypeData> responseMfaTypeData(Long userId, String email)
            throws Exception {
        return responseMfaTypeData(email, userMfaService.getMfaType(userId));
    }

    private boolean overMaxAuthenticationTrials(String email) {
        String key = getAuthenticationTrialsKey(email);
        Integer value = redisManager.getInteger(key);
        return value != null && value.compareTo(MAX_AUTHENTICATION_TRIALS) == 1;
    }

    private void countAuthenticationTrials(String email) {
        String key = getAuthenticationTrialsKey(email);
        Integer value = redisManager.getInteger(key);

        if (value == null) {
            value = 0;
        }

        redisManager.setInteger(key, value + 1, getAuthenticationTrialsExpiredMinutes());
    }

    private void deleteAuthenticationTrials(String email) {
        redisManager.delete(getAuthenticationTrialsKey(email));
    }

    private void deleteMfaCode(String email) {
        redisManager.delete(getMfaCodeKey(email));
    }
}
