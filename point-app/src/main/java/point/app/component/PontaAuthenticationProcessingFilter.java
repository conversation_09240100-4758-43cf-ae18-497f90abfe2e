package point.app.component;

import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.security.web.authentication.session.ChangeSessionIdAuthenticationStrategy;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import point.common.constant.CommonConstants;
import point.common.constant.ErrorCode;
import point.common.exception.PontaUserCancelConsentAuthenticationException;

/** Receive operate user login(callback) request */
public class PontaAuthenticationProcessingFilter extends AbstractAuthenticationProcessingFilter {

    protected AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource =
            new WebAuthenticationDetailsSource();

    public PontaAuthenticationProcessingFilter(AuthenticationManager authenticationManager) {
        super(new AntPathRequestMatcher("/app/v1/ponta-callback/oauth", "GET"));
        setAuthenticationManager(authenticationManager);
        setSessionAuthenticationStrategy(new ChangeSessionIdAuthenticationStrategy());
    }

    @Override
    public Authentication attemptAuthentication(
            HttpServletRequest request, HttpServletResponse response)
            throws AuthenticationException, IOException, ServletException {

        Enumeration<String> paramNames = request.getParameterNames();
        Map<String, String> callbackParams = new HashMap<>();

        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String paramValue = request.getParameter(paramName);
            callbackParams.put(paramName, paramValue);
        }

        if (CollectionUtils.isEmpty(callbackParams)) {
            throw new BadCredentialsException(
                    String.valueOf(ErrorCode.PONTA_AUTHENTICATION_ERROR_INVALID_PARAMS.getCode()));
        }

        String errorMessage = callbackParams.get(CommonConstants.PONTA_ERROR_CODE);
        if (StringUtils.hasLength(errorMessage)) {
            if (CommonConstants.PONTA_ERROR_USER_CANCEL_CONSENT_CODE.equals(errorMessage)) {
                throw new PontaUserCancelConsentAuthenticationException();
            } else {
                throw new BadCredentialsException(errorMessage);
            }
        }

        String secretCode = callbackParams.get(CommonConstants.PONTA_SECRET_CODE);
        String partnerNumber = callbackParams.get(CommonConstants.PONTA_PARTNER_NUMBER);
        if (!StringUtils.hasLength(secretCode) && !StringUtils.hasLength(partnerNumber)) {
            throw new BadCredentialsException(
                    String.valueOf(ErrorCode.PONTA_AUTHENTICATION_ERROR_INVALID_PARAMS.getCode()));
        }

        UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken =
                new UsernamePasswordAuthenticationToken(callbackParams, callbackParams);
        usernamePasswordAuthenticationToken.setDetails(
                authenticationDetailsSource.buildDetails(request));
        return getAuthenticationManager().authenticate(usernamePasswordAuthenticationToken);
    }
}
