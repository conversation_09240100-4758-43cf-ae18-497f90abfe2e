package point.app.component;

import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Service;
import point.app.config.RecaptchaConfig;
import point.app.invest.model.response.RecaptchaResponse;
import point.common.constant.ErrorCode;
import point.common.exception.CustomException;
import point.common.http.HttpManager;

@RequiredArgsConstructor
@Service
public class RecaptchaManager {

    private final HttpManager<RecaptchaResponse> httpManager;

    private final RecaptchaConfig recaptchaConfig;

    public void verify(String recaptchaToken) throws Exception {
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        nameValuePairs.add(new BasicNameValuePair("secret", recaptchaConfig.getSecretKey()));
        nameValuePairs.add(new BasicNameValuePair("response", recaptchaToken));
        RecaptchaResponse recaptchaResponse =
                httpManager.doPostForm(
                        recaptchaConfig.getUrl(),
                        nameValuePairs,
                        null,
                        null,
                        RecaptchaResponse.class);

        if (!recaptchaResponse.isSuccess()) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_RECAPTCHA_TOKEN);
        }
    }
}
