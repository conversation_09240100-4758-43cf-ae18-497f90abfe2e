package point.app.component;

import java.io.IOException;
import java.util.Map;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.web.util.UriComponentsBuilder;
import point.app.component.jwt.JwtManager;
import point.app.component.model.UserPrincipal;
import point.common.component.RedisManager;
import point.common.config.PontaConfig;
import point.common.constant.*;
import point.common.entity.UserLoginInfo;
import point.common.ponta.PontaRedirectUtil;
import point.common.service.ChoicePowerSyncService;
import point.common.service.OneTimePasswordService;
import point.common.service.UserLoginInfoService;

/** Operate user login success handler */
@Slf4j
@RequiredArgsConstructor
public class PontaAuthenticationSuccessHandlerImpl implements AuthenticationSuccessHandler {

    private final PontaConfig pontaConfig;
    private final IpAddressResolver ipAddressResolver;
    private final UserLoginInfoService userLoginInfoService;
    private final JwtManager jwtManager;
    private final RedisManager redisManager;
    private final Integer cacheTokenTtl;
    private final Integer otpTtl;
    private final OneTimePasswordService oneTimePasswordService;
    private final ChoicePowerSyncService choicePowerSyncService;

    @Override
    public void onAuthenticationSuccess(
            HttpServletRequest request, HttpServletResponse response, Authentication authentication)
            throws IOException, ServletException {
        if (response.isCommitted()) {
            log.info(getClass().getName(), "Response has already been committed.");
            return;
        }

        UserPrincipal principal =
                (UserPrincipal)
                        SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        try {
            String ipAddress = ipAddressResolver.getRemoteAddr(request);
            UserLoginInfo userLoginInfo = new UserLoginInfo();
            userLoginInfo.setUserId(principal.getId());
            userLoginInfo.setIpAddress(ipAddress);
            userLoginInfoService.save(userLoginInfo);

            Map<String, String> tokenPair =
                    jwtManager.createTokenPair(principal.getEmail(), () -> principal);
            String key = CommonConstants.REDIS_USER_TOCKN_PREFIX + principal.getId();
            redisManager.hset(key, tokenPair, cacheTokenTtl);
            clearAuthenticationAttributes(request);

            String otp =
                    oneTimePasswordService.generateOneTimePassword(
                            CommonConstants.PONTA_LOGIN_OTP_PREFIX + principal.getId(), otpTtl);

            String redirectUrl =
                    UriComponentsBuilder.fromUriString(
                                    PontaRedirectUtil.getSuccessUri(
                                            pontaConfig, principal.getClient()))
                            .queryParam(CommonConstants.UID, principal.getId())
                            .queryParam(CommonConstants.OTP, otp)
                            .queryParam(
                                    CommonConstants.SOURCE,
                                    principal.getSource()) // Used to decide whether to display the
                            // `operate` or `invest`
                            // screen
                            .build()
                            .toUriString();

            // SET POWER WHEN OPERATION LOGIN
            choicePowerSyncService.savePowerByRule(
                    userLoginInfo.getUserId(), ChoiceActivityRuleEnum.LOGIN_OPERATION_COURSE, null);
            response.sendRedirect(redirectUrl);
        } catch (Exception e) {
            log.error(
                    "Failed to handle operation user login success, errorMessage: {}",
                    e.getMessage(),
                    e);
            response.sendRedirect(
                    PontaRedirectUtil.getFailureUriWithError(
                            pontaConfig,
                            principal.getClient(),
                            ErrorCode.COMMON_ERROR_SYSTEM_ERROR));
        }
    }

    /**
     * Removes temporary authentication-related data which may have been stored in the session
     * during the authentication process.
     */
    private void clearAuthenticationAttributes(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return;
        }
        session.removeAttribute(WebAttributes.AUTHENTICATION_EXCEPTION);
    }
}
