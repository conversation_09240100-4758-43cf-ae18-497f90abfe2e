package point.app.config;

import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/*@RequiredArgsConstructor
@Configuration*/
public class WebMvcConfigurerImpl implements WebMvcConfigurer {

    /* private final HandlerInterceptor handlerInterceptor;*/

    /* @Override
    public void addInterceptors(InterceptorRegistry registry) {
      registry.addInterceptor(handlerInterceptor).excludePathPatterns("/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**");
    }
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
      registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");
      registry.addResourceHandler("swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
      registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
    }*/
}
