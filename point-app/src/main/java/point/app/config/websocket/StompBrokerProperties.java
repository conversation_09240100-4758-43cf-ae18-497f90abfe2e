package point.app.config.websocket;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "exchange-websocket.stomp-broker")
public class StompBrokerProperties {
    private boolean enabled;
    private String host;
    private int port;
    private String clientLogin;
    private String clientPasscode;
    private int singleConsumerLockExpiredInSeconds;
    private long heartbeatClientSendIntervalInMilliseconds;
    private long heartbeatServerSendIntervalInMilliseconds;
}
