package point.app.config.websocket;

import static org.springframework.messaging.simp.stomp.StompCommand.*;

import java.security.Principal;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import point.app.component.jwt.JwtAuthenticationProvider;
import point.app.component.jwt.JwtAuthenticationToken;
import point.app.component.model.UserPrincipal;
import point.common.component.CustomRedisTemplate;
import point.common.config.SpringConfig;

@Component
@Slf4j
@RequiredArgsConstructor
public class WebSocketAuthInterceptor implements ChannelInterceptor {
    private static final List<StompCommand> ALLOWED_COMMAND_LIST =
            List.of(CONNECT, DISCONNECT, MESSAGE, SUBSCRIBE, UNSUBSCRIBE);
    private static final List<String> ALLOWED_TOPIC_LIST =
            List.of(
                    "/user/queue/asset",
                    "/topic/currency-pair",
                    "/topic/pos/currency-pair",
                    "/topic/pos/candlestick.symbol.*",
                    "/topic/pos/price.symbol.*",
                    "/user/queue/pos/trade.symbol.*",
                    "/user/queue/errors");
    private static final List<String> AUTH_DESTINATION_LIST = List.of("/user/queue/.*");

    private static final List<String> NOAUTH_DESTINATION_LIST = List.of("/user/queue/errors");
    private final boolean isValidationEnabled =
            true; // set to false to disable validation for debugging
    private final SpringConfig springConfig;
    private final JwtAuthenticationProvider jwtAuthenticationProvider;
    private final CustomRedisTemplate<String> redisTemplate;
    private final DestinationConverter destinationConverter;

    @Value("${exchange-websocket.subscription-limit-per-session:100}")
    public int subscriptionLimit;

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {

        StompHeaderAccessor accessor =
                MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
        assert accessor != null;
        if (accessor.isHeartbeat()) {
            return message;
        }

        StompCommand command = accessor.getCommand();
        log.debug("command {}", command);
        String destination = accessor.getDestination();
        log.debug("destination {}", destination);
        String sessionId = accessor.getSessionId();
        log.debug("sessionId {}", destination);
        String subscriptionId = accessor.getSubscriptionId();
        log.debug("subscriptionId {}", subscriptionId);

        // ensureCommandAllowed
        if (isValidationEnabled && !isCommandAllowed(command)) {
            throw new WebSocketAuthenticationException("Command is not allowed. " + command);
        }
        switch (Objects.requireNonNull(accessor.getCommand())) {
            case CONNECT:
                // Set User
                Principal principal = validateAndExtractUserDetails(accessor);
                if (principal != null) {
                    accessor.setUser(principal);
                }
                break;
            case SUBSCRIBE:
                // ensureAuthenticated
                if (isValidationEnabled
                        && accessor.getUser() == null
                        && isAuthRequired(destination)) {
                    throw new WebSocketAuthenticationException(
                            "Cannot subscribe due to Unauthorized!");
                }
                // ensureTopicAllowed
                if (isValidationEnabled && isTopicNotAllowed(destination)) {
                    throw new WebSocketAuthenticationException(
                            "Topic is not allowed to subscribe, topic is " + destination);
                }
                // ensureNotDuplicate
                if (isValidationEnabled && addAndCheckIfInSessionCache(sessionId, subscriptionId)) {
                    throw new WebSocketAuthenticationException(
                            "Topic is already to subscribed, topic is " + destination);
                }
                // ensureNotTooMany
                if (isValidationEnabled && checkIfTooManyInSessionCache(sessionId)) {
                    throw new WebSocketAuthenticationException(
                            "Too many subscription on one session, topic is " + destination);
                }
                accessor.setDestination(destinationConverter.determineRealDestination(destination));
                break;
            case UNSUBSCRIBE:
                cleanUpSessionCache(sessionId, subscriptionId);
                break;
            case DISCONNECT:
                cleanUpSessionCache(sessionId, null);
                break;
            default:
                log.debug(" COMMAND IS " + accessor.getCommand());
        }
        return message;
    }

    private boolean isTopicNotAllowed(String destination) {
        return ALLOWED_TOPIC_LIST.stream()
                .noneMatch(pattern -> Pattern.matches(pattern, destination));
    }

    private boolean isCommandAllowed(StompCommand command) {
        return ALLOWED_COMMAND_LIST.contains(command);
    }

    private boolean isAuthRequired(String destination) {
        if (NOAUTH_DESTINATION_LIST.stream()
                .anyMatch(pattern -> Pattern.matches(pattern, destination))) {
            return false;
        }
        return AUTH_DESTINATION_LIST.stream()
                .anyMatch(pattern -> Pattern.matches(pattern, destination));
    }

    private Principal validateAndExtractUserDetails(StompHeaderAccessor accessor) {
        // for dev only, you can pass username={user-id} in the message header
        if (springConfig.isLocal() || springConfig.isDev()) {
            String authorizationHeader = accessor.getFirstNativeHeader("username");
            if (authorizationHeader != null) {
                return new WebSocketUser(authorizationHeader);
            }
        }

        // for prod, you need pass auth-token={auth token} in the message header
        String authorizationHeader = accessor.getFirstNativeHeader("auth-token");
        if (authorizationHeader == null) {
            return null;
        }
        Authentication authentication =
                jwtAuthenticationProvider.authenticate(
                        new JwtAuthenticationToken(
                                authorizationHeader.replaceAll("Bearer", "").trim()));
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        return new WebSocketUser(userPrincipal.getId().toString());
    }

    private boolean addAndCheckIfInSessionCache(String sessionId, String subscriptionId) {
        long expiredMinutes =
                24 * 60; // 1day, the key should be removed when disconnected, there to expire
        // the key just in case
        Long affectCount =
                redisTemplate.sadd(getSessionCacheKey(sessionId), subscriptionId, expiredMinutes);
        return affectCount == 0;
    }

    private boolean checkIfTooManyInSessionCache(String sessionId) {
        return redisTemplate.scard(getSessionCacheKey(sessionId))
                > subscriptionLimit; // FIXME configurable
    }

    private void cleanUpSessionCache(String sessionId, String destination) {
        if (destination == null) {
            redisTemplate.delete(getSessionCacheKey(sessionId));
        } else {
            redisTemplate.sremove(getSessionCacheKey(sessionId), destination);
        }
    }

    private String getSessionCacheKey(String sessionId) {
        return "websocket:session:" + sessionId + ":topic";
    }

    public static class WebSocketUser implements Principal {
        private final String name;

        public WebSocketUser(String name) {
            this.name = name;
        }

        @Override
        public String getName() {
            return name;
        }
    }
}
