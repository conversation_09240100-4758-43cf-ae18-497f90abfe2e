package point.app.config.websocket;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.session.Session;
import org.springframework.session.web.socket.config.annotation.AbstractSessionWebSocketMessageBrokerConfigurer;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import point.app.config.PointAppConfig;
import point.common.config.SpringConfig;

@Configuration
@EnableWebSocketMessageBroker
@RequiredArgsConstructor
// Also, note that, when you use Spring Security’s authorization for messages, at present,
// you need to ensure that the authentication ChannelInterceptor config is ordered ahead of Spring
// Security’s.
// This is best done by declaring the custom interceptor in its own implementation of
// WebSocketMessageBrokerConfigurer
// that is marked with @Order(Ordered.HIGHEST_PRECEDENCE + 99)
@Order(Ordered.HIGHEST_PRECEDENCE + 99)
@Slf4j
public class WebSocketConfig extends AbstractSessionWebSocketMessageBrokerConfigurer<Session> {
    private final PointAppConfig exchangeAppConfig;
    private final SpringConfig springConfig;
    private final StompBrokerProperties stompBrokerProperties;
    private final WebSocketAuthInterceptor webSocketAuthInterceptor;

    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        super.configureClientInboundChannel(registration);

        registration.interceptors(webSocketAuthInterceptor);
    }

    private TaskScheduler messageBrokerTaskScheduler;

    @Autowired
    public void setMessageBrokerTaskScheduler(@Lazy TaskScheduler taskScheduler) {
        this.messageBrokerTaskScheduler = taskScheduler;
    }

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        if (stompBrokerProperties.isEnabled()) {
            // use external broker
            config.setPathMatcher(new AntPathMatcher("."))
                    .setApplicationDestinationPrefixes("/app")
                    .enableStompBrokerRelay("/queue/", "/topic/", "/exchange")
                    .setRelayHost(stompBrokerProperties.getHost())
                    .setRelayPort(stompBrokerProperties.getPort())
                    .setClientLogin(stompBrokerProperties.getClientLogin())
                    .setClientPasscode(stompBrokerProperties.getClientPasscode());
        } else {
            // use simp broker
            config.setPathMatcher(new AntPathMatcher("."))
                    .setApplicationDestinationPrefixes("/app")
                    .enableSimpleBroker("/queue/", "/topic/")
                    .setHeartbeatValue(
                            new long[] {
                                stompBrokerProperties
                                        .getHeartbeatClientSendIntervalInMilliseconds(),
                                stompBrokerProperties.getHeartbeatServerSendIntervalInMilliseconds()
                            })
                    .setTaskScheduler(this.messageBrokerTaskScheduler);
        }
    }

    @Override
    public void configureStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/websocket")
                .setAllowedOrigins(
                        springConfig.isLocal() || springConfig.isDev()
                                ? CorsConfiguration.ALL
                                : exchangeAppConfig.getAllowedOrigin());
    }
}
