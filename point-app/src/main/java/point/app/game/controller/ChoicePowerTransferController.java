package point.app.game.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.common.constant.ChoiceActivityRuleEnum;
import point.common.constant.ViewVariables;
import point.common.entity.*;
import point.common.exception.GameException;
import point.common.model.request.ChoiceVoteForm;
import point.common.model.request.ChoiceVoteModifyForm;
import point.common.model.response.*;
import point.common.service.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/game/choice")
public class ChoicePowerTransferController {

    private final ChoicePowerTransferService choicePowerTransferService;
    private final ChoiceActivityRuleService choiceActivityRuleService;

    public final ChoiceVoteInfoService choiceVoteInfoService;

    @GetMapping("/vote-history")
    @Operation(
            summary = "get vote history",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "200",
                        description = "Success"),
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "400",
                        description = "Bad Request")
            })
    public ResponseEntity<PageData<List<ChoicePowerTransferResponse>>>
            getChoicePowerTransferForCustomer(
                    @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user,
                    @RequestParam(value = "dateFrom", required = false) Long dateFrom,
                    @RequestParam(value = "dateTo", required = false) Long dateTo,
                    @RequestParam(
                                    value = "number",
                                    defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                            Integer number,
                    @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                            Integer size) {
        List<ChoicePowerTransfer> choicePowerTransferList =
                choicePowerTransferService.getChoicePowerTransferByUserId(user.getUserIds());

        List<ChoicePowerTransferResponse> choicePowerTransferResponseList = new ArrayList<>();

        for (ChoicePowerTransfer choicePowerTransfer : choicePowerTransferList) {
            ChoicePowerTransferResponse response = new ChoicePowerTransferResponse();
            response.setId(choicePowerTransfer.getId());
            ChoiceActivityRule choiceActivityRule =
                    choiceActivityRuleService.findOne(
                            choicePowerTransfer.getChoiceActivityRuleId());
            if (choiceActivityRule != null) {
                response.setActivityName(choiceActivityRule.getActivityName());
            }
            // add activityname チョイス消費
            if (ChoiceActivityRuleEnum.CHOICE_CONSUMPTION
                    .getId()
                    .equals(choicePowerTransfer.getChoiceActivityRuleId())) {
                response.setActivityName(ChoiceActivityRuleEnum.CHOICE_CONSUMPTION.getName());
            }
            response.setCreatedAt(choicePowerTransfer.getCreatedAt());
            response.setAmount(choicePowerTransfer.getAmount());
            response.setTransferType(choicePowerTransfer.getTransferType());

            choicePowerTransferResponseList.add(response);
        }

        Long count = Long.valueOf(choicePowerTransferResponseList.size());
        return ResponseEntity.ok(
                createPageData(choicePowerTransferResponseList, count, number, size));
    }

    private PageData<List<ChoicePowerTransferResponse>> createPageData(
            List<ChoicePowerTransferResponse> content, Long count, Integer number, Integer size) {
        List<List<ChoicePowerTransferResponse>> pageContents = new ArrayList<>();

        int start = number * size;
        int end = Math.min(start + size, content.size());

        if (start < content.size()) {
            pageContents.add(content.subList(start, end));
        } else {
            pageContents.add(new ArrayList<>());
        }

        return new PageData<>(number, size, count, pageContents);
    }

    @GetMapping("/info")
    @Operation(
            summary = "choice info",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "200",
                        description = "Success"),
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "400",
                        description = "Bad Request")
            })
    public ResponseEntity<GlobalApiResponse<ChoiceInfoData>> getChoiceInfo(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user) {
        ChoiceInfoData choiceInfo = choiceVoteInfoService.getChoiceInfo(user.getUserIds());
        // add investUserId & operateUserId to choiceInfo
        choiceInfo.setInvestUserId(
                Objects.nonNull(user.getUserWrapper().getUser())
                        ? user.getUserWrapper().getUser().getId()
                        : null);
        choiceInfo.setOperateUserId(
                Objects.nonNull(user.getUserWrapper().getPointUser())
                        ? user.getUserWrapper().getPointUser().getId()
                        : null);
        return ResponseEntity.ok().body(new GlobalApiResponse<>(200, choiceInfo));
    }

    @PutMapping("/vote")
    @Operation(
            summary = "choice modify vote power",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "200",
                        description = "Success"),
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "400",
                        description = "Bad Request")
            })
    public ResponseEntity<GlobalApiResponse<ChoiceVoteData>> modifyVotePower(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user,
            @RequestBody @Valid ChoiceVoteModifyForm choiceVoteForm)
            throws Exception {
        try {
            return choiceVoteInfoService.modifyVotePower(user.getUserIds(), choiceVoteForm);
        } catch (GameException e) {
            return ResponseEntity.ok().body(GlobalApiResponse.badRequest(e.getErrorCode()));
        }
    }

    @PostMapping("/vote")
    @Operation(
            summary = "choice vote power",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "200",
                        description = "Success"),
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "400",
                        description = "Bad Request")
            })
    public ResponseEntity<GlobalApiResponse<ChoiceVoteData>> choiceVote(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user,
            @RequestBody @Valid ChoiceVoteForm choiceVoteForm)
            throws Exception {
        try {
            return choiceVoteInfoService.choiceVote(user.getUserIds(), choiceVoteForm);
        } catch (GameException e) {
            return ResponseEntity.ok().body(GlobalApiResponse.badRequest(e.getErrorCode()));
        }
    }
}
