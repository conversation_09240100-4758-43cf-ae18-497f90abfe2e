package point.app.game.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;
import point.common.config.PontaConfig;
import point.common.constant.CommonConstants;
import point.common.constant.ErrorCode;
import point.common.entity.PointPartner;
import point.common.service.PointPartnerService;

/**
 * The home page for the game.
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/game")
public class HomeController {

    private final PointPartnerService pointPartnerService;
    private final PontaConfig pontaConfig;

    /**
     * Redirects to the home page of the game based on the partner number.
     *
     * @param partnerNumber the partner number provided by the backseat
     * @param response the HTTP response used for redirection
     * @throws IOException if an I/O error occurs during redirection
     */
    @GetMapping("/home/<USER>")
    @Operation(summary = "Enter to game screen")
    public void index(@PathVariable String partnerNumber, HttpServletResponse response)
            throws IOException {
        PointPartner pointPartner =
                pointPartnerService.findByPartnerNumberWithoutStatusLimit(partnerNumber);

        if (Objects.isNull(pointPartner)) {
            redirectWithError(response, ErrorCode.PARTNER_INFO_NOT_FOUND);
            return;
        }

        if (pointPartner.isInactive()) {
            redirectWithError(response, ErrorCode.PONTA_PARTNER_STATUS_INACTIVE);
            return;
        }

        if (pointPartner.isExpired()) {
            redirectWithError(response, ErrorCode.PONTA_PARTNER_STATUS_EXPIRED);
            return;
        }

        if (pointPartner.isActive() && pointPartner.isValidate()) {
            redirectWithPartnerNumber(response, partnerNumber);
            return;
        }

        redirectWithError(response, ErrorCode.PARTNER_INFO_NOT_FOUND);
    }

    /**
     * Redirects to the game home URL with an error message.
     *
     * @param response the HTTP response used for redirection
     * @param errorCode the errorCode with message to be included in the query parameters
     * @throws IOException if an I/O error occurs during redirection
     */
    private void redirectWithError(HttpServletResponse response, ErrorCode errorCode)
            throws IOException {
        MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>(2);
        queryParams.add(CommonConstants.ERROR, String.valueOf(errorCode.getCode()));
        queryParams.add(
                CommonConstants.ERROR_MESSAGE,
                URLEncoder.encode(errorCode.getMessage(), StandardCharsets.UTF_8));
        response.sendRedirect(buildUri(queryParams));
    }

    /**
     * Redirects to the game home URL with the partner number.
     *
     * @param response the HTTP response used for redirection
     * @param partnerNumber the partner number to be included in the query parameters
     * @throws IOException if an I/O error occurs during redirection
     */
    private void redirectWithPartnerNumber(HttpServletResponse response, String partnerNumber)
            throws IOException {
        MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>(1);
        queryParams.add(CommonConstants.PONTA_PARTNER_NUMBER, partnerNumber);
        response.sendRedirect(buildUri(queryParams));
    }

    /**
     * Builds the URI for redirection with the given query parameters.
     *
     * @param queryParams the query parameters to be included in the URI
     * @return the built URI as a string
     */
    private String buildUri(MultiValueMap<String, String> queryParams) {
        return UriComponentsBuilder.fromUriString(pontaConfig.getGameHomeUrl())
                .queryParams(queryParams)
                .build()
                .toUriString();
    }
}
