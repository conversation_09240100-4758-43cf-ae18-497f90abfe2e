package point.app.game.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.common.entity.ChoiceActivityRule;
import point.common.model.response.CampaignListResponse;
import point.common.model.response.GlobalApiResponse;
import point.common.service.ChoiceActivityRuleService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/game")
public class GameCampaignController {

    private final ChoiceActivityRuleService choiceActivityRuleService;

    @GetMapping("/campaigns")
    @Operation(
            summary = "campaigns list",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @ApiResponse(responseCode = "200", description = "Success"),
                @ApiResponse(responseCode = "400", description = "Bad Request")
            })
    public ResponseEntity<GlobalApiResponse<List<CampaignListResponse>>> getCampaigns(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal userPrincipal) {
        List<ChoiceActivityRule> ruleList = choiceActivityRuleService.fingCampaignList();
        List<CampaignListResponse> result =
                ruleList.stream()
                        .map(
                                rule ->
                                        CampaignListResponse.builder()
                                                .id(rule.getId())
                                                .activityName(rule.getActivityName())
                                                .expiryDate(rule.getExpiryDate())
                                                .effectiveDate(rule.getEffectiveDate())
                                                .powerAmount(rule.getPowerAmount())
                                                .powerAmountRate(rule.getAmountRateDisplayValue())
                                                .status(rule.getStatus())
                                                .build())
                        .toList();
        return ResponseEntity.ok(new GlobalApiResponse<>(HttpStatus.OK.value(), result));
    }
}
