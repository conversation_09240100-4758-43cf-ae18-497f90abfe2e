package point.app.operate.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.common.constant.*;
import point.common.entity.*;
import point.common.model.request.RecordShareForm;
import point.common.model.request.TradeHistShareForm;
import point.common.model.response.GlobalApiResponse;
import point.common.model.response.PageData;
import point.common.model.response.PosTradeResponse;
import point.common.service.*;
import point.common.util.DateUnit;
import point.pos.entity.PosTrade;
import point.pos.service.PosTradeService;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/operate")
public class TradeHistoryOperateController {

    private final SymbolService symbolService;
    private final CurrencyPairConfigService currencyPairConfigService;
    private final PosTradeService posTradeService;

    private final SharedHistService sharedHistService;

    @GetMapping("/trade-history/page/history")
    @Operation(
            summary = "operate trade history",
            security = @SecurityRequirement(name = "x-auth"),
            parameters = {
                @Parameter(name = "symbolId", description = "Symbol ID", required = true),
                @Parameter(name = "id", description = "ID"),
                @Parameter(name = "idFrom", description = "ID from"),
                @Parameter(name = "idTo", description = "ID to"),
                @Parameter(name = "dateFrom", description = "Date from"),
                @Parameter(name = "dateTo", description = "Date to"),
                @Parameter(name = "orderSide", description = "Order side"),
                @Parameter(name = "orderType", description = "Order type"),
                @Parameter(name = "orderTypes", description = "List of order types"),
                @Parameter(name = "exceptOrderTypes", description = "List of except order types"),
                @Parameter(name = "number", description = "Page number", example = "1"),
                @Parameter(name = "size", description = "Page size", example = "10")
            },
            responses = {
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "200",
                        description = "Success"),
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "400",
                        description = "Bad Request")
            })
    public ResponseEntity<PageData<PosTradeResponse>> getPageHistory(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam(value = "symbolId") Long symbolId,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "idFrom", required = false) Long idFrom,
            @RequestParam(value = "idTo", required = false) Long idTo,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
            @RequestParam(value = "orderType", required = false) OrderType orderType,
            @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
            @RequestParam(value = "exceptOrderTypes", required = false)
                    List<OrderType> exceptOrderTypes,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {
        PageData<PosTrade> pg;
        PageData<PosTradeResponse> pgResust = new PageData<>(number, size, 0, null);
        if (size == 0) {
            return ResponseEntity.ok(pgResust);
        }

        if (symbolId == null || userPrincipal == null) {
            return ResponseEntity.ok(pgResust);
        }

        Symbol symbol = symbolService.findOne(symbolId);

        Long userId = userPrincipal.getId();
        if (symbol == null || userId == null) {
            return ResponseEntity.ok(pgResust);
        }

        // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
        List<CurrencyPairConfig> currencyPairConfigs =
                currencyPairConfigService.findAllByCondition(
                        TradeType.OPERATE, symbol.getCurrencyPair(), true);

        if (CollectionUtils.isEmpty(currencyPairConfigs)) {
            return ResponseEntity.ok(pgResust);
        }

        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

        // 取引履歴取得
        List<PosTrade> posTrades =
                posTradeService.findByCondition(
                        symbolId,
                        userId,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        null,
                        orderSide,
                        orderType,
                        orderTypes,
                        exceptOrderTypes,
                        0,
                        Integer.MAX_VALUE,
                        false);

        Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
        Date dateToDate = (dateTo == null) ? null : new Date(dateTo);

        List<PosTrade> posTradesHistory =
                posTradeService.findAllFromHistory(
                        symbol,
                        userId,
                        id,
                        idFrom,
                        idTo,
                        orderSide,
                        orderType,
                        orderTypes,
                        exceptOrderTypes,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        dateFromDate,
                        dateToDate);

        if (posTrades == null) {
            posTrades = new ArrayList<>();
        }
        if (posTradesHistory == null) {
            posTradesHistory = new ArrayList<>();
        }

        // merge & sort
        posTrades.addAll(posTradesHistory);
        // order by id desc
        posTrades.sort(
                (x, y) -> {
                    if (x == null || y == null || x.getId() == null || y.getId() == null) {
                        return 0;
                    }
                    return x.getId().compareTo(y.getId()) * -1;
                });

        // PageData作成
        //   posTrades + posTradesHistory合算後count
        Long count = (long) posTrades.size();
        pg = posTradeService.createPageData(posTrades, count, number, size);
        pgResust.setTotalElements(count);
        pg.getContent()
                .forEach(
                        trade -> {
                            BigDecimal income =
                                    trade.getIncome() != null
                                            ? trade.getIncome().setScale(2, RoundingMode.HALF_UP)
                                            : BigDecimal.ZERO;
                            pgResust.getContent()
                                    .add(
                                            PosTradeResponse.builder()
                                                    .createdAt(trade.getCreatedAt())
                                                    .symbolId(trade.getSymbolId())
                                                    .orderSide(trade.getOrderSide())
                                                    .orderType(trade.getOrderType())
                                                    .price(trade.getPrice())
                                                    .amount(trade.getAmount())
                                                    .assetAmount(trade.getAssetAmount())
                                                    .id(trade.getId())
                                                    .income(income)
                                                    .notes(trade.getNotes())
                                                    .treeLevel(
                                                            trade.getUserGrowthStageId().toString())
                                                    .build());
                        });
        return ResponseEntity.ok(pgResust);
    }

    /**
     * ファームで収穫した作物をシェアする
     *
     * @param user
     * @param form
     * @return
     * @throws Exception
     */
    @PostMapping("/trade-x-connect")
    @Operation(
            summary = "share operate trade history to X",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @ApiResponse(responseCode = "200", description = "Success"),
                @ApiResponse(responseCode = "400", description = "Bad Request")
            })
    public ResponseEntity<GlobalApiResponse<Long>> shareRecordToX(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user,
            @RequestBody TradeHistShareForm form)
            throws Exception {
        if (Objects.isNull(form.getHistoryId())) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
        Long userId = user.getId();
        Long count = posTradeService.countById(form.getHistoryId(), UserIdType.Operate, userId);
        if (0L == count) {
            log.info(
                    "there is no operate trade record : id="
                            + form.getHistoryId()
                            + ",userId:"
                            + userId);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
        return ResponseEntity.ok()
                .body(
                        sharedHistService.shareRecordAndJoinCampaign(
                                RecordShareForm.builder().recordId(form.getHistoryId()).build(),
                                userId,
                                SharedRecordType.INVEST_TRADE_HIST_SHARED));
    }
}
