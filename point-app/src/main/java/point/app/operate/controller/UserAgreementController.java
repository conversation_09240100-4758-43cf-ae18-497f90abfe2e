package point.app.operate.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import java.util.Optional;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import point.app.component.model.UserPrincipal;
import point.app.invest.model.request.UserAgreementForm;
import point.common.constant.ErrorCode;
import point.common.constant.UserAgreementType;
import point.common.entity.UserAgreement;
import point.common.entity.UserAgreementFile;
import point.common.model.response.GlobalApiResponse;
import point.common.service.UserAgreementFileService;
import point.common.service.UserAgreementService;

@Slf4j
@RestController
@RequestMapping("/app/v1/operate")
@RequiredArgsConstructor
public class UserAgreementController {

    private final UserAgreementFileService userAgreementFileService;
    private final UserAgreementService userAgreementService;

    /**
     * The method used for the game company
     *
     * @return GlobalApiResponse<List<UserAgreementFile>>
     */
    @Operation(
            summary = "Get the terms that for operate user.",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @ApiResponse(
                        responseCode = "200",
                        description = "Success",
                        content =
                                @Content(
                                        schema = @Schema(implementation = UserAgreementFile.class)))
            })
    @GetMapping("/agreement-file")
    public ResponseEntity<GlobalApiResponse<List<UserAgreementFile>>> getAgreementFiles() {
        // currently only TERMS_OF_SERVICE
        GlobalApiResponse<List<UserAgreementFile>> globalApiResponse =
                new GlobalApiResponse<>(
                        HttpStatus.OK.value(),
                        userAgreementFileService.findByAgreementType(
                                UserAgreementType.TERMS_OF_SERVICE_FOR_OPERATE));
        return ResponseEntity.ok().body(globalApiResponse);
    }

    /**
     * The method used for the game company
     *
     * @param user The login user
     * @return GlobalApiResponse<List<UserAgreement>>
     */
    @Operation(
            summary = "Get the terms that the user has agreed to.",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {@ApiResponse(responseCode = "200", description = "Success")})
    @GetMapping("/agreement")
    public ResponseEntity<GlobalApiResponse<List<UserAgreement>>> getUserAgreement(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user) {
        GlobalApiResponse<List<UserAgreement>> globalApiResponse =
                new GlobalApiResponse<>(
                        HttpStatus.OK.value(), userAgreementService.findByUserId(user.getId()));
        return ResponseEntity.ok().body(globalApiResponse);
    }

    /**
     * The method used for the game company
     *
     * @param user The login user
     * @param form The agreement form
     * @return GlobalApiResponse<UserAgreement>
     */
    @Operation(
            summary = "The user agrees to the terms of the specified version.",
            security = @SecurityRequirement(name = "x-auth"),
            requestBody =
                    @io.swagger.v3.oas.annotations.parameters.RequestBody(
                            description = "Agreement form data",
                            required = true,
                            content =
                                    @Content(
                                            schema =
                                                    @Schema(
                                                            implementation =
                                                                    UserAgreementForm.class))),
            responses = {@ApiResponse(responseCode = "200", description = "Success")})
    @PostMapping("/agreement")
    public ResponseEntity<GlobalApiResponse<UserAgreement>> agreement(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user,
            @Valid @RequestBody UserAgreementForm form) {

        log.info(
                "User {} is attempting to agree to the terms. Type: {}, Version: {}",
                user.getId(),
                form.getUserAgreementType(),
                form.getVersion());

        Optional<UserAgreement> userAgreement =
                userAgreementService.saveOrUpdateVersion(
                        user.getId(), form.getUserAgreementType(), form.getVersion());

        if (userAgreement.isPresent()) {
            log.info(
                    "User {} successfully agreed to the terms. Type: {}, Version: {}",
                    user.getId(),
                    form.getUserAgreementType(),
                    form.getVersion());
            return ResponseEntity.ok()
                    .body(new GlobalApiResponse<>(HttpStatus.OK.value(), userAgreement.get()));
        } else {
            log.warn(
                    "User {} failed to agree to the terms. Type: {}, Version: {}. Reason: Invalid version or no agreement files found.",
                    user.getId(),
                    form.getUserAgreementType(),
                    form.getVersion());
            return ResponseEntity.badRequest()
                    .body(
                            new GlobalApiResponse<>(
                                    HttpStatus.BAD_REQUEST.value(),
                                    ErrorCode.REQUEST_ERROR_USER_AGREEMENT_FILE_INVALID
                                            .getMessage()));
        }
    }
}
