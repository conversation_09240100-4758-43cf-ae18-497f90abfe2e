<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.6.final using JasperReports Library version 6.20.6-5c96b6aa8a39ac1dc6b6bea4b81168e16dd39231  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="yearly_report" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="0d1f9f25-95cd-4356-b881-0dbf91d945d7">
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="reportList_prt1" uuid="a82353f3-90c1-430a-9846-08291fb7ac55">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="currency" class="java.lang.String"/>
		<field name="yearBuyAmount" class="java.lang.String"/>
		<field name="yearBuyAsset" class="java.lang.String"/>
		<field name="yearSellAmount" class="java.lang.String"/>
		<field name="yearSellAsset" class="java.lang.String"/>
		<field name="moveInAmount" class="java.lang.String"/>
	</subDataset>
	<subDataset name="reportList_prt2" uuid="*************-46e4-8fbc-d840215638c1">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="currency" class="java.lang.String"/>
		<field name="moveOutAmount" class="java.lang.String"/>
		<field name="feeAmount" class="java.lang.String"/>
		<field name="feeAsset" class="java.lang.String"/>
	</subDataset>
	<subDataset name="reportList_prt3" uuid="f3d3d1ec-b7c4-41ce-87e9-754bb62469ac">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="currency" class="java.lang.String"/>
		<field name="yearBeginAmount" class="java.lang.String"/>
		<field name="yearEndAmount" class="java.lang.String"/>
	</subDataset>
	<parameter name="createdDay" class="java.lang.String"/>
	<parameter name="userId" class="java.lang.String"/>
	<parameter name="targetDate" class="java.lang.String"/>
	<parameter name="userName" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="reportList_prt1" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<field name="reportList_prt2" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<field name="reportList_prt3" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="213" splitType="Stretch">
			<staticText>
				<reportElement x="5" y="10" width="64" height="17" uuid="c717b03c-d09c-4a6a-902c-8d7bd4ed252e"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="msgothic-cus" size="10"/>
				</textElement>
				<text><![CDATA[【タイトル】]]></text>
			</staticText>
			<staticText>
				<reportElement x="5" y="30" width="64" height="17" uuid="c1ad6193-4990-41d7-b640-f9ab088d33c1">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="msgothic-cus" size="10"/>
				</textElement>
				<text><![CDATA[【作成日】]]></text>
			</staticText>
			<textField>
				<reportElement x="125" y="30" width="300" height="17" uuid="2ce66b79-317f-4709-805f-8268b0915054">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="msgothic-cus"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{createdDay}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="5" y="50" width="114" height="17" uuid="e8010b57-d10d-46d8-8f9c-b9c8bbaceab1">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="msgothic-cus" size="10"/>
				</textElement>
				<text><![CDATA[【運営者】]]></text>
			</staticText>
			<staticText>
				<reportElement x="5" y="70" width="114" height="17" uuid="d079d0f4-6596-4180-a42a-5ae54fc2739d">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="msgothic-cus" size="10"/>
				</textElement>
				<text><![CDATA[【住所】]]></text>
			</staticText>
			<staticText>
				<reportElement x="125" y="10" width="200" height="17" uuid="523224c2-1aaf-4142-ba88-a980e4b46c29">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="msgothic-cus"/>
				</textElement>
				<text><![CDATA[取引報告書兼残高報告書]]></text>
			</staticText>
			<staticText>
				<reportElement x="125" y="50" width="200" height="17" uuid="6592f539-b670-40ca-8623-a6a7555d6834">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="msgothic-cus"/>
				</textElement>
				<text><![CDATA[株式会社 backseat]]></text>
			</staticText>
			<staticText>
				<reportElement x="125" y="70" width="285" height="81" uuid="d8e43c60-a147-4ecd-a449-cf3f4642df8e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="msgothic-cus"/>
					<paragraph lineSpacing="Double"/>
				</textElement>
				<text><![CDATA[〒107-0052
東京都港区赤坂2-18-14 赤坂STビル2階
登録番号： 暗号資産交換業者 関東財務局長 第00026号
加入協会： 一般社団法人日本暗号資産取引業協会]]></text>
			</staticText>
			<staticText>
				<reportElement x="5" y="171" width="114" height="17" uuid="c7784a3b-b039-4e09-a10d-0f2137a8cbca">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="msgothic-cus" size="10"/>
				</textElement>
				<text><![CDATA[【ユーザーID】]]></text>
			</staticText>
			<textField>
				<reportElement x="125" y="171" width="300" height="17" uuid="53eba8cb-fbd1-4ffa-bf47-c76258a35f59">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="msgothic-cus"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{userId}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="5" y="191" width="114" height="17" uuid="5bb3584f-6d28-42a9-b9ef-972eabc9f0a9">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="msgothic-cus" size="10"/>
				</textElement>
				<text><![CDATA[【対象期間】]]></text>
			</staticText>
			<textField>
				<reportElement x="125" y="191" width="300" height="17" uuid="7b7b4ed3-4e1a-4bd3-b4ef-32bc657b46bd">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="msgothic-cus"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{targetDate}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="5" y="151" width="114" height="17" uuid="e2dd37db-e2ed-4d83-ac30-6117c9e7c7fd">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="msgothic-cus" size="10"/>
				</textElement>
				<text><![CDATA[【氏名】]]></text>
			</staticText>
			<textField>
				<reportElement x="125" y="151" width="300" height="17" uuid="4ff06d16-0fdf-4175-805e-e490a73a4101">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="msgothic-cus"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{userName}]]></textFieldExpression>
			</textField>
		</band>
		<band height="105">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="5" y="6" width="114" height="17" uuid="3efe1a72-aa2c-46fe-8531-b5efb641ecb0">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="msgothic-cus" size="10"/>
				</textElement>
				<text><![CDATA[【取引所/販売所取引等】]]></text>
			</staticText>
			<componentElement>
				<reportElement x="5" y="25" width="290" height="60" uuid="437d09d6-70e9-4cbf-9762-3cd00b1cc425">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table 2_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table 2_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table 2_TD"/>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" whenNoDataType="AllSectionsNoDetail">
					<datasetRun subDataset="reportList_prt3" uuid="a9b4eab6-8cbf-4ca0-bb10-9395db3e8373">
						<dataSourceExpression><![CDATA[$F{reportList_prt1}]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="40" uuid="c8f75e55-8cda-4261-a05c-8815cccab7d2">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						<jr:columnHeader style="Table 2_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="40" height="20" uuid="35ca2ca1-8814-4410-afb3-18d73cecbfcc">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[通貨名]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table 2_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="40" height="30" uuid="86a0b801-a7df-49a9-86b1-f0752d2380c6">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{currency}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="9eecfd50-abcd-4e8b-b927-57518f36e759">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
						<jr:columnHeader style="Table 2_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="120" height="20" uuid="*************-4057-bf6c-0aa9f9be145c">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[年始数量]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table 2_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="120" height="30" uuid="a4f94d7f-43e7-4ba7-af52-3efddfb3ce01">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{yearBeginAmount}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="f8200d20-**************-ffd01dc925ad">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
						<jr:columnHeader style="Table 2_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="120" height="20" uuid="4422b6b0-a3fa-40b5-95fd-29b581316f28">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[年末数量]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table 2_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="120" height="30" uuid="e2c2ea78-b531-421b-8c4c-b09243c737d3">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{yearEndAmount}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
		<band height="85">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<componentElement>
				<reportElement x="5" y="5" width="670" height="62" uuid="5199be80-d245-4e85-8eba-4a0d936433fc">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" whenNoDataType="AllSectionsNoDetail">
					<datasetRun subDataset="reportList_prt1" uuid="fb0972a6-c833-498e-b661-01ff8c2b7512">
						<dataSourceExpression><![CDATA[$F{reportList_prt2}]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="40" uuid="544184a2-4638-4a13-abff-9904192eaf93">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						<jr:columnHeader style="Table_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="40" height="20" uuid="398bf4aa-b10a-46fa-b948-c92320aaa68b">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[通貨名]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="40" height="30" uuid="ac9d2349-7bba-4920-852f-528d5a3caa02">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{currency}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="8706fc89-dea0-41b0-859c-caef96d8e9fe">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
						<jr:columnHeader style="Table_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="120" height="20" uuid="07680747-6620-42ab-bce0-9d2d7a9ff737">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[年中購入数量(*1)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="120" height="30" uuid="d97936ef-6051-46a1-8246-352acdcdb8a5">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{yearBuyAmount}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="130" uuid="f2c1d99f-5b60-4ef4-871d-a20e19a5cbb6">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
						<jr:columnHeader style="Table_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="130" height="20" uuid="ef66d668-39f1-46d4-8b75-ba96fa7e94d1">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[年中購入金額(*2)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="130" height="30" uuid="98746be2-8884-4725-a081-a5c35816d0ad">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{yearBuyAsset}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="beb98039-e47f-4f59-94ac-30a29636b477">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
						<jr:columnHeader style="Table_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="120" height="20" uuid="e3cb9bb4-2341-4d8b-ba6c-328136f30dcd">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[年中売却数量(*3)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="120" height="30" uuid="f852fbc8-e9e5-40b8-a748-2e070f156be9">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{yearSellAmount}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="130" uuid="71e494d1-c30a-4bf4-bdd7-c534be0ad9ef">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
						<jr:columnHeader style="Table_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="130" height="20" uuid="b4b8b9c6-8eed-44f0-8f19-4daa462943b7">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[年中売却金額(*4)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="130" height="30" uuid="765a8144-e254-45a1-9bda-d7c845a68df4">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{yearSellAsset}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="fd441960-f1f8-41fa-a9e1-6399bf4b35b3">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column6"/>
						<jr:columnHeader style="Table_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="120" height="20" uuid="53343114-f730-42fc-8a30-402b431c939b">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[年中移入数量(*5)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="120" height="30" uuid="22972643-06c5-4ab7-9f95-d396edb30d19">
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{moveInAmount}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
		<band height="70">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<componentElement>
				<reportElement x="5" y="5" width="670" height="55" uuid="5eff6ba4-5a14-49e6-ad21-501047c83bed">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" whenNoDataType="AllSectionsNoDetail">
					<datasetRun subDataset="reportList_prt2" uuid="471fd5e8-b17a-48ed-91bc-b7c5dad23306">
						<dataSourceExpression><![CDATA[$F{reportList_prt3}]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="40" uuid="1cca7976-f6f3-48c0-a9d7-7a54081919f6">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						<jr:columnHeader style="Table 1_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="40" height="20" uuid="b74f403d-e629-4cec-9c60-d389a846f036">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[通貨名]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table 1_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="40" height="30" uuid="ada2acc6-aae1-4633-83ff-b0c1735dd0be">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{currency}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="9201c428-f176-4a50-80c0-7b93aa7017b2">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
						<jr:columnHeader style="Table 1_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="120" height="20" uuid="53ce2589-f9b7-4391-bdbe-057d2831774f">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[年中移出数量(*6)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table 1_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="120" height="30" uuid="1cab11aa-7833-4ac8-88ab-0d2f51906df1">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{moveOutAmount}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="d31625d6-040c-45fc-8b48-28712eef087b">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
						<jr:columnHeader style="Table 1_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="120" height="20" uuid="fd46dbd2-9b5f-4629-9f18-29dd68e971ef">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[ステーキング報酬数量]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table 1_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="120" height="30" uuid="8c38bff3-79b2-4126-9e9c-ce3b6df61aba">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="130" uuid="44718f35-4e96-4512-86e8-8b11bc9e6bee">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
						<jr:columnHeader style="Table 1_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="130" height="20" uuid="cf1a201c-b00c-4541-9c01-aaa2fa8e4b77">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[ステーキング報酬金額(*7) ]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table 1_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="130" height="30" uuid="79574f03-8688-4266-83c9-85eb9170a7df">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="407a9f84-0dff-4573-bf13-053c6da7178f">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
						<jr:columnHeader style="Table 1_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="120" height="20" uuid="0c7cca0d-2760-44cc-b23e-b98f567104a1">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[支払手数料(数量)(*8)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table 1_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="120" height="30" uuid="750d5655-90ae-4cda-8e61-9e1bf0d05acf">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{feeAmount}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="130" uuid="13857c41-16c7-4700-adda-c825252a312e">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column6"/>
						<jr:columnHeader style="Table 1_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="130" height="20" uuid="55fbf3da-581e-4bca-8116-c03617c52265">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<text><![CDATA[支払手数料(円換算)(*9)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table 1_TD" height="30">
							<textField textAdjust="StretchHeight">
								<reportElement x="0" y="0" width="130" height="30" uuid="b23e6355-21f4-4ee2-bf5b-9ae13031dbde">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="msgothic-cus"/>
									<paragraph leftIndent="5" rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{feeAsset}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
		<band height="456">
			<staticText>
				<reportElement x="5" y="11" width="787" height="439" uuid="a6d9ddaf-4a64-44fe-b391-2f95407efadf">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="msgothic-cus" size="10"/>
					<paragraph lineSpacing="1_1_2"/>
				</textElement>
				<text><![CDATA[(*1） 年中購入数量：取得した暗号資産の数量
(*2） 年中購入金額：購入数量を取得時のレートで円換算した金額
(*3)　年中売却数量：支払った暗号資産の数量
(*4)　年中売却金額：売却数量を支払い時のレートで円換算した金額
(*5)　年中移入数量：その年に購入以外で口座に受け入れた暗号資産の数量・各種キャンペーンボーナスの数量・日本円入金・暗号資産売却により獲得した日本円
(*6)　年中移出数量：その年に売却以外で口座から払い出した暗号資産の数量・日本円出金・暗号資産購入により支出した日本円
(*7)　ステーキング報酬金額：１年間に受け取った各ステーキング報酬数量に受取日の終値で円換算した金額
(*8)　支払手数料(数量)は、「移出数量」のうち手数料としてお支払いいただいた暗号資産の数量のみを記載しております。
(*9)　支払手数料(円換算) のうちJPYの欄の金額は、その年の発生した日本円の手数料を表します。
　　　 暗号資産の欄の数量は、支払手数料としてお支払いいただいた暗号資産の数量に支払い時のレートで円換算した金額となります。

(注1)キャンペーン等で無償付与された場合、以下の通り表示しております。
移入数量：付与された暗号資産の数量

【本報告書に関するご注意】
1.　本書は、資金決済に関する法律第６３条の１０及び暗号資産交換業者に関する内閣府令第２２条第６項に基づき交付されるものです。
必ず内容をご確認いただき、万一疑義がございましたら、速やかにご連絡ください。本書発行後５日以内にご連絡がない場合には、ご承認いただいたものとして取り扱わせていただきます。

2.　本報告書は参考情報となります。暗号資産の税務上の取り扱い（ステーキングの報酬、キャンペーンにより付与された暗号資産の取り扱いなどを含む）はお客さまの状況により異なりますので、ご不明な点等は最寄りの税務署⼜は税理⼠等の専⾨家にお問い合わせください。

3.　当社以外の暗号資産に係るサービスをご利用になった場合や、他の取引所などから入金している場合、取得単価が不明なため当社で損益を確定することができません。お客様ご自身で取引報告書等を基に、他社のお取引と合わせて損益計算を行っていただきますようお願いいたします。]]></text>
			</staticText>
		</band>
	</detail>
	<pageFooter>
		<band height="33">
			<textField>
				<reportElement x="320" y="2" width="100" height="30" uuid="9bf0dd07-5fdd-4844-afe7-7ac620eee7bd"/>
				<textElement textAlignment="Right">
					<font fontName="msgothic-cus"/>
				</textElement>
				<textFieldExpression><![CDATA["-  " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="420" y="2" width="100" height="30" uuid="3a699e1d-c28c-4f65-96fc-d1e5d4336f8c"/>
				<textElement textAlignment="Left">
					<font fontName="msgothic-cus"/>
				</textElement>
				<textFieldExpression><![CDATA["/" + $V{PAGE_NUMBER} + "  -"]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
