plugins {
//  id 'com.gorylenko.gradle-git-properties' version '2.3.1'
  id 'eclipse'
  id 'io.freefair.lombok' version '6.3.0'
  id 'io.spring.dependency-management' version '1.0.11.RELEASE'
  id 'jacoco'
  id 'org.springframework.boot' version '2.5.7'
}

  sourceCompatibility = '17'
  targetCompatibility = '17'

configurations {
  compile.exclude module: 'slf4j-nop'
}

dependencies {
  implementation project(':' + project_name_prefix + 'point-common')

//  annotationProcessor 'org.hibernate:hibernate-jpamodelgen:5.4.10.Final'

  implementation platform('com.amazonaws:aws-java-sdk-bom:1.12.128')
  implementation 'com.amazonaws:aws-java-sdk-sns'
  implementation 'com.amazonaws:aws-java-sdk-s3'

  implementation 'com.amazon.redshift:redshift-jdbc42-no-awssdk:1.2.55.1083'
  implementation 'com.auth0:java-jwt:3.18.2'
  implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-csv'
  implementation 'com.google.zxing:core:3.4.1'
  implementation 'com.ibm.icu:icu4j:70.1'
  implementation 'com.warrenstrange:googleauth:1.5.0'
  implementation 'commons-io:commons-io:2.11.0'
  implementation 'io.micrometer:micrometer-registry-prometheus'
  implementation 'net.logstash.logback:logstash-logback-encoder:6.6'
  implementation 'org.apache.commons:commons-lang3:3.12.0'
  implementation 'org.apache.commons:commons-text:1.10.0'
  implementation 'org.apache.httpcomponents:httpmime:4.5.13'
  implementation 'org.codehaus.janino:janino:3.1.6'
  implementation 'org.mariadb.jdbc:mariadb-java-client:2.7.4'
  implementation 'org.springframework.boot:spring-boot-configuration-processor'
  implementation 'org.springframework.boot:spring-boot-devtools'
  implementation 'org.springframework.boot:spring-boot-starter-actuator'
  implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
  implementation 'org.springframework.boot:spring-boot-starter-data-redis'
  implementation 'org.springframework.boot:spring-boot-starter-mail'
  implementation 'org.springframework.boot:spring-boot-starter-security'
  implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
  implementation 'org.springframework.boot:spring-boot-starter-validation'
  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springframework.boot:spring-boot-starter-websocket'
  implementation 'org.springframework.cloud:spring-cloud-aws-actuator:2.2.6.RELEASE'
  implementation 'org.springframework.cloud:spring-cloud-starter-aws:2.2.6.RELEASE'
  implementation 'org.springframework.session:spring-session-data-redis'

  implementation 'io.projectreactor.netty:reactor-netty'
  implementation 'io.netty:netty-all'

  implementation 'io.grpc:grpc-all:1.42.1'
  implementation 'io.envoyproxy.protoc-gen-validate:pgv-java-stub:0.6.2'
  implementation 'com.google.protobuf:protobuf-java:3.19.1'
  implementation 'org.springdoc:springdoc-openapi-ui:1.7.0'
  implementation 'com.itextpdf:itextpdf:5.5.6'
  implementation 'com.itextpdf:itext-asian:5.2.0'

  compileOnly "com.github.spotbugs:spotbugs:4.5.1"

  runtimeOnly ('com.amazon.redshift:redshift-jdbc42:2.1.0.7'){
    exclude group: 'com.amazon.redshift', module: 'redshift-jdbc42'
  }

  testImplementation 'org.springframework.boot:spring-boot-starter-test'
  testImplementation 'org.springframework.boot:spring-boot-starter-webflux'
  testImplementation 'org.springframework.security:spring-security-test'
  testRuntimeOnly 'com.h2database:h2' 
  testRuntimeOnly 'org.junit.jupiter:junit-jupiter-api'
  testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

ext['generatedMainJava'] = 'src/main/java'

sourceSets {
  main {
    java {
      srcDir generatedMainJava
    }
  }
}
/*
compileJava {
  options.annotationProcessorGeneratedSourcesDirectory = file(generatedMainJava)
}
*/
bootJar {
  manifest {
    attributes 'Start-Class': 'point.app.Application'
  }

  archiveBaseName = 'point-app'
}

bootRun {
  if (project.hasProperty('args')) {
    args project.args.split('\\s+' as Closure)
  }
}

jacoco {
    applyTo bootRun
}

test {
  ignoreFailures = true // Because development is in progress
  useJUnitPlatform()
    jacoco {
      enabled = true
      destinationFile = file("$buildDir/jacoco/${name}.exec")
      includes = []
      excludes = []
      excludeClassLoaders = []
      includeNoLocationClasses = false
      sessionId = "<auto-generated value>"
      dumpOnExit = true
      classDumpDir = null
      output = JacocoTaskExtension.Output.FILE
      address = "localhost"
      port = 34323
      jmx = true
    }
    finalizedBy jacocoTestReport // report is always generated after tests run
}

jacocoTestReport {
  reports {
    xml.enabled = true
    html.enabled = true
  }
  dependsOn test // tests are required to run before generating the report
}

task applicationCodeCoverageReport(type:JacocoReport) {
  executionData bootRun
  sourceSets sourceSets.main
}

springBoot {
   buildInfo()
}

bootBuildImage {
  doFirst() {
    builder = "paketobuildpacks/builder:full"

    def env = System.getenv('ENVIRONMENT')
    def env_name = System.getenv('BUILD_DOCKER_IMAGE_NAME')
    def env_version = System.getenv('BUILD_DOCKER_IMAGE_VERSION')

    if (env_name != null) {
      if (env_version == null) {
        env_version = 'latest'
      }

      imageName = env_name + ':' + env_version
    }

    environment = ["BP_JVM_VERSION" : "17.*"]
    pullPolicy = 'IF_NOT_PRESENT'

    println "bootBuildImage: env = $env, imageName=" + imageName
  }
}
