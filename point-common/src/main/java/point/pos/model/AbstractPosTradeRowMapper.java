package point.pos.model;

import java.sql.ResultSet;
import java.sql.SQLException;
import point.common.constant.OrderChannel;
import point.common.constant.OrderType;
import point.common.constant.UserIdType;
import point.common.model.TradeRowMapper;
import point.common.util.FormatUtil;
import point.pos.entity.PosTrade;
import point.pos.entity.PosTrade_;

public abstract class AbstractPosTradeRowMapper extends TradeRowMapper<PosTrade> {

    @Override
    public PosTrade mapRow(ResultSet rs, int rowNum) throws SQLException {
        PosTrade trade = super.mapRow(rs, rowNum);

        if (trade != null) {
            trade.setOrderType(
                    OrderType.valueOf(
                            rs.getString(
                                    FormatUtil.formatCamelToSnake(PosTrade_.orderType.getName()))));
            trade.setOrderChannel(
                    OrderChannel.valueOf(
                            rs.getString(
                                    FormatUtil.formatCamelToSnake(
                                            PosTrade_.orderChannel.getName()))));
            trade.setJpyConversion(
                    rs.getBigDecimal(
                            FormatUtil.formatCamelToSnake(PosTrade_.jpyConversion.getName())));
            trade.setAssetAmount(
                    rs.getBigDecimal(
                            FormatUtil.formatCamelToSnake(PosTrade_.assetAmount.getName())));
            trade.setIdType(
                    UserIdType.valueOf(
                            rs.getString(
                                    FormatUtil.formatCamelToSnake(PosTrade_.idType.getName()))));
            trade.setExperiencePoints(
                    rs.getLong(
                            FormatUtil.formatCamelToSnake(PosTrade_.experiencePoints.getName())));
            trade.setCreatedAt(
                    rs.getTimestamp(FormatUtil.formatCamelToSnake(PosTrade_.createdAt.getName())));
            trade.setSymbolId(
                    rs.getLong(FormatUtil.formatCamelToSnake(PosTrade_.symbolId.getName())));
            trade.setIncome(
                    rs.getBigDecimal(FormatUtil.formatCamelToSnake(PosTrade_.income.getName())));
        }

        return trade;
    }
}
