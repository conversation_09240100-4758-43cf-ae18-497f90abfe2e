package point.pos.predicate;

import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.OrderChannel;
import point.common.constant.OrderType;
import point.common.constant.UserIdType;
import point.common.predicate.TradePredicate;
import point.pos.entity.PosTrade;
import point.pos.entity.PosTrade_;

@Component
public class PosTradePredicate extends TradePredicate<PosTrade> {

    public Predicate inUserIds(
            CriteriaBuilder criteriaBuilder, Root<PosTrade> root, List<Long> userIds) {
        return root.get(PosTrade_.userId).in(userIds);
    }

    public Predicate inExceptUserIds(
            CriteriaBuilder criteriaBuilder, Root<PosTrade> root, List<Long> exceptUserIds) {
        return root.get(PosTrade_.userId).in(exceptUserIds).not();
    }

    public Predicate inOrderType(Root<PosTrade> root, OrderType... orderTypes) {
        return root.get(PosTrade_.orderType).in((Object[]) orderTypes);
    }

    public Predicate notInOrderType(
            CriteriaBuilder criteriaBuilder, Root<PosTrade> root, OrderType... orderTypes) {
        return criteriaBuilder.not(inOrderType(root, orderTypes));
    }

    public Predicate equalOrderChannel(
            CriteriaBuilder criteriaBuilder, Root<PosTrade> root, OrderChannel orderChannel) {
        return criteriaBuilder.equal(root.get(PosTrade_.orderChannel), orderChannel);
    }

    public Predicate equalOrderId(
            CriteriaBuilder criteriaBuilder, Root<PosTrade> root, Long orderId) {
        return criteriaBuilder.equal(root.get(PosTrade_.orderId), orderId);
    }

    public Predicate equalTargetOrderId(
            CriteriaBuilder criteriaBuilder, Root<PosTrade> root, Long targetOrderId) {
        return criteriaBuilder.equal(root.get(PosTrade_.targetOrderId), targetOrderId);
    }

    public Predicate equalOrderType(
            CriteriaBuilder criteriaBuilder, Root<PosTrade> root, OrderType orderType) {
        return criteriaBuilder.equal(root.get(PosTrade_.orderType), orderType);
    }

    public Predicate equalIdType(
            CriteriaBuilder criteriaBuilder, Root<PosTrade> root, UserIdType userIdType) {
        return criteriaBuilder.equal(root.get(PosTrade_.idType), userIdType);
    }
}
