package point.pos.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.OrderChannel;
import point.common.constant.OrderType;
import point.common.constant.UserIdType;
import point.common.entity.Trade;
import point.common.serializer.BigDecimalSerializer;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "pos_trade")
public class PosTrade extends Trade {

    @Column(name = "order_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private OrderType orderType;

    @Column(name = "order_channel", nullable = false)
    @Enumerated(EnumType.STRING)
    private OrderChannel orderChannel = OrderChannel.UNKNOWN;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Column(name = "jpy_conversion", precision = 34, scale = 20, nullable = false)
    private BigDecimal jpyConversion;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Column(name = "asset_amount", precision = 34, scale = 20, nullable = false)
    private BigDecimal assetAmount;

    @Column(name = "id_type")
    @Enumerated(EnumType.STRING)
    private UserIdType idType;

    @Column(name = "eval_profit_loss_amt", precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal evalProfitLossAmt;

    @Column(name = "eval_profit_loss_amt_rate", precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal evalProfitLossAmtRate;

    @Column(name = "avg_acq_unit_price", precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal avgAcqUnitPrice;

    @Column(name = "income", precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal income;

    @Column(name = "experience_points")
    private Long experiencePoints;

    @Column(name = "notes")
    private String notes;

    // TODO bad solution
    // set value when order sell. default to zero
    @Column(name = "user_growth_stage_id")
    private Integer userGrowthStageId;

    public PosTrade setAnotherProperties(
            BigDecimal fee,
            OrderType orderType,
            OrderChannel orderChannel,
            BigDecimal jpyConversion,
            BigDecimal assetAmount,
            UserIdType idType) {
        setFee(fee);
        this.orderType = orderType;
        this.orderChannel = orderChannel;
        this.jpyConversion = jpyConversion;
        this.assetAmount = assetAmount;
        this.idType = idType;
        return this;
    }

    @JsonIgnore
    public BigDecimal nextBaseBalance(BigDecimal balance) {
        return getOrderSide().isBuy() ? balance.add(getAmount()) : balance.subtract(getAmount());
    }

    @JsonIgnore
    public BigDecimal nextQuoteBalance(BigDecimal balance) {
        return getOrderSide().isBuy()
                ? balance.subtract(assetAmount).subtract(getFee())
                : balance.add(assetAmount).subtract(getFee());
    }

    @JsonIgnore
    public BigDecimal ownNextBaseBalance(BigDecimal balance) {
        return getOrderSide().isBuy() ? balance.add(getAmount()) : balance.subtract(getAmount());
    }

    @JsonIgnore
    public BigDecimal ownNextQuoteBalance(BigDecimal balance) {
        return getOrderSide().isBuy()
                ? balance.subtract(getAssetAmount()).subtract(getFee())
                : balance.add(getAssetAmount()).subtract(getFee());
    }

    public PosTrade setCalProperties(
            BigDecimal fee,
            OrderType orderType,
            OrderChannel orderChannel,
            BigDecimal jpyConversion,
            BigDecimal assetAmount,
            UserIdType idType,
            BigDecimal evalProfitLossAmt,
            BigDecimal evalProfitLossAmtRate,
            BigDecimal avgAcqUnitPrice,
            BigDecimal income,
            Long experiencePoints) {
        setFee(fee);
        this.orderType = orderType;
        this.orderChannel = orderChannel;
        this.jpyConversion = jpyConversion;
        this.assetAmount = assetAmount;
        this.idType = idType;
        this.evalProfitLossAmt = evalProfitLossAmt;
        this.evalProfitLossAmtRate = evalProfitLossAmtRate;
        this.avgAcqUnitPrice = avgAcqUnitPrice;
        this.income = income;
        this.experiencePoints = experiencePoints;
        return this;
    }
}
