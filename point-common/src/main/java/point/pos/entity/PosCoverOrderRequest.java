package point.pos.entity;

import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.NoArgsConstructor;
import point.common.entity.AbstractEntity;

@Entity
@NoArgsConstructor
@Table(name = "pos_cover_order_request")
public class PosCoverOrderRequest extends AbstractEntity {

    private Long orderId;
    private Long userId;
    private String requestBody;
    private String responseBody;
}
