package point.pos.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.service.EntityService;
import point.pos.entity.PosOrder;
import point.pos.model.PosOrderRowMapper;
import point.pos.predicate.PosOrderPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public abstract class PosOrderHistoryService extends EntityService<PosOrder, PosOrderPredicate> {

    public abstract Class<PosOrderRowMapper> getRowMapperClass();

    public PosOrderRowMapper newRowMapper() {
        PosOrderRowMapper rowMapper = null;

        try {
            rowMapper = getRowMapperClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.error("newRowMapper failed", e);
        }

        return rowMapper;
    }
}
