package point.pos.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import point.common.component.CustomRedisTemplate;
import point.common.constant.*;
import point.common.entity.BcCurrencyConfig;
import point.common.entity.CurrencyPairConfig;
import point.common.entity.Symbol;
import point.common.model.response.CurrencyConfigData;
import point.common.service.BcCurrencyConfigService;
import point.common.service.CurrencyPairConfigService;
import point.common.service.SymbolService;
import point.common.util.DateUnit;
import point.common.util.JsonUtil;
import point.common.websocket.RedisPublisher;
import point.pos.entity.PosCandlestick;
import point.pos.entity.PosMarketMakerConfig;
import point.pos.model.PosBestPriceData;

@Slf4j
@Transactional(readOnly = true, transactionManager = "masterTransactionManager")
@RequiredArgsConstructor
public class PosBestPriceService {

    private final SymbolService symbolService;
    private final CurrencyPairConfigService currencyPairConfigService;
    private final PosAmberBestPriceService amberBestPriceService;
    private final CustomRedisTemplate<PosBestPriceData> bestPriceRedisTemplate;
    private final PosBestPriceArchiveService archiveService;
    private final PosMarketMakerConfigService posMarketMakerConfigService;
    private final PosTradeService posTradeService;
    private final PosCandlestickService posCandlestickService;
    private final RedisPublisher redisPublisher;
    private final BcCurrencyConfigService bcCurrencyConfigService;

    public PosBestPriceData getBestPrice(Long symbolId) {
        // get symbol by ID
        Symbol symbol = symbolService.findOne(symbolId);
        if (Objects.isNull(symbol)) {
            log.warn(" getBestPrice The symbol not exists by id: {}", symbolId);
            return null;
        }

        if (!EnumSet.of(TradeType.INVEST, TradeType.OPERATE).contains(symbol.getTradeType())) {
            log.warn(
                    "getBestPrice The symbol tradeType incorrect, symbolId:{}, symbolName:{}, tradeType: {}",
                    symbolId,
                    symbol.getBaseCurrencyName(),
                    symbol.getTradeType());
            return null;
        }

        Exchange exchange = this.getPointBy();
        String cacheKey = this.getCacheKey(exchange, symbol.getName());
        return bestPriceRedisTemplate.getValue(cacheKey);
    }

    public void handleBestPrice(Long symbolId) throws Exception {
        // get symbol by ID
        Symbol symbol = symbolService.findOne(symbolId);
        if (Objects.isNull(symbol)) {
            log.warn(" handleBestPrice The symbol not exists by id: {}", symbolId);
            return;
        }
        this.handleBestPrice(symbol);
    }

    public void handleBestPrice(@NotNull Symbol symbol) throws Exception {
        Long symbolId = symbol.getId();
        if (!EnumSet.of(TradeType.INVEST, TradeType.OPERATE).contains(symbol.getTradeType())) {
            log.warn(
                    "handleBestPrice The symbol tradeType incorrect, symbolId:{}, symbolName:{}, tradeType: {}",
                    symbolId,
                    symbol.getName(),
                    symbol.getTradeType());
            return;
        }

        Exchange exchange = this.getPointBy();
        CurrencyPairConfig currencyPairConfig =
                currencyPairConfigService.findOne(symbol.getTradeType(), symbol.getCurrencyPair());
        if (!currencyPairConfig.isEnabled()) {
            String cacheKey = this.getCacheKey(exchange, symbol.getName());
            bestPriceRedisTemplate.delete(cacheKey);
            return;
        }
        // -- get best price determine by symbol
        PosBestPriceData bestPrice = this.getBestPrice(symbol);
        if (Objects.isNull(bestPrice)
                || Objects.isNull(bestPrice.getBestAsk())
                || Objects.isNull(bestPrice.getBestBid())) {
            log.warn("No best price returned by symbol {}", symbol.getName());
            return;
        }

        Date date = new Date();
        long now = date.getTime();
        long before1Day = now - DateUnit.DAY.getMillis();
        Date minute1From =
                CandlestickType.PT1M.getTargetAt(
                        DateUnit.MINUTE.truncate(before1Day + DateUnit.MINUTE.getMillis()));
        Date hour1From =
                CandlestickType.PT1H.getTargetAt(
                        DateUnit.HOUR.truncate(before1Day + DateUnit.HOUR.getMillis()));
        Date hour1To = CandlestickType.PT1H.getTargetAt(DateUnit.HOUR.truncate(now));
        Date minute1To = CandlestickType.PT1M.getTargetAt(DateUnit.MINUTE.truncate(now));

        PosBestPriceData.PosTicker ticker = new PosBestPriceData.PosTicker();
        updateTickerByTrades(symbol, ticker, new Date(before1Day), minute1From);
        updateTickerByCandlesticks(symbol, ticker, CandlestickType.PT1M, minute1From, hour1From);
        updateTickerByCandlesticks(symbol, ticker, CandlestickType.PT1H, hour1From, hour1To);
        updateTickerByCandlesticks(symbol, ticker, CandlestickType.PT1M, hour1To, minute1To);
        updateTickerByTrades(symbol, ticker, minute1To, date);

        bestPrice.setSymbolId(symbol.getId());
        bestPrice.setOpen(symbol.getCurrencyPair().getScaledPrice(ticker.getOpen()));
        bestPrice.setHigh(symbol.getCurrencyPair().getScaledPrice(ticker.getHigh()));
        bestPrice.setLow(symbol.getCurrencyPair().getScaledPrice(ticker.getLow()));
        bestPrice.setLast(symbol.getCurrencyPair().getScaledPrice(ticker.getLast()));
        LocalDate currentDay = LocalDate.now();
        LocalDateTime timePrevious = currentDay.minusDays(1).atTime(0, 0, 0);
        ZonedDateTime zonedDateTime = timePrevious.atZone(ZoneId.systemDefault());
        PosCandlestick posCandlestick =
                posCandlestickService.findOne(
                        symbolId, CandlestickType.P1D, Date.from(zonedDateTime.toInstant()));
        bestPrice.setClose(
                posCandlestick == null
                        ? symbol.getCurrencyPair().getScaledPrice(ticker.getLast())
                        : symbol.getCurrencyPair().getScaledPrice(posCandlestick.getClose()));
        bestPrice.setVolume(
                symbol.getCurrencyPair().getScaledAmount(ticker.getVolume()).toPlainString());

        // -- spread handle
        this.handleSpread(bestPrice, symbol);
        BigDecimal mid = bestPrice.getBestAsk().add(bestPrice.getBestBid());
        bestPrice.setMid(symbol.getCurrencyPair().getScaledPrice(mid.divide(new BigDecimal("2"))));

        // -- sync to redis
        bestPriceRedisTemplate.setUnexpireValue(getCacheKey(exchange, symbol.getName()), bestPrice);
        // Pub to websocket through redis
        redisPublisher.publish(bestPrice);
        // -- archive best price
        archiveService.archive(symbol, bestPrice.getBestAsk(), bestPrice.getBestBid());
    }

    private void handleSpread(@NotNull PosBestPriceData posBestPriceData, Symbol symbol) {

        BigDecimal bestAsk = posBestPriceData.getBestAsk();
        BigDecimal bestBid = posBestPriceData.getBestBid();

        // -- get currency pair config
        CurrencyPairConfig currencyPairConfig =
                currencyPairConfigService.findOne(symbol.getTradeType(), symbol.getCurrencyPair());
        CurrencyPair currencyPair = symbol.getCurrencyPair();
        if (Objects.nonNull(currencyPairConfig)
                && Objects.nonNull(currencyPairConfig.getPosSpreadPercent())) {
            // original price: 1.01
            // ask: 1.01 - (1.01 * ( 3/100 ))
            // bid: 1.01 + (1.01 * ( 3/100 ))
            // ask be:MM_price * (1 + Spread + Slippage)
            // bid be:MM_price * (1 - Spread - Slippage)
            BigDecimal posSpreadPercent =
                    currencyPairConfig.getPosSpreadPercent().divide(CommonConstants.HUNDRED);
            BigDecimal posSlippagePercent =
                    currencyPairConfig.getPosSlippagePercent().divide(CommonConstants.HUNDRED);
            BigDecimal bestAskMultiply =
                    bestAsk.multiply(
                            (BigDecimal.ONE.add(posSpreadPercent).add(posSlippagePercent)));
            BigDecimal bestBidMultiply =
                    bestBid.multiply(
                            BigDecimal.ONE.subtract(posSpreadPercent).subtract(posSlippagePercent));
            posBestPriceData.setBestAsk(
                    bestAskMultiply.setScale(currencyPair.getQuotePrecision(), RoundingMode.UP));
            posBestPriceData.setBestBid(
                    bestBidMultiply.setScale(currencyPair.getQuotePrecision(), RoundingMode.DOWN));
        }
        posBestPriceData.setBestMmAsk(bestAsk);
        posBestPriceData.setBestMmBid(bestBid);
    }

    private PosBestPriceData getBestPrice(Symbol symbol) {
        // TODO only need to find once
        PosMarketMakerConfig marketMakerConfig =
                posMarketMakerConfigService.findBySymbolId(symbol.getId());
        if (Objects.isNull(marketMakerConfig)) {
            log.warn(
                    "No PosMarketMakerConfig found by symbolId: {} or config is disabled",
                    symbol.getId());
            return null;
        }
        List<CurrencyConfigData> currencyConfigData = getCurrencyConfigData(symbol);
        return amberBestPriceService.getBestPrice(
                symbol, marketMakerConfig.getQuantity(), currencyConfigData);
    }

    /**
     * @param point
     * @param symbolName POS_ETH_JPY
     * @return
     */
    private String getCacheKey(Exchange point, String symbolName) {
        return ("best_price:" + point.getName() + ":" + symbolName).toLowerCase();
    }

    /**
     * @param
     * @return
     */
    private Exchange getPointBy() {
        return Exchange.AMBER;
    }

    private void updateTicker(
            PosBestPriceData.PosTicker ticker,
            BigDecimal open,
            BigDecimal high,
            BigDecimal low,
            BigDecimal last,
            BigDecimal volume) {
        if (ticker.getOpen().signum() == 0) {
            ticker.setOpen(open);
        }
        if (ticker.getHigh().signum() == 0 || ticker.getHigh().compareTo(high) < 0) {
            ticker.setHigh(high);
        }

        if (ticker.getLow().signum() == 0 || ticker.getLow().compareTo(low) > 0) {
            ticker.setLow(low);
        }

        ticker.setLast(last);
        ticker.setVolume(ticker.getVolume().add(volume));
    }

    private void updateTickerByTrades(
            Symbol symbol, PosBestPriceData.PosTicker ticker, Date dateFrom, Date dateTo)
            throws Exception {
        // Takerには販売所のスプレッドが含まれるため、Makerで生成する
        posTradeService
                .findMakerByCondition(symbol.getId(), dateFrom, dateTo)
                .forEach(
                        posTrade ->
                                updateTicker(
                                        ticker,
                                        posTrade.getPrice(),
                                        posTrade.getPrice(),
                                        posTrade.getPrice(),
                                        posTrade.getPrice(),
                                        posTrade.getAmount()));
    }

    private void updateTickerByCandlesticks(
            Symbol symbol,
            PosBestPriceData.PosTicker ticker,
            CandlestickType candlestickType,
            Date from,
            Date to) {
        posCandlestickService
                .findByCondition(symbol.getId(), candlestickType, from, to)
                .forEach(
                        candlestick ->
                                updateTicker(
                                        ticker,
                                        candlestick.getOpen(),
                                        candlestick.getHigh(),
                                        candlestick.getLow(),
                                        candlestick.getClose(),
                                        candlestick.getVolume()));
    }

    private List<CurrencyConfigData> getCurrencyConfigData(Symbol symbol) {
        if (isSupportedCurrencyPair(symbol.getCurrencyPair())) {
            BcCurrencyConfig currencyConfig = getCurrencyConfig(symbol);
            if (Objects.isNull(currencyConfig)) {
                log.warn("No BcCurrencyConfig found for symbol: {}", symbol);
                return null;
            }
            return parseCurrencyConfigData(currencyConfig);
        }

        return Collections.emptyList();
    }

    private BcCurrencyConfig getCurrencyConfig(Symbol symbol) {
        String currencyType = CurrencyType.getCurrencyTypeById(symbol.getId());
        if (currencyType == null) {
            log.warn("No CurrencyType found for symbolId: {}", symbol.getId());
            return null;
        }

        // TODO only need to find once
        BcCurrencyConfig currencyConfig = bcCurrencyConfigService.findOneByActive(currencyType);
        if (currencyConfig != null) {
            log.info("currencyConfigData: {}", JsonUtil.encode(currencyConfig.getCurrencyData()));
        }
        return currencyConfig;
    }

    private boolean isSupportedCurrencyPair(CurrencyPair currencyPair) {
        return CurrencyPair.BALC_JPY.equals(currencyPair)
                || CurrencyPair.ACTC_JPY.equals(currencyPair);
    }

    private List<CurrencyConfigData> parseCurrencyConfigData(BcCurrencyConfig currencyConfig) {
        try {
            return JsonUtil.fromJsonToList(
                    currencyConfig.getCurrencyData(), CurrencyConfigData.class);
        } catch (Exception e) {
            log.error("Failed to parse currencyConfigData: {}", e.getMessage(), e);
            return null;
        }
    }
}
