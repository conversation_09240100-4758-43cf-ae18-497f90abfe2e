package point.common.auth;

import java.util.Base64;
import point.common.config.StringConfig;

public class AuthUtil {

    public static String getAuthorization(
            AuthTypeEnum authTypeEnum, String clientId, String clientSecret) {
        switch (authTypeEnum) {
            case CLIENT_SECRET_BASIC:
                return Base64.getEncoder()
                        .encodeToString((clientId + StringConfig.colon + clientSecret).getBytes());
            default:
                return null;
        }
    }
}
