package point.common.auth;

import com.google.gson.reflect.TypeToken;
import java.io.IOException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.*;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidatorFactory;
import javax.validation.executable.ExecutableValidator;
import point.common.auth.common.*;
import point.common.auth.model.TokenRequest;
import point.common.auth.model.TokenResponse;

public class TokenApi {
    private ApiClient apiClient;

    public TokenApi() {
        this(Configuration.getAuthApiClient());
    }

    public TokenApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Build call for tokenUsingPOST
     *
     * @param contentType application/x-www-form-urlencodedに固定 (required)
     * @param body HTTPリクエストボディ (required)
     * @param authorization クライアント認証用のBasic認証値。
     *     (クライアントIDとクライアントシーレットを:(コロン)で連結し、Base64エンコードしたものを設定。)
     *     事前に登録する「クライアント認証方式」にclient_secret_basic(ベーシック認証)を設定した場合、必須。
     *     「クライアント認証方式」にclient_secret_post(パラメーター認証)を設定した場合、設定不要。 (optional)
     * @param progressListener Progress listener
     * @param progressRequestListener Progress request listener
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     */
    public com.squareup.okhttp.Call tokenUsingPOSTCall(
            String contentType,
            TokenRequest body,
            String authorization,
            final ProgressResponseBody.ProgressListener progressListener,
            final ProgressRequestBody.ProgressRequestListener progressRequestListener)
            throws ApiException {
        Object localVarPostBody = body;

        // create path and map variables
        String localVarPath = "/token";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        if (contentType != null)
            localVarHeaderParams.put("Content-Type", apiClient.parameterToString(contentType));
        if (authorization != null)
            localVarHeaderParams.put("Authorization", apiClient.parameterToString(authorization));

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();
        localVarFormParams.put("grant_type", body.getGrantType());
        localVarFormParams.put("code", body.getCode());
        localVarFormParams.put("refresh_token", body.getRefreshToken());
        localVarFormParams.put("redirect_uri", body.getRedirectUri());
        localVarFormParams.put("client_id", body.getClientId());
        localVarFormParams.put("client_secret", body.getClientSecret());

        final String[] localVarAccepts = {"application/json;charset=UTF-8"};
        final String localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) localVarHeaderParams.put("Accept", localVarAccept);

        if (progressListener != null) {
            apiClient
                    .getHttpClient()
                    .networkInterceptors()
                    .add(
                            new com.squareup.okhttp.Interceptor() {
                                @Override
                                public com.squareup.okhttp.Response intercept(
                                        com.squareup.okhttp.Interceptor.Chain chain)
                                        throws IOException {
                                    com.squareup.okhttp.Response originalResponse =
                                            chain.proceed(chain.request());
                                    return originalResponse
                                            .newBuilder()
                                            .body(
                                                    new ProgressResponseBody(
                                                            originalResponse.body(),
                                                            progressListener))
                                            .build();
                                }
                            });
        }

        String[] localVarAuthNames = new String[] {};
        return apiClient.buildCall(
                localVarPath,
                "POST",
                localVarQueryParams,
                localVarCollectionQueryParams,
                localVarPostBody,
                localVarHeaderParams,
                localVarFormParams,
                localVarAuthNames,
                progressRequestListener);
    }

    @SuppressWarnings("rawtypes")
    private com.squareup.okhttp.Call tokenUsingPOSTValidateBeforeCall(
            String contentType,
            TokenRequest body,
            String authorization,
            final ProgressResponseBody.ProgressListener progressListener,
            final ProgressRequestBody.ProgressRequestListener progressRequestListener)
            throws ApiException {
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            ExecutableValidator executableValidator = factory.getValidator().forExecutables();

            Object[] parameterValues = {contentType, body, authorization};
            Method method =
                    this.getClass()
                            .getMethod(
                                    "tokenUsingPOSTWithHttpInfo",
                                    String.class,
                                    TokenRequest.class,
                                    String.class);
            Set<ConstraintViolation<TokenApi>> violations =
                    executableValidator.validateParameters(this, method, parameterValues);

            if (violations.size() == 0) {
                com.squareup.okhttp.Call call =
                        tokenUsingPOSTCall(
                                contentType,
                                body,
                                authorization,
                                progressListener,
                                progressRequestListener);
                return call;

            } else {
                throw new BeanValidationException((Set) violations);
            }
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
            throw new ApiException(e.getMessage());
        } catch (SecurityException e) {
            e.printStackTrace();
            throw new ApiException(e.getMessage());
        }
    }

    /**
     * アクセストークン発行 認可エンドポイントで取得した認可コードを用いたアクセストークンの取得及びリフレッシュトークンを用いたアクセストークンの更 新を行うためのエンドポイント
     *
     * @param contentType application/x-www-form-urlencodedに固定 (required)
     * @param body HTTPリクエストボディ (required)
     * @param authorization クライアント認証用のBasic認証値。
     *     (クライアントIDとクライアントシーレットを:(コロン)で連結し、Base64エンコードしたものを設定。)
     *     事前に登録する「クライアント認証方式」にclient_secret_basic(ベーシック認証)を設定した場合、必須。
     *     「クライアント認証方式」にclient_secret_post(パラメーター認証)を設定した場合、設定不要。 (optional)
     * @return TokenResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the
     *     response body
     */
    public TokenResponse tokenUsingPOST(String contentType, TokenRequest body, String authorization)
            throws ApiException {
        ApiResponse<TokenResponse> resp =
                tokenUsingPOSTWithHttpInfo(contentType, body, authorization);
        return resp.getData();
    }

    /**
     * アクセストークン発行 認可エンドポイントで取得した認可コードを用いたアクセストークンの取得及びリフレッシュトークンを用いたアクセストークンの更 新を行うためのエンドポイント
     *
     * @param contentType application/x-www-form-urlencodedに固定 (required)
     * @param body HTTPリクエストボディ (required)
     * @param authorization クライアント認証用のBasic認証値。
     *     (クライアントIDとクライアントシーレットを:(コロン)で連結し、Base64エンコードしたものを設定。)
     *     事前に登録する「クライアント認証方式」にclient_secret_basic(ベーシック認証)を設定した場合、必須。
     *     「クライアント認証方式」にclient_secret_post(パラメーター認証)を設定した場合、設定不要。 (optional)
     * @return ApiResponse&lt;TokenResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the
     *     response body
     */
    public ApiResponse<TokenResponse> tokenUsingPOSTWithHttpInfo(
            String contentType, TokenRequest body, String authorization) throws ApiException {
        com.squareup.okhttp.Call call =
                tokenUsingPOSTValidateBeforeCall(contentType, body, authorization, null, null);
        Type localVarReturnType = new TypeToken<TokenResponse>() {}.getType();
        return apiClient.execute(call, localVarReturnType);
    }
}
