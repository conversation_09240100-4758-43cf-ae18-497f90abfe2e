package point.common.event.listener;

import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import point.common.event.ChoiceActivityTotalChangedEvent;
import point.common.model.response.websocket.ChoiceActivityDataWrapper;
import point.common.service.ChoiceActivityTotalService;

@Slf4j
@Component
@RequiredArgsConstructor
public class ChoiceActivityListener {

    private final ChoiceActivityTotalService choiceActivityTotalService;

    @Async
    @EventListener
    public void handleAssetChanged(ChoiceActivityTotalChangedEvent event) {
        if (Objects.isNull(event.getSource())) {
            return;
        }

        if (event.getSource() instanceof ChoiceActivityDataWrapper dw) {
            choiceActivityTotalService.handleTotal(dw);
        }
    }
}
