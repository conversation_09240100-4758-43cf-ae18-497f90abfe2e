package point.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class XmlUtil {

    private static final XmlMapper OBJECT_MAPPER = new XmlMapper();

    public static String encode(Object value) {
        try {
            return OBJECT_MAPPER.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            log.error("Failed to encode object to XML: {}", e.getMessage(), e);
            return "";
        }
    }

    public static <T> T decode(String xml, Class<T> clazz) {
        if (StringUtils.isEmpty(xml)) {
            return null;
        }

        try {
            return OBJECT_MAPPER.readValue(xml, clazz);
        } catch (Exception e) {
            log.error("Failed to decode XML to object: {}", e.getMessage(), e);
            return null;
        }
    }
}
