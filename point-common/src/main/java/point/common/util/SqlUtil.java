package point.common.util;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.persistence.*;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.repository.query.EscapeCharacter;
import org.springframework.util.CollectionUtils;

public class SqlUtil {
    private SqlUtil() {} // no usages

    private static final String COMMA = ","; // 12 usages
    private static final String ANTI_POINT = ";"; // 4 usages
    private static final String SINGLE_QUOTES = "'"; // 10 usages
    private static final String LEFT_SQUARE_BRACKET = "["; // 4 usages
    private static final String RIGHT_SQUARE_BRACKET = "]"; // 4 usages
    private static final String LEFT_BRACKET_WITH_SPACE = " ("; // 3 usages
    private static final String RIGHT_BRACKET_WITH_SPACE = ")"; // 3 usages

    private static final String INSERT_PREFIX = "INSERT INTO"; // 1 usage
    private static final String INSERT_VALUES = " VALUES"; // 1 usage
    private static final String GET_METHOD_NAME_PREFIX = "get"; // 2 usages

    private static final String NULL_STRING = "null"; // 2 usages
    private static final int BATCH_NUMBER = 1000; // 3 usages

    private static final EscapeCharacter escapeCharacter = EscapeCharacter.DEFAULT;

    public static <T> int batchInsert(Class<T> clazz, List<T> list, EntityManager EM) {
        return batchInsert(clazz, list, BATCH_NUMBER, EM);
    }

    public static <T> int batchInsert(
            Class<?> clazz, List<?> list, int batchNumber, EntityManager EM) {

        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        batchNumber = batchNumber == 0 ? BATCH_NUMBER : batchNumber;
        Table table = clazz.getAnnotation(Table.class);

        String tableName = table.name();

        Field[] declaredFields = clazz.getDeclaredFields();
        List<Field> fields =
                Arrays.stream(declaredFields)
                        .filter(
                                field -> {
                                    if (Objects.nonNull(field.getAnnotation(Id.class))) {
                                        return false;
                                    }
                                    Column column = field.getAnnotation(Column.class);
                                    if (Objects.nonNull(column)) {
                                        return true;
                                    }
                                    JoinColumn joinColumn = field.getAnnotation(JoinColumn.class);
                                    return Objects.nonNull(joinColumn) && joinColumn.insertable();
                                })
                        .collect(Collectors.toList());

        List<String> tableFields =
                fields.stream()
                        .map(
                                field -> {
                                    Column annotation = field.getAnnotation(Column.class);
                                    return StringUtils.isNotBlank(annotation.name())
                                            ? annotation.name()
                                            : field.getName();
                                })
                        .collect(Collectors.toList());
        StringBuilder sbp =
                new StringBuilder(INSERT_PREFIX)
                        .append(" ")
                        .append(tableName)
                        .append(LEFT_BRACKET_WITH_SPACE);
        tableFields.forEach(
                f -> {
                    if (f.startsWith(LEFT_SQUARE_BRACKET) && f.endsWith(RIGHT_SQUARE_BRACKET)) {
                        f = f.replace(LEFT_SQUARE_BRACKET, ANTI_POINT);
                        f = f.replace(RIGHT_SQUARE_BRACKET, ANTI_POINT);
                    }
                    sbp.append(f).append(COMMA);
                });

        String presql =
                sbp.deleteCharAt(sbp.lastIndexOf(COMMA))
                        .append(RIGHT_BRACKET_WITH_SPACE)
                        .append(INSERT_VALUES)
                        .toString();

        List<String> sqlList = getBatchInsertSql(list, presql, batchNumber, fields, clazz);
        AtomicInteger row = new AtomicInteger();
        sqlList.forEach(
                sql -> {
                    Query insert = EM.createNativeQuery(sql);
                    row.addAndGet(insert.executeUpdate());
                });
        return row.get();
    }

    private static <T> List<String> getBatchInsertSql(
            List<?> list, String preSql, int batchNumber, List<Field> fields, Class<?> clazz) {
        List<String> sqlList = new ArrayList<>();
        if (list.size() > batchNumber) {
            ListUtils.partition(list, batchNumber)
                    .forEach(
                            batchList ->
                                    sqlList.add(getInsertSql(batchList, preSql, fields, clazz)));
        } else {
            sqlList.add(getInsertSql(list, preSql, fields, clazz));
        }
        return sqlList;
    }

    private static <I> String getInsertSql(
            List<?> list, String preSql, List<Field> fields, Class<?> clazz) {
        StringBuilder valuesSql = new StringBuilder(preSql);
        list.forEach(
                obj -> {
                    valuesSql.append(LEFT_BRACKET_WITH_SPACE);
                    fields.forEach(
                            field -> {
                                String fieldName = field.getName();
                                String getMethodName =
                                        GET_METHOD_NAME_PREFIX
                                                + fieldName.substring(0, 1).toUpperCase()
                                                + fieldName.substring(1);
                                try {
                                    Method getMethod = clazz.getMethod(getMethodName);
                                    Object y = getMethod.invoke(obj);
                                    if (Objects.isNull(y)
                                            || NULL_STRING.equalsIgnoreCase(String.valueOf(y))
                                            || BooleanUtils.TRUE.equalsIgnoreCase(String.valueOf(y))
                                            || BooleanUtils.FALSE.equalsIgnoreCase(
                                                    String.valueOf(y))) {
                                        valuesSql.append(y).append(COMMA);
                                        return;
                                    }
                                    if (y instanceof Date localDate) {
                                        y = LocalDate2Integer(localDate);
                                    }
                                    if (y instanceof String stringValue) {
                                        valuesSql
                                                .append(SINGLE_QUOTES)
                                                .append(
                                                        escapeCharacter.escape(
                                                                stringValue)) // mainly escape the
                                                // backslash to
                                                // prevent errors in
                                                // entering the DB
                                                .append(SINGLE_QUOTES)
                                                .append(COMMA);
                                    } else {
                                        valuesSql
                                                .append(SINGLE_QUOTES)
                                                .append(y)
                                                .append(SINGLE_QUOTES)
                                                .append(COMMA);
                                    }
                                } catch (NoSuchMethodException
                                        | InvocationTargetException
                                        | IllegalAccessException e) {
                                    String message =
                                            String.format(
                                                    "Fetching or executing '%s' failed",
                                                    getMethodName);
                                    throw new RuntimeException(message);
                                }
                            });
                    valuesSql
                            .deleteCharAt(valuesSql.lastIndexOf(COMMA))
                            .append(RIGHT_BRACKET_WITH_SPACE)
                            .append(COMMA);
                });
        return valuesSql.deleteCharAt(valuesSql.lastIndexOf(COMMA)).toString();
    }

    public static String LocalDate2Integer(Date localDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        return sdf.format(localDate);
    }
}
