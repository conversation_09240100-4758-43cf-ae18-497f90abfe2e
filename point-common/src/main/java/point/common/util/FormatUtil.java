package point.common.util;

import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.WordUtils;

public class FormatUtil {

    public enum FormatPattern {
        YYMMDDHHMM("yyMMddHHmm"),
        YYYY("yyyy"),
        YYYYMMDD("yyyyMMdd"),
        YYYYMMDDHHMM("yyyyMMddHHmm"),
        YYYYMMDDHHMMSS("yyyyMMddHHmmss"),
        MMDD("MMdd"),
        YYYY_MM("yyyy_MM"),
        YYYY_MM_DD("yyyy-MM-dd"),
        YYYY_MM_DD_SLASH("yyyy/MM/dd"),
        YYYY_MM_DD_HH_MM("yyyy-MM-dd HH:mm"),
        YYYY_MM_DD_HH_MM_SLASH("yyyy/MM/dd HH:mm"),
        YYYY_MM_DDTHH_MM("yyyy-MM-dd'T'HH:mm"),
        YYYY_MM_DD_HH_MM_SS("yyyy-MM-dd HH:mm:ss"),
        YYYY_MM_DD_HH_MM_SS_SLASH("yyyy/MM/dd HH:mm:ss"),
        YYYY_MM_DD_HH_MM_SS_S("yyyy-MM-dd HH:mm:ss.S"),
        YYYY_MM_DDTHH_MM_SSZ("yyyy-MM-dd'T'HH:mm:ss'Z'"),
        YYYY_MM_DDTHH_MM_SS_SZ("yyyy-MM-dd'T'HH:mm:ss.S'Z'"),
        YYYY_MM_DDTHH_MM_SS_SSSZ("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
        DATE_FORMAT_YYYY_T_MM_DD_HH_MM_SS_900("YYYY-MM-dd\'T\'HH:mm:ss+09:00"),
        EEE_MMM_DD_YYYY_HH_MM_SS("EEE MMM dd yyyy HH:mm:ss", Locale.ENGLISH),
        YYYY_MM_DD_MAIL("yyyy年MM月dd日"),
        YYYYMM("yyyyMM");
        private final String pattern;

        private final Locale locale;

        FormatPattern(String pattern, Locale locale) {
            this.pattern = pattern;
            this.locale = locale;
        }

        FormatPattern(String pattern) {
            this(pattern, null);
        }

        private SimpleDateFormat createSimpleDateFormat() {
            return locale != null
                    ? new SimpleDateFormat(pattern, locale)
                    : new SimpleDateFormat(pattern);
        }

        private SimpleDateFormat createSimpleDateFormatJst() {
            SimpleDateFormat simpleDateFormat =
                    locale != null
                            ? new SimpleDateFormat(pattern, locale)
                            : new SimpleDateFormat(pattern);
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Tokyo"));
            return simpleDateFormat;
        }
    }

    private static NumberFormat NUMBER_FORMAT = NumberFormat.getInstance();

    public static String format(Date date, FormatPattern formatPattern) {
        if (date == null) {
            return "";
        }

        return formatPattern.createSimpleDateFormat().format(date);
    }

    public static String formatJst(Date date, FormatPattern formatPattern) {
        if (date == null) {
            return "";
        }

        return formatPattern.createSimpleDateFormatJst().format(date);
    }

    public static Date parse(String source, FormatPattern formatPattern) {
        try {
            SimpleDateFormat sdf = formatPattern.createSimpleDateFormat();
            sdf.setLenient(false);
            return sdf.parse(source);
        } catch (ParseException e) {
            return null;
        }
    }

    public static Date parseJst(String source, FormatPattern formatPattern) {
        try {
            SimpleDateFormat sdf = formatPattern.createSimpleDateFormatJst();
            sdf.setLenient(false);
            return sdf.parse(source);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String getBeanName(Class<?> clazz) {
        return clazz.getSimpleName().substring(0, 1).toLowerCase()
                + clazz.getSimpleName().substring(1);
    }

    public static String formatSpacePadding(int unit, String source) {
        return String.format("%" + unit + "s", source == null ? "" : source);
    }

    public static String formatZeroPadding(int unit, String source) {
        return String.format("%0" + unit + "d", source);
    }

    public static String formatZeroPadding(int unit, long source) {
        return String.format("%0" + unit + "d", source);
    }

    public static String formatCamelToSnake(String source) {
        return StringUtils.join(StringUtils.splitByCharacterTypeCamelCase(source), "_");
    }

    public static String formatSnakeToCamel(String source) {
        return StringUtils.remove(WordUtils.capitalizeFully(source, '_'), "_");
    }

    public static String formatPhoneNumber(String callingCode, String phoneNumber) {
        return callingCode + phoneNumber.substring(1);
    }

    public static String formatThousandsSeparator(String source) {
        // 12345.6789 -> 12,345.6789
        var sources = source.split("\\.");
        return sources.length < 2
                ? NUMBER_FORMAT.format(Long.valueOf(source))
                : NUMBER_FORMAT.format(Long.valueOf(sources[0])) + "." + sources[1];
    }
}
