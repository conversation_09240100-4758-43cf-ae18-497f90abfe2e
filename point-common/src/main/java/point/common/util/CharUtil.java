package point.common.util;

import org.apache.commons.lang3.StringUtils;

public class CharUtil {

    public static String toDbc(String text) {
        if (StringUtils.isBlank(text)) {
            return StringUtils.EMPTY;
        }
        char[] chars = text.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if (chars[i] == 12288) {
                chars[i] = (char) 32;
                continue;
            }
            if (chars[i] > 65280 && chars[i] < 65375) {
                chars[i] = (char) (chars[i] - 65248);
            }
        }
        return new String(chars);
    }
}
