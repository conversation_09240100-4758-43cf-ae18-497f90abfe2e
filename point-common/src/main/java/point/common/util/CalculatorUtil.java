package point.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class CalculatorUtil {

    public static BigDecimal HUNDRED = BigDecimal.valueOf(100);

    public static BigDecimal divide(BigDecimal numerator, BigDecimal denominator) {
        return numerator.divide(denominator, 20, RoundingMode.HALF_UP);
    }

    public static BigDecimal divide(
            BigDecimal numerator, BigDecimal denominator, RoundingMode roundingMode) {
        return numerator.divide(denominator, 20, roundingMode);
    }

    public static BigDecimal divide(
            BigDecimal numerator, BigDecimal denominator, int scale, RoundingMode roundingMode) {
        return numerator.divide(denominator, scale, roundingMode);
    }

    public static BigDecimal toPercent(BigDecimal numerator, BigDecimal denominator) {
        return divide(numerator.multiply(HUNDRED), denominator);
    }

    public static BigDecimal toPercent(
            BigDecimal numerator, BigDecimal denominator, int scale, RoundingMode roundingMode) {
        return divide(numerator.multiply(HUNDRED), denominator, scale, roundingMode);
    }

    public static BigDecimal calculatePercentage(BigDecimal percent) {
        return divide(percent, HUNDRED);
    }

    public static BigDecimal calculatePercentage(BigDecimal source, BigDecimal percent) {
        return divide(source.multiply(percent), HUNDRED);
    }

    public static BigDecimal calculatePercentage(
            BigDecimal source, BigDecimal percent, int scale, RoundingMode roundingMode) {
        return divide(source.multiply(percent), HUNDRED, scale, roundingMode);
    }
}
