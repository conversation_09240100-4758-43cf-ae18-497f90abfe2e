package point.common.ponta;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.net.ssl.SSLContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.xml.MappingJackson2XmlHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import point.common.config.PontaConfig;
import point.common.constant.ErrorCode;
import point.common.exception.CustomException;
import point.common.util.EnvironmentUtil;

@Slf4j
@Component
public class PontaRequestUtil {

    private static final String SUCCESS_CODE_VALUE = "N000000000";
    private static final String RESPONSE_CODE_KEY = "RE0004_tl";
    private static final String ACCESS_TOKEN_KEY = "AT0010_tl";
    private static final String MEMBER_ID_KEY = "MI0002_tl";

    private final XmlMapper xmlMapper = new XmlMapper();

    private final PontaConfig pontaConfig;
    private final RestTemplate restTemplate;

    public PontaRequestUtil(Environment environment, PontaConfig pontaConfig) {
        this.pontaConfig = pontaConfig;
        String activeProfile = EnvironmentUtil.getActiveProfileOrDefault(environment);
        try {
            String password =
                    new String(
                            pontaConfig.getKeystorePassword().getBytes(StandardCharsets.UTF_8),
                            StandardCharsets.UTF_8);

            KeyStore keyStore = KeyStore.getInstance("JKS");
            String formattedKeyStoreName =
                    String.format("ponta/ponta-web-keystore-%s.jks", activeProfile);
            log.info("Loading keystore: {}", formattedKeyStoreName);
            ClassPathResource resource = new ClassPathResource(formattedKeyStoreName);

            keyStore.load(resource.getInputStream(), password.toCharArray());

            SSLContext sslContext =
                    SSLContextBuilder.create()
                            .loadKeyMaterial(keyStore, password.toCharArray())
                            .build();

            SSLConnectionSocketFactory sslConnectionSocketFactory =
                    new SSLConnectionSocketFactory(sslContext);

            CloseableHttpClient httpClient =
                    HttpClients.custom().setSSLSocketFactory(sslConnectionSocketFactory).build();

            HttpComponentsClientHttpRequestFactory requestFactory =
                    new HttpComponentsClientHttpRequestFactory();
            requestFactory.setHttpClient(httpClient);

            this.restTemplate = new RestTemplate(requestFactory);

            List<HttpMessageConverter<?>> messageConverters =
                    new ArrayList<>(this.restTemplate.getMessageConverters());
            messageConverters.add(new MappingJackson2XmlHttpMessageConverter());
            this.restTemplate.setMessageConverters(messageConverters);

        } catch (UnrecoverableKeyException
                | CertificateException
                | NoSuchAlgorithmException
                | KeyManagementException
                | KeyStoreException
                | IOException e) {
            log.error("Failed to create RestTemplate with custom SSL configuration", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * get accessToken
     *
     * @param secretCode the secret code that returned by callback
     * @return the accessToken
     * @throws CustomException
     */
    public String if1507(String secretCode) throws CustomException {
        // Log the start of the process
        log.info("Starting if1507 request to get accessToken for secretCode: {}", secretCode);

        // Build request body
        RequestBodyUtil.Rpc requestBody =
                RequestBodyUtil.if1507(pontaConfig.getCredentials(), secretCode);
        String paramsXml = RequestBodyUtil.Rpc.rpcToXml(requestBody);

        if (log.isDebugEnabled()) {
            log.debug("Request body for if1507: {}", paramsXml);
        }

        // Set headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("DATA", paramsXml);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);

        // Send request
        String response;
        try {
            log.info("Sending if1507 request to Ponta for secretCode: {}", secretCode);
            response =
                    restTemplate.postForObject(
                            pontaConfig.getIf1507Url(), requestEntity, String.class);
            log.info("Received response from if1507 request for secretCode: {}", secretCode);
            if (log.isDebugEnabled()) {
                log.debug("Raw response from if1507: {}", response);
            }
        } catch (Exception e) {
            log.error(
                    "Failed to send if1507 request to Ponta for secretCode: {}, error: {}",
                    secretCode,
                    e.getMessage(),
                    e);
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_INTERACT_FAILED);
        }

        // Parse response
        PontaResponseUtil.Rpc rpc;
        try {
            rpc = xmlMapper.readValue(response, PontaResponseUtil.Rpc.class);
            if (log.isDebugEnabled()) {
                log.debug("Parsed response from if1507: {}", rpc);
            }
        } catch (Exception e) {
            log.error(
                    "Failed to parse XML response from if1507 for secretCode: {}, response: {}, error: {}",
                    secretCode,
                    response,
                    e.getMessage(),
                    e);
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_GET_ACCESS_TOKEN_FAILED);
        }

        // Validate response
        if (Objects.isNull(rpc)) {
            log.error("Invalid XML response from if1507 for secretCode: {}", secretCode);
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_GET_ACCESS_TOKEN_FAILED);
        }

        Map<String, String> responseMap = rpc.getParamsAsMap();
        if (!this.isSuccess(responseMap)) {
            log.error(
                    "Failed to get accessToken from if1507 for secretCode: {}, errorCode: {}",
                    secretCode,
                    responseMap.get(RESPONSE_CODE_KEY));
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_GET_ACCESS_TOKEN_FAILED);
        }
        String accessToken = responseMap.get(ACCESS_TOKEN_KEY);
        log.info("Successfully retrieved accessToken from if1507 for secretCode: {}", secretCode);
        if (log.isDebugEnabled()) {
            log.debug("AccessToken: {}", accessToken);
        }
        return accessToken;
    }

    /**
     * get memberId
     *
     * @param accessToken the accessToken that issued by ponta
     * @return the user memberId
     * @throws CustomException
     */
    public String if1511(String accessToken) throws CustomException {

        log.info("Starting if1511 request to get memberId for accessToken: {}", accessToken);

        // Build request body
        RequestBodyUtil.Rpc requestBody =
                RequestBodyUtil.if1511(pontaConfig.getCredentials(), accessToken);
        String paramsXml = RequestBodyUtil.Rpc.rpcToXml(requestBody);

        if (log.isDebugEnabled()) {
            log.debug("Request body for if1511: {}", paramsXml);
        }

        // Set headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("DATA", paramsXml);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);

        // Send request
        String response;
        try {
            log.info("Sending if1511 request to Ponta for accessToken: {}", accessToken);
            response =
                    restTemplate.postForObject(
                            pontaConfig.getIf1511Url(), requestEntity, String.class);
            log.info("Received response from if1511 request for accessToken: {}", accessToken);
            if (log.isDebugEnabled()) {
                log.debug("Raw response from if1511: {}", response);
            }
        } catch (Exception e) {
            log.error(
                    "Failed to send if1511 request to Ponta for accessToken: {}, error: {}",
                    accessToken,
                    e.getMessage(),
                    e);
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_INTERACT_FAILED);
        }

        // Parse response
        PontaResponseUtil.Rpc rpc;
        try {
            rpc = xmlMapper.readValue(response, PontaResponseUtil.Rpc.class);
            if (log.isDebugEnabled()) {
                log.debug("Parsed response from if1511: {}", rpc);
            }
        } catch (Exception e) {
            log.error(
                    "Failed to parse XML response from if1511 for accessToken: {}, response: {}, error: {}",
                    accessToken,
                    response,
                    e.getMessage(),
                    e);
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_GET_MEMBER_ID_FAILED);
        }

        // Validate response
        if (Objects.isNull(rpc)) {
            log.error("Invalid XML response from if1511 for accessToken: {}", accessToken);
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_GET_MEMBER_ID_FAILED);
        }

        Map<String, String> responseMap = rpc.getParamsAsMap();
        if (!this.isSuccess(responseMap)) {
            log.error(
                    "Failed to get memberId from if1511 for accessToken: {}, errorCode: {}",
                    accessToken,
                    responseMap.get(RESPONSE_CODE_KEY));
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_GET_MEMBER_ID_FAILED);
        }
        String memberId = responseMap.get(MEMBER_ID_KEY);
        log.info("Successfully retrieved memberId from if1511 for accessToken: {}", accessToken);
        if (log.isDebugEnabled()) {
            log.debug("MemberId: {}", memberId);
        }
        return memberId;
    }

    private boolean isSuccess(Map<String, String> responseMap) {
        if (CollectionUtils.isEmpty(responseMap)) {
            return false;
        }
        return SUCCESS_CODE_VALUE.equals(responseMap.get(RESPONSE_CODE_KEY));
    }
}
