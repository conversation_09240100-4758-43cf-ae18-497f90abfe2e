package point.common.pdf;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.ExceptionConverter;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfPageEventHelper;
import com.itextpdf.text.pdf.PdfTemplate;
import com.itextpdf.text.pdf.PdfWriter;
import java.io.IOException;
import org.springframework.core.io.ClassPathResource;

public class PdfPageXofYEventHelper extends PdfPageEventHelper {
    public PdfTemplate total;

    public BaseFont baseFont;

    @Override
    public void onOpenDocument(PdfWriter writer, Document document) {
        total = writer.getDirectContent().createTemplate(500, 500);
        try {
            ClassPathResource cpr = new ClassPathResource("font/MS-PGothic.ttf");
            baseFont =
                    BaseFont.createFont(cpr.getPath(), BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            throw new ExceptionConverter(e);
        }
    }

    @Override
    public void onEndPage(PdfWriter writer, Document document) {
        PdfContentByte pdfContentByte = writer.getDirectContent();
        pdfContentByte.saveState();
        String text = "- " + writer.getPageNumber() + "/";
        float textSize = baseFont.getWidthPoint(text, 11);
        pdfContentByte.beginText();
        pdfContentByte.setFontAndSize(baseFont, 11);

        float x = document.left() + (document.right() - document.left()) / 2;
        float y = 20f;
        pdfContentByte.setTextMatrix(x, y);
        pdfContentByte.showText(text);
        pdfContentByte.endText();
        pdfContentByte.addTemplate(total, x + textSize, y);
        pdfContentByte.restoreState();
    }

    @Override
    public void onCloseDocument(PdfWriter writer, Document document) {
        total.beginText();
        try {
            ClassPathResource cpr = new ClassPathResource("font/MS-PGothic.ttf");
            baseFont =
                    BaseFont.createFont(cpr.getPath(), BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            total.setFontAndSize(baseFont, 11);
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        total.setTextMatrix(0, 0);
        total.showText(String.valueOf(writer.getPageNumber() - 1) + " -");
        total.endText();
    }
}
