package point.common.http.whalefin;

import java.net.HttpURLConnection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;
import point.common.config.WhaleFinConfig;
import point.common.http.cb.HttpClient;
import point.common.util.JsonUtil;

@Slf4j
@Component
@RequiredArgsConstructor
public class WhaleFinClient {

    public static final String POST_METHOD = "POST";
    public static final String GET_METHOD = "GET";
    private static final int TOO_MANY_REQUESTS = 429;
    private static HmacUtils hmacUtils;

    private final WhaleFinConfig whaleFinConfig;

    @PostConstruct
    public void setAccessSecret() {
        if (Objects.nonNull(whaleFinConfig) && Objects.nonNull(whaleFinConfig.getAccessSecret())) {
            WhaleFinClient.hmacUtils =
                    new HmacUtils("HmacSha256", whaleFinConfig.getAccessSecret());
        } else {
            log.warn("[WhaleFin-Config] accessSecret is null");
        }
    }

    public Optional<GetOrderResult> getOrder(String requestId, String path) {
        return executeRequest(requestId, GET_METHOD, path, null, this::handleGetOrderResponse);
    }

    public Optional<CreateOrderResult> createOrder(String requestId, String path, String payload) {
        return executeRequest(
                requestId, POST_METHOD, path, payload, this::handleCreateOrderResponse);
    }

    private <T> Optional<T> executeRequest(
            String requestId,
            String method,
            String path,
            String payload,
            Function<HttpClient.HttpResult, Optional<T>> responseHandler) {

        final long startTime = System.currentTimeMillis();
        try {
            long timestamp = System.currentTimeMillis();
            String signStr = this.buildSignString(method, path, timestamp, payload);
            List<String> headers =
                    this.buildHeaders(whaleFinConfig.getAccessKey(), timestamp, signStr);

            log.info(
                    """
                            [WhaleFin-Request] requestId={} method={} path={}\s
                            headers={} payload={}""",
                    requestId,
                    method,
                    path,
                    headers,
                    payload);

            String requestUrl = whaleFinConfig.getApiHost() + path;
            HttpClient.HttpResult result =
                    POST_METHOD.equals(method)
                            ? HttpClient.httpPost(requestUrl, headers, payload)
                            : HttpClient.httpGet(requestUrl, headers, null);

            log.info(
                    """
                            [WhaleFin-Response] requestId={} code={} latency={}ms\s
                            response={}""",
                    requestId,
                    result.code,
                    System.currentTimeMillis() - startTime,
                    result.content);

            return responseHandler.apply(result);
        } catch (Exception e) {
            log.error(
                    """
                            [WhaleFin-Error] requestId={} method={} path={}\s
                            error={} stacktrace={}""",
                    requestId,
                    method,
                    path,
                    e.getMessage(),
                    ExceptionUtils.getStackTrace(e));
            return Optional.empty();
        }
    }

    private Optional<GetOrderResult> handleGetOrderResponse(HttpClient.HttpResult result) {
        return handleResponse(result, GetOrderResponse.class).map(GetOrderResponse::getResult);
    }

    private Optional<CreateOrderResult> handleCreateOrderResponse(HttpClient.HttpResult result) {
        return handleResponse(result, CreateOrderResponse.class)
                .map(CreateOrderResponse::getResult);
    }

    private <T> Optional<T> handleResponse(HttpClient.HttpResult result, Class<T> responseType) {
        if (isHttpError(result.code)) {
            log.warn("[WhaleFin-ErrorResponse] code={} content={}", result.code, result.content);
            return Optional.empty();
        }

        try {
            T response = JsonUtil.decode(result.content, responseType);
            if (response == null) {
                log.warn("[WhaleFin-InvalidResponse] content={}", result.content);
                return Optional.empty();
            }
            return Optional.of(response);
        } catch (Exception e) {
            log.error(
                    """
                            [WhaleFin-ParseError] content={} error={}""",
                    result.content,
                    e.getMessage());
            return Optional.empty();
        }
    }

    private String buildSignString(String method, String path, long timestamp, String payload) {
        String base = String.format("method=%s&path=%s&timestamp=%d", method, path, timestamp);
        return payload != null ? base + "&body=" + payload : base;
    }

    private List<String> buildHeaders(String accessKey, long timestamp, String sign) {
        return List.of(
                "access-key", accessKey,
                "access-timestamp", String.valueOf(timestamp),
                "access-sign", hmacUtils.hmacHex(sign));
    }

    private static boolean isHttpError(int code) {
        return switch (code) {
            case HttpURLConnection.HTTP_INTERNAL_ERROR,
                            HttpURLConnection.HTTP_BAD_REQUEST,
                            HttpURLConnection.HTTP_UNAUTHORIZED,
                            HttpURLConnection.HTTP_FORBIDDEN,
                            HttpURLConnection.HTTP_NOT_FOUND,
                            HttpURLConnection.HTTP_UNSUPPORTED_TYPE,
                            HttpURLConnection.HTTP_CLIENT_TIMEOUT,
                            TOO_MANY_REQUESTS ->
                    true;
            default -> false;
        };
    }
}
