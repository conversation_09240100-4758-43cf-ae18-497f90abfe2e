package point.common.http.whalefin;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateOrderResult {
    private String type; // 注文タイプ（例: "limit" または "market"）
    private String direction; // 売買方向（例: "buy" または "sell"）
    private String status; // 注文ステータス（例: "filled", "open", "canceled"）
    private String strategy; // 戦略名
    private String quantity; // 元の注文数量
    private String filledQuantity; // 成約済み数量
    private String id; // 注文ID
    private String symbol; // 取引ペア
    private String price; // 注文価格
    private FiatReference fiatReference; // フィアット参考情報
    private String baseQuantity; // 基軸通貨数量
    private String quoteQuantity; // 見積通貨数量
    private String filledPrice; // 成約価格
    private String filledBaseQuantity; // 成約基軸通貨数量
    private String filledQuoteQuantity; // 成約見積通貨数量
    private String fee; // 取引手数料
    private String feeCcy; // 手数料の通貨
    private String isQuote; // 見積通貨かどうか（"true" または "false"）
    private String createdAt; // 作成日時（ISO 8601フォーマット）
    private String updatedAt; // 更新日時（ISO 8601フォーマット）
}
