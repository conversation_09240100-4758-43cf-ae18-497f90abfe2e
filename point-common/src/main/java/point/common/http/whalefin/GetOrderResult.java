package point.common.http.whalefin;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

/**
 * @see CreateOrderResult
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetOrderResult {
    private String type;
    private String direction;
    private String status;
    private String strategy;
    private String quantity;
    private String filledQuantity;
    private String id;
    private Execution execution;
    @Nullable private FiatReference fiatReference;
    private String symbol;
    private String price;
    private String baseQuantity;
    private String quoteQuantity;
    private String filledPrice;
    private String filledBaseQuantity;
    private String filledQuoteQuantity;
    private String fee;
    private String feeCcy;
    private String isQuote;
    private String createdAt;
    private String updatedAt;
}
