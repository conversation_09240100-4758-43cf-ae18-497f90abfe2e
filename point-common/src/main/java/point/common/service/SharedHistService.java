package point.common.service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.constant.*;
import point.common.entity.ChoiceActivityRule;
import point.common.entity.SharedHist;
import point.common.entity.SharedHist_;
import point.common.model.request.RecordShareForm;
import point.common.model.response.GlobalApiResponse;
import point.common.predicate.SharedHistPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class SharedHistService extends EntityService<SharedHist, SharedHistPredicate> {

    @Autowired private ChoicePowerSyncService choicePowerSyncService;

    @Autowired private final ChoiceActivityRuleService choiceActivityRuleService;

    @Override
    public Class<SharedHist> getEntityClass() {
        return SharedHist.class;
    }

    public boolean isSharedTodayBy(
            List<Long> userIds,
            SharedRecordType sharedRecordType,
            SharedTargetType sharedTargetType) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                List<Predicate> predicates =
                                        Stream.of(
                                                        criteriaBuilder
                                                                .in(root.get(SharedHist_.USER_ID))
                                                                .value(userIds),
                                                        criteriaBuilder.equal(
                                                                root.get(
                                                                        SharedHist_
                                                                                .SHARED_RECORD_TYPE),
                                                                sharedRecordType),
                                                        criteriaBuilder.equal(
                                                                root.get(
                                                                        SharedHist_
                                                                                .SHARED_TARGET_TYPE),
                                                                sharedTargetType),
                                                        criteriaBuilder.equal(
                                                                root.get(SharedHist_.SHARED_DAY),
                                                                DateUtils.truncate(
                                                                        new Date(), Calendar.DATE)))
                                                .toList();
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        predicates);
                            }
                        });

        return count > 0L;
    }

    public GlobalApiResponse<Long> shareRecordAndJoinCampaign(
            RecordShareForm form, Long userId, SharedRecordType sharedRecordType) throws Exception {

        ChoiceActivityRuleEnum rule = sharedRecordType.getChoiceActivityRule();
        SharedHist hist =
                SharedHist.builder()
                        .recordId(form.getRecordId())
                        .sharedRecordType(sharedRecordType)
                        .userId(userId)
                        .sharedDay(DateUtils.truncate(new Date(), Calendar.DATE))
                        .sharedTargetType(SharedTargetType.TWITTER_X)
                        .ruleId(rule == null ? null : rule.getId())
                        .choiceActivityFunction(sharedRecordType.getChoiceActivityFunction())
                        .build();

        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindHasShared(criteriaBuilder, root, hist));
                            }
                        });

        GlobalApiResponse<Long> response =
                new GlobalApiResponse<>(HttpStatus.OK.value(), BizCode.SUCCESS.getMessage());
        response.setParams(0L);
        customTransactionManager.execute(
                entityManager -> {
                    // apply campaign
                    if (Objects.nonNull(rule) && count == 0L) {
                        ChoiceActivityRule ruleEntity =
                                choiceActivityRuleService.findOne(rule.getId());
                        Long amount = ruleEntity.isActive() ? ruleEntity.getPowerAmount() : 0L;
                        hist.setPowerAmount(amount);
                        // save
                        super.save(hist, entityManager);
                        choicePowerSyncService.savePowerByRule(hist.getUserId(), rule, null);
                        response.setParams(amount);
                        return;
                    }
                    hist.setChoiceActivityFunction(null);
                    hist.setRuleId(null);
                    hist.setPowerAmount(null);
                    super.save(hist, entityManager);
                });

        return response;
    }

    private List<Predicate> getPredicatesOfFindHasShared(
            CriteriaBuilder criteriaBuilder, Root<SharedHist> root, SharedHist hist) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(criteriaBuilder.equal(root.get(SharedHist_.userId), hist.getUserId()));
        predicates.add(
                criteriaBuilder.equal(
                        root.get(SharedHist_.sharedRecordType), hist.getSharedRecordType()));
        predicates.add(criteriaBuilder.equal(root.get(SharedHist_.sharedDay), hist.getSharedDay()));
        predicates.add(
                criteriaBuilder.equal(
                        root.get(SharedHist_.sharedTargetType), hist.getSharedTargetType()));
        return predicates;
    }
}
