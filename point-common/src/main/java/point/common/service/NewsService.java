package point.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.NewsType;
import point.common.constant.ViewVariables;
import point.common.entity.News;
import point.common.entity.News_;
import point.common.model.response.PageData;
import point.common.predicate.NewsPredicate;

@Service
public class NewsService extends EntityService<News, NewsPredicate> {

    @Override
    public Class<News> getEntityClass() {
        return News.class;
    }

    public List<News> findByCondition() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<News, List<News>>() {
                    @Override
                    public List<News> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.isEnabled(criteriaBuilder, root, true));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                ViewVariables.DEFAULT_NUMBER,
                                ViewVariables.DEFAULT_SIZE,
                                criteriaBuilder.desc(root.get(News_.date)));
                    }
                });
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<News> root,
            Long dateFrom,
            Long dateTo,
            NewsType newsType,
            Boolean enabled,
            Integer number,
            Integer size) {
        List<Predicate> predicates = new ArrayList<>();

        if (dateFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(
                            criteriaBuilder, root, new Date(dateFrom)));
        }

        if (dateTo != null) {
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
        }

        if (newsType != null) {
            predicates.add(predicate.equalNewsType(criteriaBuilder, root, newsType));
        }

        return predicates;
    }

    public PageData<News> findByConditionPageData(
            Long dateFrom, Long dateTo, NewsType newsType, Integer number, Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                dateFrom,
                                                dateTo,
                                                newsType,
                                                true,
                                                number,
                                                size));
                            }
                        });

        return new PageData<News>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<News, List<News>>() {
                            @Override
                            public List<News> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                dateFrom,
                                                dateTo,
                                                newsType,
                                                true,
                                                number,
                                                size);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(News_.id)));
                            }
                        }));
    }
}
