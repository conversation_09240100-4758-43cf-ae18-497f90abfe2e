/**
 * Copyright (c) 2019, toiware Inc.<br>
 * All rights reserved
 */
package point.common.aml;

import java.time.LocalDate;
import lombok.Builder;
import lombok.Value;
import point.common.constant.CustomerAttribute;

/** World Check One反社チェックリクエスト */
@Value
@Builder
public class WorldCheckOneReq {

    /** 反社チェック種別 (WC1側の種別, CaseEntityType) */
    private WorldCheckOneReqType type;

    /** 顧客属性 (<PERSON>側の種別) */
    private CustomerAttribute customerAttribute;

    /** 氏名 (INDIVIDUAL) */
    private String name;

    /** 生年月日 (INDIVIDUAL only) */
    private LocalDate birthday;

    private String gender;
}
