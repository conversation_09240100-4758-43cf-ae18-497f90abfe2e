package point.common.model.request;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

public class CurrencyPairConfigUpdateForm extends IdForm {
    @Getter @Setter @NotNull private BigDecimal minOrderAmount;

    @Getter @Setter @NotNull private BigDecimal maxOrderAmount;

    @Getter @Setter @NotNull private BigDecimal maxActiveOrderAmount;

    @Getter @Setter @NotNull private BigDecimal limitPriceRangeRate;

    @Getter @Setter @NotNull private BigDecimal marketPriceRangeRate;

    @Getter @Setter @NotNull private BigDecimal marketAmountRangeRate;

    @Getter @Setter @NotNull private BigDecimal makerTradeFeePercent;

    @Getter @Setter @NotNull private BigDecimal takerTradeFeePercent;

    @Getter @Setter @NotNull private BigDecimal simpleMarketSpreadPercent;

    @Getter @Setter @NotNull private BigDecimal simpleMarketFeePercent;

    @Getter @Setter @NotNull private boolean tradable;

    @Getter @Setter @NotNull private boolean enabled;

    @Getter @Setter private BigDecimal circuitBreakPercent;

    @Getter @Setter private Long circuitBreakCheckTimespan;

    @Getter @Setter private Long circuitBreakStopTimespan;

    @Getter @Setter private BigDecimal spikePercent;

    @Getter @Setter private Integer spikeMinutes;

    @Getter @Setter private Integer spikeCount;

    @Getter @Setter @NotNull private Integer washTradingCheckSpanHours;

    @Getter @Setter @NotNull private BigDecimal washTradingPercentThreshold;

    @Getter @Setter private Integer highValueTraderCheckSpanHours;

    @Getter @Setter private Integer highValueTraderCountThreshold;

    @Getter @Setter private BigDecimal posSpreadPercent;

    @Getter @Setter private BigDecimal posSlippagePercent;

    @Getter @Setter private BigDecimal highValueTraderPosOrderLimitAmountThreshold;

    @Getter @Setter private BigDecimal highValueTraderPosTradeMarketAmountThreshold;
}
