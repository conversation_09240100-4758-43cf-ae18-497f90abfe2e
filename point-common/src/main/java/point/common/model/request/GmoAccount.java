package point.common.model.request;

import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GmoAccount {
    private String raId; // 入金先の口座を識別するID
    private String raBranchCode; // 入金先口座の支店コード
    private String raBranchNameKana; // 入金先口座の支店名カナ
    private String raAccountNumber; // 入金先口座の口座番号
    private String raHolderName; // 入金先口座の口座名義（漢字）
    private String baseDate; // 応答日付、もしくは入金明細の基準日を示す
    private String baseTime; // 応答時刻、もしくは入金明細の基準時刻を示す ISO8601 時差(offset)も表記
}
