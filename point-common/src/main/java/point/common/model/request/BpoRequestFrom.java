package point.common.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class BpoRequestFrom {

    // 申請ID 申請に対して一意に振られるID
    @JsonProperty("applicant_id")
    private Long applicantId;

    // 連携ID 事業者が案件を特定するためのID
    @JsonProperty("reference_id")
    private String referenceId;

    // 本人確認書類
    // 1：運転免許証
    // 2：マイナンバーカード
    // 3：在留カード
    // 4：運転経歴証明書
    // 5：特別永住者証明書
    // 6：住民基本台帳カード
    // 7：保険証
    @JsonProperty("document_ype")
    private String documentType;

    // 受付日時 YYYY-MM-DDThh:mm:ss+09:00形式 申請APIを使用して申請を行った日時
    @JsonProperty("accepted_at")
    private String acceptedAt;

    // ステータス completed: 確認完了
    @JsonProperty("status")
    private String status;

    // 顔照合の有無 true:：eKYCかBPOのいずれかで顔照合 を実施している false：顔照合を実施していない
    @JsonProperty("require_face_match")
    private Boolean requireFaceMatch;

    // 顔照合以外の 確認結果 match, unmatch 目視による確認結果
    @JsonProperty("result_without_face_match")
    private String resultWithoutFaceMatch;

    // 顔照合を含む確認結果 match, unmatch
    // eKYCを加味した顔照合の結果を含む、
    // 最終確認結果
    // ※ 「7：保険証」または顔照合有無が
    // falseの場合は、顔照合結果が無いため、
    // 「顔照合以外の照合結果」と同じ値
    @JsonProperty("result")
    private String result;

    // 確認完了日時 YYYY-MM-DDThh:mm:ss+09:00形式
    @JsonProperty("completed_at")
    private String completedAt;

    // 確認エラー項目 不整合になった項目を返す
    @JsonProperty("verification_errors")
    private List<VerificationError> verificationErrors;

    @Getter
    @Setter
    public static class VerificationError {

        // エラー項目ID 不整合の項目を表すコード
        // ※エラーが無い場合はなし
        @JsonProperty("error_code")
        private String errorCode;

        // エラー項目内容 不整合の項目内容
        // ※エラーが無い場合はなし
        @JsonProperty("error_message")
        private String errorMessage;

        // Constructors
        public VerificationError() {}

        public VerificationError(String errorCode, String errorMessage) {
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }
    }
}
