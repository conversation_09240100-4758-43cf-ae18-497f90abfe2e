package point.common.model.request;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

public class BalanceNotifyConfigUpdateForm extends IdForm {

    @Getter @Setter @NotNull private BigDecimal defaultBalance;

    @Getter @Setter @NotNull private BigDecimal limitBalancePercent;

    @Getter @Setter @NotNull private String mailTo;

    @Getter @Setter @NotNull private boolean enabled;
}
