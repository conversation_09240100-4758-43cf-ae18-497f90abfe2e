package point.common.model.request;

import java.math.BigDecimal;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Currency;

@Getter
@Setter
@NoArgsConstructor
public class CurrencyConfigForm {

    private Long symbolId;

    @Enumerated(EnumType.STRING)
    private Currency currency;

    private BigDecimal percentage;
}
