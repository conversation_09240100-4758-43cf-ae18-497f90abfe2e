package point.common.model.request;

import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.NoticesType;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserMailNoticesForm {

    @NotNull private Long id;

    private Long userId;

    private NoticesType noticesType;

    @NotNull @Builder.Default private Boolean noticesEnabled = true;
}
