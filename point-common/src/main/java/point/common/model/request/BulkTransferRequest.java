package point.common.model.request;

import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/** BulkTransferRequest */
@Getter
@Setter
@Builder
public class BulkTransferRequest {
    private String accountId;

    private String remitterName;

    private String transferDesignatedDate;

    private String transferDateHolidayCode;

    private String transferDataName;

    private String totalCount;

    private String totalAmount;

    private String applyComment;

    private List<BulkTransfer> bulkTransfers;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class BulkTransferRequest {\n");

        sb.append("    accountId: ").append(toIndentedString(accountId)).append("\n");
        sb.append("    remitterName: ").append(toIndentedString(remitterName)).append("\n");
        sb.append("    transferDesignatedDate: ")
                .append(toIndentedString(transferDesignatedDate))
                .append("\n");
        sb.append("    transferDateHolidayCode: ")
                .append(toIndentedString(transferDateHolidayCode))
                .append("\n");
        sb.append("    transferDataName: ").append(toIndentedString(transferDataName)).append("\n");
        sb.append("    totalCount: ").append(toIndentedString(totalCount)).append("\n");
        sb.append("    totalAmount: ").append(toIndentedString(totalAmount)).append("\n");
        sb.append("    applyComment: ").append(toIndentedString(applyComment)).append("\n");
        sb.append("    bulkTransfers: ").append(toIndentedString(bulkTransfers)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
