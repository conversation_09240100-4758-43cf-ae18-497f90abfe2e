package point.common.model.request;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

public class CurrencyConfigUpdateForm extends IdForm {
    @Getter @Setter @NotNull private BigDecimal depositFee;

    @Getter @Setter @NotNull private BigDecimal withdrawalFee;

    @Getter @Setter @NotNull private BigDecimal transactionFee;

    @Getter @Setter @NotNull private BigDecimal maxOrderAmountPerDay;

    @Getter @Setter @NotNull private BigDecimal minDepositAmount;

    @Getter @Setter @NotNull private BigDecimal minWithdrawalAmount;

    @Getter @Setter @NotNull private boolean depositable;

    @Getter @Setter @NotNull private boolean withdrawable;

    @Getter @Setter @NotNull private boolean enabled;
}
