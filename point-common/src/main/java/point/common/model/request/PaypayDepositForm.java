package point.common.model.request;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

public class PaypayDepositForm {

    @Getter
    @Setter
    @NotNull
    @Length(min = 4, max = 4)
    private String HD_VL;

    @Getter
    @Setter
    @NotNull
    @Length(min = 2, max = 2)
    @Pattern(regexp = "RQ")
    private String HD_MsgClass;

    @Getter
    @Setter
    @NotNull
    @Length(min = 17, max = 17)
    private String HD_ReqDateTime;

    @Getter @Setter private String HD_RspDateTime;

    @Getter
    @Setter
    @NotNull
    @Length(min = 24, max = 24)
    public String HD_TranId;

    @Getter
    @Setter
    @NotNull
    @Length(min = 32, max = 32)
    public String HD_HashValue;

    @Getter @Setter public String HD_DetailCode;

    @Getter @Setter public String HD_CompCode;

    @Getter
    @Setter
    @NotNull
    @Length(min = 1, max = 1)
    public String DataKbn;

    @Getter
    @Setter
    @NotNull
    @Length(min = 6, max = 6)
    public String ShokaiNo;

    @Getter
    @Setter
    @NotNull
    @Length(min = 6, max = 6)
    public String KanjoDate;

    @Getter
    @Setter
    @NotNull
    @Length(min = 6, max = 6)
    public String KisanDate;

    @Getter
    @Setter
    @NotNull
    @Length(min = 10, max = 10)
    public String Amount;

    @Getter
    @Setter
    @NotNull
    @Length(min = 10, max = 10)
    public String AnotherAmount;

    @Getter
    @Setter
    @NotNull
    @Length(min = 10, max = 10)
    public String OutputCode;

    @Getter
    @Setter
    @NotNull
    @Length(min = 48, max = 48)
    public String OutputName;

    @Getter
    @Setter
    @NotNull
    @Length(min = 15, max = 15)
    public String RmtBankName;

    @Getter
    @Setter
    @NotNull
    @Length(min = 15, max = 15)
    public String RmtBrName;

    @Getter
    @Setter
    @NotNull
    @Length(min = 1, max = 1)
    public String CancelKind;

    @Getter
    @Setter
    @NotNull
    @Length(min = 20, max = 20)
    public String EDIInfo;

    @Getter
    @Setter
    @NotNull
    @Length(min = 52, max = 52)
    public String Dummy;

    public PaypayDepositForm() {}
}
