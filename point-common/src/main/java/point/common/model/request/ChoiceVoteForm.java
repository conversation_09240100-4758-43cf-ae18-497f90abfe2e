package point.common.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.ChoiceActivityVoteResult;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChoiceVoteForm {

    @NotNull private Long activityId;
    @NotNull private Long votePower;
    @NotNull private ChoiceActivityVoteResult choiceActivityVoteResult;
}
