package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class Account {
    @JsonAlias("accountId")
    private String accountId;

    @JsonAlias("branchCode")
    private String branchCode;

    @JsonAlias("branchName")
    private String branchName;

    @JsonAlias("accountTypeCode")
    private String accountTypeCode;

    @JsonAlias("accountTypeName")
    private String accountTypeName;

    @JsonAlias("accountNumber")
    private String accountNumber;

    @Json<PERSON>lias("primaryAccountCode")
    private String primaryAccountCode;

    @JsonAlias("primaryAccountCodeName")
    private String primaryAccountCodeName;

    @JsonAlias("accountName")
    private String accountName;

    @JsonAlias("accountNameKana")
    private String accountNameKana;

    @JsonAlias("currencyCode")
    private String currencyCode;

    @JsonAlias("currencyName")
    private String currencyName;

    @Json<PERSON>lias("transferLimitAmount")
    private String transferLimitAmount;
}
