package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.HistoryType;
import point.common.entity.FiatDeposit;
import point.common.entity.FiatWithdrawal;

@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class JpyHistoryData implements Serializable {

    private static final long serialVersionUID = 332350155679199387L;

    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class JpyHistoryElement implements Serializable {
        private static final long serialVersionUID = -6724221163430559710L;
        @Getter @Setter private Long id;

        @Getter @Setter private HistoryType historyType;

        @Getter @Setter private BigDecimal amount;

        @Getter @Setter private BigDecimal fee;

        @Getter @Setter private String status;

        @Getter @Setter private String date;
    }

    static class SortByDate implements Comparator<JpyHistoryElement> {
        @Override
        public int compare(JpyHistoryElement a, JpyHistoryElement b) {
            return b.getDate().compareTo(a.getDate());
        }
    }

    @Getter @Setter private List<JpyHistoryElement> jpyHistories = new ArrayList<>();

    public void setFiatDeposit(List<FiatDeposit> fiatDepositList) {
        fiatDepositList.forEach(
                v ->
                        jpyHistories.add(
                                new JpyHistoryElement(
                                        v.getId(),
                                        HistoryType.DEPOSIT,
                                        v.getAmount(),
                                        v.getFee(),
                                        v.getFiatDepositStatus().toString(),
                                        v.getFormattedCreatedAt())));
    }

    public void setFiatWithdrawal(List<FiatWithdrawal> fiatWithdrawalList) {
        fiatWithdrawalList.forEach(
                v ->
                        jpyHistories.add(
                                new JpyHistoryElement(
                                        v.getId(),
                                        HistoryType.WITHDRAWAL,
                                        v.getAmount(),
                                        v.getFee(),
                                        v.getFiatWithdrawalStatus().toString(),
                                        v.getFormattedCreatedAt())));
    }
}
