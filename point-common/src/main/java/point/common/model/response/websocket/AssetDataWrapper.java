package point.common.model.response.websocket;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Currency;
import point.common.entity.Asset;
import point.common.model.response.AssetData;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssetDataWrapper implements Serializable {
    @Serial private static final long serialVersionUID = -566380285075113191L;

    private Long userId;

    private Currency currency;

    private BigDecimal onhandAmount;

    private BigDecimal lockedAmount;

    public AssetDataWrapper(Asset asset) {
        this.userId = asset.getUserId();
        this.currency = asset.getCurrency();
        this.onhandAmount = asset.getOnhandAmount();
        this.lockedAmount = asset.getLockedAmount();
    }

    public AssetData unwrap(BigDecimal jpyConversion) {
        AssetData assetData = new AssetData();
        assetData.setCurrency(currency);
        assetData.setJpyLockedAmount(lockedAmount.multiply(jpyConversion));
        assetData.setJpyOnhandAmount(onhandAmount.multiply(jpyConversion));
        assetData.setLockedAmount(lockedAmount);
        assetData.setOnhandAmount(onhandAmount);
        assetData.setUnlockedAmount(onhandAmount.subtract(lockedAmount));
        //    assetData.setUser(asset.getUser()); // User Info is not required
        assetData.setUserId(userId);
        return assetData;
    }
}
