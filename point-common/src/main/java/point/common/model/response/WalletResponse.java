package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class WalletResponse<T> implements Serializable {

    private static final long serialVersionUID = 150196569704075377L;

    @Getter
    @Setter
    @JsonProperty(value = "code")
    private int code;

    @Getter
    @Setter
    @JsonProperty(value = "message")
    private String message;

    @Getter
    @Setter
    @JsonProperty(value = "data")
    private T data;
}
