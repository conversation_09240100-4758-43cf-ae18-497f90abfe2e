package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.*;
import point.common.serializer.BigDecimalSerializer;
import point.pos.entity.PosOrder;

@NoArgsConstructor
public class PosOrderTableData implements Serializable {

    private static final long serialVersionUID = -2442773807283492959L;

    @Getter @Setter private String tableKey;

    @Getter @Setter private Long id;

    @Getter @Setter private Long symbolId;

    @Getter @Setter private Long userId;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private OrderSide orderSide;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private OrderType orderType;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private OrderChannel orderChannel = OrderChannel.UNKNOWN;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal price;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal averagePrice = BigDecimal.ZERO;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal amount;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal remainingAmount;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private PosOrderStatus orderStatus;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private OrderOperator orderOperator;

    @Getter @Setter private String notes;

    @Getter
    @Setter
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Getter
    @Setter
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    public PosOrderTableData setProperties(PosOrder posOrder) {
        // for vue.js v-data-table
        this.setTableKey(posOrder.getSymbolId() + "_" + posOrder.getId());

        this.setId(posOrder.getId());
        this.setSymbolId(posOrder.getSymbolId());
        this.setUserId(posOrder.getUserId());
        this.setOrderSide(posOrder.getOrderSide());
        this.setOrderType(posOrder.getOrderType());
        this.setOrderChannel(posOrder.getOrderChannel());
        this.setPrice(posOrder.getPrice());
        this.setAveragePrice(BigDecimal.ZERO);
        this.setAmount(posOrder.getAmount());
        this.setRemainingAmount(posOrder.getRemainingAmount());
        this.setOrderStatus(posOrder.getOrderStatus());
        this.setOrderOperator(posOrder.getOrderOperator());
        this.setCreatedAt(posOrder.getCreatedAt());
        this.setUpdatedAt(posOrder.getUpdatedAt());
        this.setNotes(posOrder.getNotes());
        return this;
    }
}
