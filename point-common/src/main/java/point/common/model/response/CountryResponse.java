package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.ArrayList;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Country;
import point.common.model.response.CountryResponse.CountryData;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class CountryResponse extends ArrayList<CountryData> {

    @JsonIgnoreProperties(ignoreUnknown = true)
    @NoArgsConstructor
    public static class CountryData implements Serializable {

        private static final long serialVersionUID = 1308678053081778132L;

        @Getter @Setter private String isoAlpha2Code;

        @Getter @Setter private String callingCode;

        @Getter @Setter private String areaCode;

        @Getter @Setter private String label;

        @Getter @Setter private String jpyLabel;

        public CountryData(Country country) {
            isoAlpha2Code = country.name();
            callingCode = country.getCallingCode();
            areaCode = country.getAreaCode();
            label = country.getLabel();
            jpyLabel = country.getJpyLabel();
        }
    }

    private static final long serialVersionUID = -7265050176392523113L;

    public CountryResponse(Country[] countries) {
        for (Country country : countries) {
            add(new CountryData(country));
        }
    }
}
