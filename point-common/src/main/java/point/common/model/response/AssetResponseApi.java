package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.*;
import point.common.constant.Currency;
import point.common.serializer.BigDecimalSerializer;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "資産情報レスポンスAPI")
public class AssetResponseApi {

    @ApiModelProperty(value = "ユーザーID")
    private Long userId;

    @ApiModelProperty(value = "通貨")
    private Currency currency;

    @ApiModelProperty(value = "利用可能な金額")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal availableAmount;

    @ApiModelProperty(value = "評価損益金額")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal evalProfitLossAmt;

    @ApiModelProperty(value = "評価損益金額比率")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal evalProfitLossAmtRate;

    @ApiModelProperty(value = "平均取得単価")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal avgAcqUnitPrice;

    @ApiModelProperty(value = "現在の価格")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nowPrice;

    @ApiModelProperty(value = "操作ポイント")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal operatePoints;
}
