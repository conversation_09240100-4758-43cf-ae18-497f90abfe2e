package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.serializer.BigDecimalSerializer;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssetAllData implements Serializable {

    private static final long serialVersionUID = 1266569835151332326L;

    @Getter @Setter private BigDecimal assetSumAsJpy = BigDecimal.ZERO;

    @Getter @Setter private BigDecimal assetDiffAsJpy = null;

    @Getter @Setter private BigDecimal assetDiffPercent = null;

    @Getter @Setter private List<AssetData> assets;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal choiceEvalProfitLossAmtTotal = BigDecimal.ZERO;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal choiceEvalProfitLossAmtRateTotal = BigDecimal.ZERO;

    @Getter @Setter private Long choicePowerAmount;
}
