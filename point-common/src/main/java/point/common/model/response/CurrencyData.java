package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Currency;
import point.common.entity.CurrencyConfig;
import point.common.serializer.BigDecimalSerializer;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CurrencyData implements Serializable {

    private static final long serialVersionUID = 9071421502785957170L;

    @Getter @Setter private Long id;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private Currency currency;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Getter
    @Setter
    private BigDecimal depositFee = BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Getter
    @Setter
    private BigDecimal withdrawalFee = BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Getter
    @Setter
    private BigDecimal transactionFee = BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Getter
    @Setter
    private BigDecimal maxOrderAmountPerDay = BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Getter
    @Setter
    private BigDecimal minDepositAmount = BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Getter
    @Setter
    private BigDecimal minWithdrawalAmount = BigDecimal.ZERO;

    @Getter @Setter private boolean depositable = true;

    @Getter @Setter private boolean withdrawable = true;

    @Getter @Setter private boolean enabled = false;

    @Getter @Setter private String iconUrl;

    @Getter
    @Setter
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Getter
    @Setter
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    @Getter @Setter private int precision;

    public CurrencyData(CurrencyConfig currencyConfig, int precision, String iconUrl) {
        this.id = currencyConfig.getId();
        this.currency = currencyConfig.getCurrency();
        this.depositFee = currencyConfig.getDepositFee();
        this.withdrawalFee = currencyConfig.getWithdrawalFee();
        this.transactionFee = currencyConfig.getTransactionFee();
        this.maxOrderAmountPerDay = currencyConfig.getMaxOrderAmountPerDay();
        this.minDepositAmount = currencyConfig.getMinDepositAmount();
        this.minWithdrawalAmount = currencyConfig.getMinWithdrawalAmount();
        this.depositable = currencyConfig.isDepositable();
        this.withdrawable = currencyConfig.isWithdrawable();
        this.enabled = currencyConfig.isEnabled();
        this.createdAt = currencyConfig.getCreatedAt();
        this.updatedAt = currencyConfig.getUpdatedAt();
        this.iconUrl = iconUrl;
        this.precision = precision;
    }
}
