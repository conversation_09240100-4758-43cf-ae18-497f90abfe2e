package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.entity.User;
import point.common.entity.UserInfo;
import point.common.entity.UserKyc;
import point.common.entity.UserKycSub;
import point.common.model.dto.UserNoteDTO;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class UserKycData implements Serializable {

    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class KycImage implements Serializable {

        private static final long serialVersionUID = 7566295349115748983L;

        @Getter @Setter String name;

        @Getter @Setter String imageData;
    }

    private static final long serialVersionUID = 4696045618996815635L;

    @Getter @Setter private User user;

    @Getter @Setter private UserKyc userKyc;

    @Getter @Setter private UserInfo userInfo;

    @Getter @Setter private List<KycImage> kycImages = new ArrayList<>();

    @Getter @Setter private List<UserKycSub> kycSubStatuses = new ArrayList<>();

    @Getter
    @Setter
    @JsonProperty("isLatestKycData")
    private boolean isLatestKycData;

    @Getter @Setter private List<UserNoteDTO> userNotes = new ArrayList<>();

    @Getter @Setter private String email;

    @Getter @Setter private boolean flag = true;
}
