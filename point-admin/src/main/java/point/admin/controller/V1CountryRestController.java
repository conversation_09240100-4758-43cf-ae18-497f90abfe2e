package point.admin.controller;

import io.micrometer.core.annotation.Timed;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.common.constant.Country;
import point.common.model.response.CountryResponse;

@RequestMapping("/admin/v1/country")
@RequiredArgsConstructor
@RestController
@Timed
public class V1CountryRestController extends ExchangeAdminController {
    @GetMapping
    public ResponseEntity<CountryResponse> get(HttpServletResponse response) throws Exception {
        return ResponseEntity.ok(new CountryResponse(Country.values()));
    }
}
