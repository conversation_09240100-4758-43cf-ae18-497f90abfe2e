package point.admin.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.common.auth.AuthTypeEnum;
import point.common.auth.OAuth;
import point.common.config.GmoAuthorizationTokenConfiguration;
import point.common.config.GmoConfig;
import point.common.entity.AppConfiguration;
import point.common.repos.AppConfigurationRepository;

@RequestMapping("/admin/v1")
@RestController
@Slf4j
public class GmoAuthorizationController {
    @Autowired GmoConfig gmoConfig;

    @Value("${coin.cus.host-external:@null}")
    String hostExternal;

    @Autowired AppConfigurationRepository appConfigurationRepository;

    private final String configurationKey =
            "point.common.config.GmoAuthorizationTokenConfiguration";

    @GetMapping("/gmo/authorization")
    @PreAuthorize("@auth.check('gmo-oauth')")
    public ResponseEntity<String> authorization(HttpSession session)
            throws JsonProcessingException {
        // to generate state to prevent CSRF
        List<String> scopeList = gmoConfig.getScope();
        String scopes = scopeList.stream().collect(Collectors.joining(StringUtils.SPACE));
        String hashKey = RandomStringUtils.randomAlphabetic(5, 10);
        String redirectUri = hostExternal + gmoConfig.getRedirectUri();
        OAuth oAuth =
                new OAuth(
                        gmoConfig.getClientId(),
                        gmoConfig.getSecret(),
                        redirectUri,
                        scopes,
                        AuthTypeEnum.CLIENT_SECRET_BASIC,
                        hashKey);
        AppConfiguration appConfiguration =
                appConfigurationRepository.findFirstByKey(configurationKey);
        ObjectMapper obj = new ObjectMapper();
        GmoAuthorizationTokenConfiguration tokenConfiguration =
                obj.readValue(
                        appConfiguration.getValue(), GmoAuthorizationTokenConfiguration.class);
        ;
        tokenConfiguration.setHashKey(hashKey);
        tokenConfiguration.setSessionId(session.getId());
        appConfiguration.setValue(obj.writeValueAsString(tokenConfiguration));
        appConfigurationRepository.save(appConfiguration);
        String uri = null;
        try {
            log.info("generate state with hashKey:{},sessionId:{}", hashKey, session.getId());
            uri = oAuth.authorizationGetUrl(session.getId());

        } catch (Exception e) {
            log.warn("request gmo to authorization fail for:{}", e.getMessage());
        }
        return ResponseEntity.ok(uri);
    }
}
