package point.admin.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.common.constant.*;
import point.common.entity.Symbol;
import point.common.exception.CustomException;
import point.common.model.response.*;
import point.common.service.SymbolService;
import point.pos.entity.PosCoverOrder;
import point.pos.entity.PosMarketMakerConfig;
import point.pos.entity.PosOrder;
import point.pos.service.PosCoverOrderService;
import point.pos.service.PosMarketMakerConfigService;
import point.pos.service.PosOrderService;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/pos/cover-order")
public class V1PosCoverOrderRestController extends ExchangeAdminController {

    private final SymbolService symbolService;
    private final PosCoverOrderService posCoverOrderService;
    private final PosMarketMakerConfigService posMarketMakerConfigService;
    private final PosOrderService posOrderService;

    @GetMapping
    @PreAuthorize("@auth.check('pos-cover-orders')")
    public ResponseEntity<PosCoverOrderDetailTableData> get(
            @RequestParam(value = "symbolId", required = true) Long symbolId,
            @RequestParam(value = "id", required = true) Long id)
            throws CustomException {
        Symbol symbol = symbolService.findOne(symbolId);
        if (symbol == null) {
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }

        try {
            PosCoverOrder posCoverOrder = posCoverOrderService.findOne(id);
            if (posCoverOrder == null) {
                throw new CustomException(ErrorCode.DEALING_ERROR_COVER_ORDER_NOT_FOUND);
            }
            PosOrder posOrder = posOrderService.findOne(posCoverOrder.getOrderId());
            if (posOrder == null) {
                List<Long> orderIds = new ArrayList<>();
                orderIds.add(posCoverOrder.getOrderId());
                List<PosOrder> posOrders = posOrderService.findFromHistoryByOrderIds(orderIds);
                if (posOrders != null && posOrders.size() > 0) {
                    posOrder = posOrders.get(0);
                } else {
                    throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
                }
            }

            return ResponseEntity.ok(
                    new PosCoverOrderDetailTableData()
                            .setProperties(symbol.getCurrencyPair(), posCoverOrder, posOrder));
        } catch (NoSuchBeanDefinitionException e) {
            throw new CustomException(ErrorCode.DEALING_ERROR_COVER_ORDER_NOT_FOUND);
        }
    }

    @GetMapping("/page")
    @PreAuthorize("@auth.check('pos-cover-orders')")
    public ResponseEntity<PageData<PosCoverOrderTableData>> get(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "idFrom", required = false) Long idFrom,
            @RequestParam(value = "idTo", required = false) Long idTo,
            @RequestParam(value = "tradeType", required = true) TradeType tradeType,
            @RequestParam(value = "currencyPair", required = true) CurrencyPair currencyPair,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "orderType", required = false) OrderType orderType,
            @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
            @RequestParam(value = "pointOrderId", required = false) Long pointOrderId,
            @RequestParam(value = "greaterThanRemainingAmount", required = false)
                    BigDecimal greaterThanRemainingAmount,
            @RequestParam(value = "lessThanRemainingAmount", required = false)
                    BigDecimal lessThanRemainingAmount,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {

        PageData<PosCoverOrderTableData> pg = new PageData<>(number, size, 0, null);
        List<PosCoverOrderTableData> tableDatas = new ArrayList<>();
        // 注文一覧のため、件数上通貨ペア必須とする
        Symbol symbol = symbolService.findByCondition(tradeType, currencyPair);

        if (symbol == null) {
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }
        PosMarketMakerConfig marketMakerConfig =
                posMarketMakerConfigService.findBySymbolIdAll(symbol.getId());
        if (marketMakerConfig == null) {
            return ResponseEntity.ok(pg);
        }
        /** カバー注文取得 */
        PageData<PosCoverOrder> pagePosCoverOrders =
                posCoverOrderService.findByConditionPageData(
                        currencyPair,
                        symbol.getId(),
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        orderType,
                        orderSide,
                        marketMakerConfig.getExchange(),
                        pointOrderId,
                        greaterThanRemainingAmount,
                        lessThanRemainingAmount,
                        number,
                        size);

        List<PosCoverOrder> posCoverOrders = pagePosCoverOrders.getContent();
        if (CollectionUtils.isEmpty(posCoverOrders)) {
            return ResponseEntity.ok(pg);
        }

        /** カバー注文とコピー注文約定履歴のkeyファイル作成 */
        for (PosCoverOrder posCoverOrder : posCoverOrders) {

            PosOrder posOrder = posOrderService.findOne(posCoverOrder.getOrderId());
            if (posOrder == null) {
                List<Long> orderIds = new ArrayList<>();
                orderIds.add(posCoverOrder.getOrderId());
                List<PosOrder> posOrders = posOrderService.findFromHistoryByOrderIds(orderIds);
                if (posOrders != null && posOrders.size() > 0) {
                    posOrder = posOrders.get(0);
                } else {
                    throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
                }
            }

            /** コピー注文約定履歴をサマリしてresponseを編集 */
            PosCoverOrderTableData tableData =
                    new PosCoverOrderTableData()
                            .setProperties(currencyPair, posCoverOrder, posOrder);
            tableData.setOrderPrice(posOrder.getPrice());
            // 売り価格 - 買い価格(カバーが売りの場合カバー価格 - 販売価格)
            // 損益検証用に桁数処理しない(別途検討)
            BigDecimal priceDiff =
                    (posCoverOrder.getOrderSide() == OrderSide.SELL)
                            ? tableData.getAveragePrice().subtract(tableData.getOrderPrice())
                            : tableData.getOrderPrice().subtract(tableData.getAveragePrice());
            tableData.setPriceDiff(priceDiff);
            // 損益検証用に桁数処理しない(別途検討)
            tableData.setProfit(tableData.getFilledAmount().multiply(tableData.getPriceDiff()));
            tableDatas.add(tableData);
        }
        return ResponseEntity.ok(
                new PageData<>(number, size, pagePosCoverOrders.getTotalElements(), tableDatas));
    }
}
