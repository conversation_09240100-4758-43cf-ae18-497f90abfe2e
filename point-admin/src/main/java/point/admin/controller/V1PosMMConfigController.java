package point.admin.controller;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.common.constant.CurrencyPair;
import point.common.constant.TradeType;
import point.common.entity.PosMMConfig;
import point.common.exception.CustomException;
import point.common.model.request.PosMMConfigForm;
import point.common.service.PosMMConfigService;

@RequiredArgsConstructor
@RequestMapping("/admin/v1/pos/mmconfig")
@RestController
public class V1PosMMConfigController extends ExchangeAdminController {

    private final PosMMConfigService posMMConfigService;

    @GetMapping("/{id}")
    public ResponseEntity<PosMMConfig> getConfig(
            @AuthenticationPrincipal AdminUser adminUser, @PathVariable Long id)
            throws CustomException {

        return ResponseEntity.ok(posMMConfigService.getConfig(id));
    }

    @GetMapping("/all")
    @PreAuthorize("@auth.check('invest-mmconfig')")
    public ResponseEntity<List<PosMMConfig>> getAllConfig(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "symbolId", required = false) Long symbolId)
            throws CustomException {
        return ResponseEntity.ok(
                posMMConfigService.getAllConfig(symbolId).stream()
                        .filter(
                                config ->
                                        !CurrencyPair.BALC_JPY.equals(
                                                config.getSymbol().getCurrencyPair()))
                        .filter(
                                config ->
                                        config.getSymbol().getTradeType().equals(TradeType.INVEST))
                        .toList());
    }

    @PutMapping
    public ResponseEntity<PosMMConfig> modifyConfig(
            @AuthenticationPrincipal AdminUser adminUser, @RequestBody PosMMConfigForm form)
            throws Exception {

        return ResponseEntity.ok(posMMConfigService.editConfig(form));
    }
}
