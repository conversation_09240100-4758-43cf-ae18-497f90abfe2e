package point.admin.controller;

import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.common.constant.ErrorCode;
import point.common.constant.Exchange;
import point.common.entity.BalanceNotifyConfig;
import point.common.exception.CustomException;
import point.common.model.request.BalanceNotifyConfigUpdateForm;
import point.common.service.BalanceNotifyConfigService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/balance-notify-config")
public class V1BalanceNotifyConfigRestController extends ExchangeAdminController {
    private final BalanceNotifyConfigService balanceNotifyConfigService;

    @GetMapping
    public ResponseEntity<List<BalanceNotifyConfig>> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "currency", required = false) String currency,
            @RequestParam(value = "targetPos", required = false) Exchange targetPos,
            @RequestParam(value = "enabled", required = false) Boolean enabled)
            throws Exception {
        if (id != null) {
            List<BalanceNotifyConfig> balanceNotifyConfigs = new ArrayList<>();
            balanceNotifyConfigs.add(balanceNotifyConfigService.findOne(id));
            return ResponseEntity.ok(balanceNotifyConfigs);
        } else if (currency != null || targetPos != null || enabled != null) {
            List<BalanceNotifyConfig> balanceNotifyConfigs =
                    balanceNotifyConfigService.findAllByCondition(currency, targetPos, enabled);
            return ResponseEntity.ok(balanceNotifyConfigs);
        } else {
            return ResponseEntity.ok(balanceNotifyConfigService.findAll());
        }
    }

    @PutMapping
    @PreAuthorize("@auth.check('balance-notify-config')")
    public ResponseEntity<BalanceNotifyConfig> update(
            @AuthenticationPrincipal AdminUser adminUser,
            @Valid @RequestBody BalanceNotifyConfigUpdateForm form)
            throws Exception {
        BalanceNotifyConfig balanceNotifyConfig = balanceNotifyConfigService.findOne(form.getId());

        if (balanceNotifyConfig == null) {
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }
        customTransactionManager.execute(
                entityManager -> {
                    List<BalanceNotifyConfig> balanceNotifyConfigs =
                            balanceNotifyConfigService.findAll();
                    balanceNotifyConfigs.forEach(
                            (config) -> {
                                if (!config.getMailTo().equals(form.getMailTo())) {
                                    config.setMailTo(form.getMailTo());
                                    balanceNotifyConfigService.save(config);
                                }
                            });
                    balanceNotifyConfig.setEnabled(form.isEnabled());
                    balanceNotifyConfig.setMailTo(form.getMailTo());
                    balanceNotifyConfig.setDefaultBalance(form.getDefaultBalance());
                    balanceNotifyConfig.setLimitBalancePercent(form.getLimitBalancePercent());
                    balanceNotifyConfigService.save(balanceNotifyConfig);
                });

        return ResponseEntity.ok(balanceNotifyConfig);
    }
}
