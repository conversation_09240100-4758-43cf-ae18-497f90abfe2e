package point.admin.controller;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import point.admin.model.request.AdminRoleAddForm;
import point.admin.model.request.AdminRoleAssignForm;
import point.admin.model.request.AdminRoleForm;
import point.admin.model.request.AdminRoleMenuForm;
import point.admin.model.response.AdminMenuData;
import point.admin.model.response.AdminRoleData;
import point.admin.model.response.AdminRoleMenuData;
import point.admin.service.AdminRoleService;
import point.admin.service.AdminUserAuthorityService;
import point.common.entity.User;
import point.common.exception.CustomException;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/role")
public class V1AdminRoleController {

    private final AdminRoleService adminRoleService;

    private final AdminUserAuthorityService adminUserAuthorityService;

    @GetMapping("")
    public ResponseEntity<List<AdminRoleData>> getRoleList() {
        return new ResponseEntity<>(adminRoleService.searchAll(), HttpStatus.OK);
    }

    @PostMapping("/switch")
    @PreAuthorize("@auth.check('user-role')")
    public ResponseEntity<Void> switchEnableRole(
            @AuthenticationPrincipal User user, @RequestBody AdminRoleForm form)
            throws CustomException {
        adminRoleService.roleEnableSwitch(form.getId(), form.isEnable());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}")
    public ResponseEntity<AdminRoleMenuData> getRole(
            @AuthenticationPrincipal User user, @PathVariable Long id) throws CustomException {
        return new ResponseEntity<>(adminRoleService.getRoleMenu(id), HttpStatus.OK);
    }

    @PutMapping("")
    @PreAuthorize("@auth.check('user-role')")
    public ResponseEntity<List<AdminMenuData>> updateRole(
            @AuthenticationPrincipal User user, @RequestBody AdminRoleMenuForm form)
            throws CustomException {
        adminRoleService.updateRole(form.getRoleName().trim(), form.getRoleId(), form.getMenus());
        return ResponseEntity.ok().build();
    }

    @PostMapping("")
    @PreAuthorize("@auth.check('user-role')")
    public ResponseEntity<List<AdminMenuData>> saveRole(
            @AuthenticationPrincipal User user, @RequestBody AdminRoleAddForm form)
            throws CustomException {
        adminRoleService.saveRole(form.getRoleName().trim(), form.getMenus());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/assign")
    @PreAuthorize("@auth.check('user-role')")
    public ResponseEntity<List<AdminMenuData>> roleAssign(
            @AuthenticationPrincipal User user, @RequestBody AdminRoleAssignForm form) {
        adminUserAuthorityService.assign(form.getUserId(), form.getRoleId());
        return ResponseEntity.ok().build();
    }
}
