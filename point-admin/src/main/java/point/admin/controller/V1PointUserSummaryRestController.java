package point.admin.controller;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.model.response.ReportPointUserSummary;
import point.common.component.CsvDownloadManager;
import point.common.constant.ViewVariables;
import point.common.exception.CustomException;
import point.common.model.response.PageData;
import point.common.util.DateUnit;
import point.operate.entity.PointUserSummary;
import point.operate.service.PointUserSummaryService;

@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/v1/point-user-summary")
public class V1PointUserSummaryRestController extends ExchangeAdminController {

    private final PointUserSummaryService pointUserSummaryService;
    private final CsvDownloadManager<ReportPointUserSummary> downloadManager;

    @GetMapping()
    @PreAuthorize("@auth.check('point-user-summary')")
    public ResponseEntity<PageData<PointUserSummary>> get(
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws CustomException {
        Date targetAtFrom = dateFrom == null ? null : new Date(dateFrom);
        Date targetAtTo = dateTo == null ? null : DateUnit.getTommorowStartDate(new Date(dateTo));
        PageData<PointUserSummary> userSummaryList;
        userSummaryList =
                pointUserSummaryService.findByTargetAtPageData(
                        targetAtFrom, targetAtTo, number, size);
        return ResponseEntity.ok(userSummaryList);
    }

    @GetMapping("/download")
    public void download(
            HttpServletResponse response,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {
        Date targetAtFrom = dateFrom == null ? null : new Date(dateFrom);
        Date targetAtTo = dateTo == null ? null : DateUnit.getTommorowStartDate(new Date(dateTo));
        PageData<PointUserSummary> userSummaryList;
        userSummaryList =
                pointUserSummaryService.findByTargetAtPageData(
                        targetAtFrom, targetAtTo, 0, Integer.MAX_VALUE);
        List<ReportPointUserSummary> reportList =
                userSummaryList.getContent().stream().map(ReportPointUserSummary::new).toList();
        String fileNamePrefix = "point_user_summary";
        downloadManager.download(response, reportList, fileNamePrefix, true);
    }
}
