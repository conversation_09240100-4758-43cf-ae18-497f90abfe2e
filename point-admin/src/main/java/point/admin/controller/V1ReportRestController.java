package point.admin.controller;

import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfWriter;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import point.admin.entity.AdminUser;
import point.common.constant.ErrorCode;
import point.common.constant.UserIdType;
import point.common.entity.UserIdentity;
import point.common.exception.CustomException;
import point.common.model.response.ReportData;
import point.common.model.response.ReportText;
import point.common.pdf.PdfPageXofYEventHelper;
import point.common.service.ReportService;
import point.common.service.UserIdentityService;
import point.common.service.UserService;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/report")
public class V1ReportRestController extends ExchangeAdminController {

    private final ReportService reportService;
    private final TemplateEngine templateEngine;
    private final UserService userService;
    private final UserIdentityService userIdentityService;
    private static final String DAIRY_REPORT_TEMPLATE = "report/daily.txt";

    @GetMapping("/daily/download")
    public ResponseEntity<ReportText> downloadDaily(
            @AuthenticationPrincipal AdminUser adminUser,
            Long userId,
            @Valid @Pattern(regexp = "^\\d{8}$") @RequestParam(value = "firstDay", required = true)
                    String firstDay,
            @Pattern(regexp = "^\\d{8}$") @RequestParam(value = "lastDay", required = true)
                    String lastDay)
            throws Exception {

        UserIdentity userIdentity = userIdentityService.findOne(userId);

        if (userIdentity == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
        } else if (UserIdType.Operate.equals(userIdentity.getIdType())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_IS_OPERATE_USER);
        }

        ReportData reportData = reportService.createReportData(userId, firstDay, lastDay);

        // Create csv
        final Context ctx = new Context(Locale.getDefault());
        ctx.setVariable("report", reportData);
        ctx.setVariable("isMonthly", false); // 対象期間の表示切り替え
        String reportText = this.templateEngine.process(DAIRY_REPORT_TEMPLATE, ctx);
        ReportText report = new ReportText();
        report.setText(reportText);
        return ResponseEntity.ok(report);
    }

    @GetMapping("/monthly/download")
    public ResponseEntity<ReportText> downloadMonthly(
            @AuthenticationPrincipal AdminUser adminUser,
            Long userId,
            @Valid
                    @Pattern(regexp = "^\\d{6}$")
                    @RequestParam(value = "targetMonth", required = true)
                    String targetMonth)
            throws Exception {

        UserIdentity userIdentity = userIdentityService.findOne(userId);

        if (userIdentity == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
        } else if (UserIdType.Operate.equals(userIdentity.getIdType())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_IS_OPERATE_USER);
        }

        String firstDay = targetMonth + "01";
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, Integer.parseInt(targetMonth.substring(0, 4)));
        calendar.set(
                Calendar.MONTH, Integer.parseInt(targetMonth.substring(4, 6)) - 1); // MONTHは0〜11
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 31日に 2,4,6,9,11月が指定されると翌月扱いになるから 1日に
        String lastDayOfMonth = String.format("%02d", calendar.getActualMaximum(Calendar.DATE));
        String lastDay = targetMonth + lastDayOfMonth;

        ReportData reportData = reportService.createReportData(userId, firstDay, lastDay);

        // Create csv
        final Context ctx = new Context(Locale.getDefault());
        ctx.setVariable("report", reportData);
        ctx.setVariable("isMonthly", true); // 対象期間の表示切り替え
        String reportText = this.templateEngine.process(DAIRY_REPORT_TEMPLATE, ctx);
        ReportText report = new ReportText();
        report.setText(reportText);
        return ResponseEntity.ok(report);
    }

    @GetMapping("/yearly/download")
    public String downloadYearly(
            @AuthenticationPrincipal AdminUser adminUser,
            Long userId,
            @Valid
                    @Pattern(regexp = "^\\d{6}$")
                    @RequestParam(value = "targetMonth", required = true)
                    String targetMonth,
            HttpServletResponse response)
            throws Exception {

        UserIdentity userIdentity = userIdentityService.findOne(userId);

        if (userIdentity == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
        } else if (UserIdType.Operate.equals(userIdentity.getIdType())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_IS_OPERATE_USER);
        }

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, Integer.valueOf(targetMonth.substring(0, 4)));
        calendar.set(Calendar.MONTH, Integer.valueOf(targetMonth.substring(4)) - 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String lastDay = sdf.format(calendar.getTime());
        calendar.add(Calendar.YEAR, -1);
        calendar.add(Calendar.DATE, 1);
        String firstDay = sdf.format(calendar.getTime());

        OutputStream outputStream = response.getOutputStream();

        Rectangle pageSize = new Rectangle(PageSize.A4.getHeight(), PageSize.A4.getWidth());
        pageSize.rotate();
        Document document = new Document();
        document.setPageSize(pageSize);
        PdfWriter pdfWriter = PdfWriter.getInstance(document, outputStream);
        pdfWriter.setPageEvent(new PdfPageXofYEventHelper());

        document.open();
        try {
            reportService.createdPDFContent(document, userId, firstDay, lastDay, false);
        } catch (Exception e) {
            log.warn("yearly report create error." + e.getMessage());
            throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
        }
        document.close();
        response.setContentType("application/pdf");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=report.pdf");
        return null;
    }
}
