package point.admin.controller;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.admin.model.response.ReportMediationStatement;
import point.common.component.CsvDownloadManager;
import point.common.constant.TradeType;
import point.common.entity.AbstractEntity;
import point.common.entity.Symbol;
import point.common.entity.User;
import point.common.service.SymbolService;
import point.common.service.UserService;
import point.common.util.DateUnit;

// 媒介又は代理に係る取引記録
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/report-mediation")
public class V1ReportMediationController {

    private final SymbolService symbolService;
    private final UserService userService;
    private final CsvDownloadManager<ReportMediationStatement> downloadManager;

    // 管理画面からの検索で呼び出される
    // adminUser: 操作管理者
    // date：対象日時(ex 1675350000000)
    @GetMapping()
    public void download(
            HttpServletResponse response,
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "date", required = false) Long paramDate)
            throws Exception {

        // パラメータ日時がなければ現在時刻を基本日時とする
        final var date = paramDate == null ? System.currentTimeMillis() : paramDate;

        // 日時からfromToを作成する
        final var fromTo = DateUnit.createFromTo(date);

        // DBからデータを取得する
        final var readDto = read(fromTo.getLeft(), fromTo.getRight());

        // 出力DTOを生成する
        final var writeDtos = process(readDto);

        // ダウンロード情報を返却する
        downloadManager.download(response, writeDtos, "report_mediation", true);
    }

    // DBからデータを取得する
    protected ReadData read(Date dateFrom, Date dateTo) {
        // Symbolを全て取得する
        final var symbols = symbolService.findAll();
        // ユーザを全て取得する
        final var users =
                userService.findAll().stream().filter(user -> user.getUserInfo() != null).toList();
        List<Symbol> posSymbols =
                symbols.stream().filter(x -> x.getTradeType() == TradeType.INVEST).toList();

        // 取得データを生成する
        return new ReadData(symbols, users);
    }

    // 取得データを出力形式に整形する
    protected List<ReportMediationStatement> process(ReadData readData) {

        // 自社口座を全て取得する
        final var systemUsers =
                userService.findByInsideAccountFlg(true).stream()
                        .filter(user -> user.getUserInfo() != null)
                        .toList();
        Set<String> ownerIds =
                systemUsers.stream().map(p -> p.getId().toString()).collect(Collectors.toSet());

        // symbolをMAP化する
        final var symbolMap =
                readData.symbols.stream()
                        .collect(Collectors.toMap(AbstractEntity::getId, it -> it));
        // ユーザをMAP化する
        final var userMap =
                readData.users.stream().collect(Collectors.toMap(AbstractEntity::getId, it -> it));
        // Wrapされたアイテムを資産毎に出力DTOに変換する
        return new ArrayList<>();
    }

    public record ReadData(List<Symbol> symbols, List<User> users) {}
}
