package point.admin.controller;

import io.micrometer.core.annotation.Timed;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.admin.model.response.PosTradeAdminTableData;
import point.common.component.CsvDownloadManager;
import point.common.constant.CurrencyPair;
import point.common.constant.OrderChannel;
import point.common.constant.OrderSide;
import point.common.constant.OrderType;
import point.common.constant.TradeAction;
import point.common.constant.ViewVariables;
import point.common.entity.Symbol;
import point.common.model.response.PageData;
import point.common.model.response.PosTradeReportData;
import point.common.service.SymbolService;
import point.pos.entity.PosTrade;
import point.pos.service.PosTradeService;

@Slf4j
@Timed
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/v1/pos/trade")
public class V1PosTradeRestController extends ExchangeAdminController {

    private final SymbolService symbolService;
    private final CsvDownloadManager<PosTradeReportData> downloadManager;
    private final PosTradeService posTradeService;

    @GetMapping("/allForPos")
    public ResponseEntity<PageData<PosTradeAdminTableData>> getAllForPos(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "idFrom", required = false) Long idFrom,
            @RequestParam(value = "idTo", required = false) Long idTo,
            @RequestParam(value = "symbolId", required = true) Long symbolId,
            @RequestParam(value = "userIds", required = false) List<Long> userIds,
            @RequestParam(value = "exceptUserIds", required = false) List<Long> exceptUserIds,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "tradeAction", required = false) TradeAction tradeAction,
            @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
            @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
            @RequestParam(value = "orderChannel", required = false) OrderChannel orderChannel,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {

        PageData<PosTradeAdminTableData> pg =
                new PageData<PosTradeAdminTableData>(number, size, 0, null);
        PageData<PosTradeAdminTableData> temp =
                new PageData<PosTradeAdminTableData>(number, size, 0, null);

        if (symbolId == null) {
            return ResponseEntity.ok(pg);
        }

        Symbol symbol = symbolService.findOne(symbolId);

        // 約定一覧取得
        List<PosTrade> posTradesMain =
                posTradeService.findByConditionForBo(
                        symbolId,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        tradeAction,
                        orderTypes != null ? orderTypes.get(0) : null,
                        orderSide,
                        orderChannel,
                        number,
                        size,
                        false);

        Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
        Date dateToDate = (dateTo == null) ? null : new Date(dateTo);

        List<PosTrade> posTradeHistoryList =
                posTradeService.findAllFromHistoryForBo(
                        symbol,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFromDate,
                        dateToDate,
                        null,
                        tradeAction,
                        orderSide,
                        orderTypes != null ? orderTypes.get(0) : null,
                        orderChannel);

        // merge & sort
        posTradesMain.addAll(posTradeHistoryList);
        posTradesMain.sort((x, y) -> y.getId().compareTo(x.getId()));
        List<PosTrade> posTrades = posTradesMain; // ソート処理

        // PageData作成
        Long count = (long) posTrades.size();
        PageData<PosTrade> data = createPageData(posTrades, count, number, size);

        // 編集
        CurrencyPair currencyPair = symbol.getCurrencyPair();
        for (int i = 0; i < data.getContent().size(); i++) {
            // 桁数揃え
            PosTradeAdminTableData tableData =
                    new PosTradeAdminTableData().setProperties(data.getContent().get(i));
            tableData.setAmount(
                    currencyPair.getAmountByTradeType(
                            tableData.getAmount(), symbol.getTradeType()));
            tableData.setPrice(
                    currencyPair.getScaledPrice(tableData.getPrice(), RoundingMode.DOWN));
            tableData.setFee(currencyPair.getScaledAsset(tableData.getFee(), RoundingMode.DOWN));
            temp.getContent().add(tableData);
        }

        temp.addTotalElements(data.getTotalElements()); // 全件数を取得するためにループは最後まで回す
        pg =
                new PageData<PosTradeAdminTableData>(
                        number, size, temp.getTotalElements(), temp.getContent());

        return ResponseEntity.ok(pg);
    }

    @GetMapping("/download")
    public String download(
            @AuthenticationPrincipal AdminUser adminUser,
            HttpServletResponse response,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "idFrom", required = false) Long idFrom,
            @RequestParam(value = "idTo", required = false) Long idTo,
            @RequestParam(value = "symbolId", required = true) Long symbolId,
            @RequestParam(value = "userIds", required = false) List<Long> userIds,
            @RequestParam(value = "exceptUserIds", required = false) List<Long> exceptUserIds,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "tradeAction", required = false) TradeAction tradeAction,
            @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
            @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
            @RequestParam(value = "orderChannel", required = false) OrderChannel orderChannel)
            throws Exception {

        List<PosTradeReportData> reportDataList = new ArrayList<PosTradeReportData>();
        Symbol symbol = symbolService.findOne(symbolId);
        if (symbol == null) {
            return null;
        }

        // 約定一覧取得
        List<PosTrade> posTrades =
                posTradeService.findByConditionForBo(
                        symbolId,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        tradeAction,
                        orderTypes != null ? orderTypes.get(0) : null,
                        orderSide,
                        orderChannel,
                        0,
                        Integer.MAX_VALUE,
                        false);

        if (posTrades == null) {
            posTrades = new ArrayList<>();
        }
        for (PosTrade posTrade : posTrades) {
            reportDataList.add(
                    new PosTradeReportData().setProperties(posTrade, symbol.getCurrencyPair()));
        }

        Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
        Date dateToDate = (dateTo == null) ? null : new Date(dateTo);

        List<PosTrade> posTradeHistoryList =
                posTradeService.findAllFromHistoryForBo(
                        symbol,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFromDate,
                        dateToDate,
                        null,
                        tradeAction,
                        orderSide,
                        orderTypes != null ? orderTypes.get(0) : null,
                        orderChannel);

        for (PosTrade posTrade : posTradeHistoryList) {
            reportDataList.add(
                    new PosTradeReportData().setProperties(posTrade, symbol.getCurrencyPair()));
        }
        reportDataList.sort((x, y) -> x.getId().compareTo(y.getId()));

        String fileNamePrefix = "posTrade";
        downloadManager.download(
                response, reportDataList, fileNamePrefix, PosTradeReportData.getReportHeader());

        return null;
    }

    public PageData<PosTrade> createPageData(
            List<PosTrade> content, Long count, Integer number, Integer size) {
        List<PosTrade> pageContents = new ArrayList<PosTrade>();
        int maxSize = Math.min((number * size + size), content.size());

        for (int i = number * size; i < maxSize; i++) {

            pageContents.add(content.get(i));
        }

        return new PageData<PosTrade>(number, size, count, pageContents);
    }
}
