package point.admin.controller;

import io.micrometer.core.annotation.Timed;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.common.component.CsvDownloadManager;
import point.common.constant.ViewVariables;
import point.common.entity.PointPartner;
import point.common.entity.PointUser;
import point.common.model.response.PageData;
import point.common.model.response.PointUserReportData;
import point.common.service.PointPartnerService;
import point.common.service.PointUserService;

@Timed
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/point/user")
public class V1PointUserRestController extends ExchangeAdminController {

    private final PointUserService pointUserService;
    public final PointPartnerService pointPartnerService;

    private final CsvDownloadManager<PointUserReportData> downloadManager;
    private final String reportPreFix = "運用ユーザー一覧通常";

    @GetMapping
    @PreAuthorize("@auth.check('point-user-list')")
    public ResponseEntity<PageData<PointUser>> get(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "partnerId", required = false) String partnerId,
            @RequestParam(value = "partnerNumber", required = false) String partnerNumber,
            @RequestParam(value = "partnerMemberId", required = false) String partnerMemberId,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size) {
        PageData<PointUser> pageData =
                pointUserService.findByCondition(
                        id,
                        name,
                        partnerId,
                        partnerNumber,
                        partnerMemberId,
                        dateFrom,
                        dateTo,
                        number,
                        size);
        return ResponseEntity.ok(pageData);
    }

    @GetMapping("/download")
    @PreAuthorize("@auth.check('point-user-list')")
    public void download(
            HttpServletResponse response,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "partnerId", required = false) String partnerId,
            @RequestParam(value = "partnerNumber", required = false) String partnerNumber,
            @RequestParam(value = "partnerMemberId", required = false) String partnerMemberId,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {

        List<PointUserReportData> reportDataList = new ArrayList<>();

        List<PointUser> res =
                pointUserService.findByAllCondition(
                        id, name, partnerId, partnerNumber, partnerMemberId, dateFrom, dateTo);
        for (PointUser pointUser : res) {
            reportDataList.add(new PointUserReportData().setProperties(pointUser));
        }
        downloadManager.download(response, reportDataList, reportPreFix, true);
    }

    @GetMapping("/select")
    @PreAuthorize("@auth.check('point-user-list')")
    public ResponseEntity<List<PointPartner>> get() throws Exception {
        List<PointPartner> pointPartnerList = new ArrayList<>();
        pointPartnerService.findAll().stream()
                .map(
                        pointPartnerInfo -> {
                            PointPartner pointPartner = new PointPartner();
                            pointPartner.setId(pointPartnerInfo.getId());
                            pointPartner.setName(pointPartnerInfo.getName());
                            pointPartner.setPartnerNumber(pointPartnerInfo.getPartnerNumber());
                            return pointPartner;
                        })
                .forEach(pointPartnerList::add);

        return ResponseEntity.ok(pointPartnerList);
    }
}
