package point.admin.controller;

import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.common.constant.ChoiceActivityResultStatus;
import point.common.constant.ChoiceActivityVoteResult;
import point.common.constant.ErrorCode;
import point.common.constant.ViewVariables;
import point.common.entity.ChoiceActivity;
import point.common.exception.CustomException;
import point.common.model.response.PageData;
import point.common.service.ChoiceActivityService;
import point.common.service.ChoicePowerService;

@Timed
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/choice/activity")
public class V1ChoiceActivityController extends ExchangeAdminController {

    public final ChoicePowerService choicePowerService;
    public final ChoiceActivityService choiceActivityService;

    @GetMapping
    @PreAuthorize("@auth.check('choice-activity-list')")
    public ResponseEntity<PageData<ChoiceActivity>> get(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "status", required = false) ChoiceActivityResultStatus status,
            @RequestParam(value = "totalVoteUsersFrom", required = false) Long totalVoteUsersFrom,
            @RequestParam(value = "totalVoteUsersTo", required = false) Long totalVoteUsersTo,
            @RequestParam(value = "voteUsersFrom", required = false) Long voteUsersFrom,
            @RequestParam(value = "voteUsersTo", required = false) Long voteUsersTo,
            @RequestParam(value = "totalVotePowerFrom", required = false) Long totalVotePowerFrom,
            @RequestParam(value = "totalVotePowerTo", required = false) Long totalVotePowerTo,
            @RequestParam(value = "voteResult", required = false)
                    ChoiceActivityVoteResult voteResult,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size) {
        PageData<ChoiceActivity> pageData =
                choiceActivityService.findByCondition(
                        id,
                        status,
                        totalVoteUsersFrom,
                        totalVoteUsersTo,
                        voteUsersFrom,
                        voteUsersTo,
                        totalVotePowerFrom,
                        totalVotePowerTo,
                        voteResult,
                        number,
                        size);
        return ResponseEntity.ok(pageData);
    }

    @GetMapping("/detail")
    @PreAuthorize("@auth.check('choice-activity-list')")
    public ResponseEntity<ChoiceActivity> get(@RequestParam(value = "id") Long id)
            throws Exception {
        if (id == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_PARAMETER_ERROR);
        }
        ChoiceActivity choiceActivity = choiceActivityService.findOne(id);
        return ResponseEntity.ok().body(choiceActivity);
    }
}
