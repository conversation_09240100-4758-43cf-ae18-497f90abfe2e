package point.admin.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.admin.model.response.ReportOrderSlipStatement;
import point.common.component.CsvDownloadManager;
import point.common.constant.TradeType;
import point.common.entity.AbstractEntity;
import point.common.entity.Symbol;
import point.common.entity.Trade;
import point.common.entity.User;
import point.common.service.SymbolService;
import point.common.service.UserService;
import point.common.util.DateUnit;
import point.pos.entity.PosOrder;
import point.pos.entity.PosTrade;
import point.pos.service.PosOrderService;
import point.pos.service.PosTradeService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/report-order-slip")
public class V1ReportOrderSlipController {

    private final SymbolService symbolService;
    private final UserService userService;
    private final CsvDownloadManager<ReportOrderSlipStatement> downloadManager;
    private final PosTradeService posTradeService;
    private final PosOrderService posOrderService;

    // 管理画面からの検索で呼び出される
    // adminUser: 操作管理者
    // date：対象日時(ex 1675350000000)
    @GetMapping()
    public void download(
            HttpServletResponse response,
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "date", required = false) Long paramDate)
            throws Exception {

        // パラメータ日時がなければ現在時刻を基本日時とする
        final var date = paramDate == null ? System.currentTimeMillis() : paramDate;

        // 日時からfromToを作成する
        final var fromTo = DateUnit.createFromTo(date);

        // DBからデータを取得する
        final var readDto = read(fromTo.getLeft(), fromTo.getRight());

        // 出力DTOを生成する
        final var writeDtos = process(readDto);

        // ダウンロード情報を返却する
        downloadManager.download(response, writeDtos, "report_order_slip", true);
    }

    // DBからデータを取得する
    protected ReadData read(Date dateFrom, Date dateTo) {
        // Symbolを全て取得する
        final var symbols = symbolService.findAll();
        // ユーザを全て取得する
        final var users =
                userService.findAll().stream().filter(user -> user.getUserInfo() != null).toList();
        List<Symbol> posSymbols =
                symbols.stream().filter(x -> x.getTradeType() == TradeType.INVEST).toList();
        List<PosTrade> posTrades = new ArrayList<>();
        posSymbols.forEach(
                symbol -> {
                    // テーブル「pos_trade」からデータを取得する
                    List<PosTrade> posTradesFromSymbol =
                            posTradeService.findAllByCondition(
                                    symbol.getId(),
                                    null,
                                    null,
                                    null,
                                    null,
                                    dateFrom.getTime(),
                                    dateTo.getTime(),
                                    null,
                                    null,
                                    null);

                    if (posTradesFromSymbol != null) {
                        posTrades.addAll(posTradesFromSymbol);
                    }

                    List<PosTrade> posTradesFromHistory =
                            posTradeService.findFromPosTradeHistory(
                                    symbol,
                                    null,
                                    null,
                                    null,
                                    null,
                                    dateFrom,
                                    dateTo,
                                    0,
                                    Integer.MAX_VALUE);
                    if (posTradesFromHistory != null) {
                        posTrades.addAll(posTradesFromHistory);
                    }
                });
        // 　②販売所
        List<PosOrder> posOrders1 = new ArrayList<>();
        posSymbols.forEach(
                symbol -> {
                    List<PosOrder> posOrdersFromSymbol =
                            posOrderService
                                    .findAllByCondition(
                                            symbol.getId(),
                                            null,
                                            null,
                                            null,
                                            null,
                                            dateFrom.getTime(),
                                            dateTo.getTime(),
                                            null,
                                            null,
                                            null)
                                    .stream()
                                    .toList();

                    if (posOrdersFromSymbol != null) {
                        posOrders1.addAll(posOrdersFromSymbol);
                    }

                    List<PosOrder> posOrdersFromHistory =
                            posOrderService.findAllFromHistory(
                                    symbol, null, null, null, null, dateFrom, dateTo, null, null,
                                    null, null, null);
                    if (posOrdersFromHistory != null) {
                        posOrders1.addAll(posOrdersFromHistory);
                    }
                });
        final var orderPairSetForPos =
                posOrders1.stream()
                        .map(order -> Pair.of(order.getSymbolId(), order.getId()))
                        .collect(Collectors.toSet());

        List<PosOrder> posOrders2 = new ArrayList<>();
        posSymbols.forEach(
                symbol -> {
                    final var tradeOrderIds =
                            posTrades.stream()
                                    .filter(trade -> trade.getSymbolId().equals(symbol.getId()))
                                    .filter(
                                            trade ->
                                                    !orderPairSetForPos.contains(
                                                            Pair.of(
                                                                    trade.getSymbolId(),
                                                                    trade.getOrderId())))
                                    .map(Trade::getOrderId)
                                    .distinct()
                                    .toList();

                    List<PosOrder> posOrdersFindByOrderIds =
                            tradeOrderIds.isEmpty()
                                    ? new ArrayList<>()
                                    : posOrderService.findByOrderIds(tradeOrderIds);
                    if (posOrdersFindByOrderIds != null) {
                        posOrders2.addAll(posOrdersFindByOrderIds);
                    }

                    List<PosOrder> posOrdersFromHistoryByOrderIds =
                            tradeOrderIds.isEmpty()
                                    ? new ArrayList<>()
                                    : posOrderService.findFromHistoryByOrderIds(tradeOrderIds);
                    if (posOrdersFromHistoryByOrderIds != null) {
                        posOrders2.addAll(posOrdersFromHistoryByOrderIds);
                    }
                });

        List<PosOrder> posOrders =
                Stream.of(posOrders1, posOrders2)
                        .flatMap(x -> x.stream())
                        .collect(Collectors.toList());
        return new ReadData(symbols, users, posOrders, posTrades, dateTo);
    }

    // 取得データを出力形式に整形する
    protected List<ReportOrderSlipStatement> process(ReadData readData) {
        // symbolをMAP化する
        final var symbolMap =
                readData.symbols.stream()
                        .collect(Collectors.toMap(AbstractEntity::getId, it -> it));
        // ユーザをMAP化する
        final var userMap =
                readData.users.stream().collect(Collectors.toMap(AbstractEntity::getId, it -> it));

        // 注文ID:約定リストのMAPを生成する
        final var posTradeMap =
                readData.posTrades.stream().collect(Collectors.groupingBy(Trade::getOrderId));

        // Wrapされたアイテムを資産毎に出力DTOに変換する（販売所）
        final var posTrades =
                readData.posOrders.stream()
                        .flatMap(
                                order ->
                                        createWriteDtoForPos(
                                                symbolMap.get(order.getSymbolId()),
                                                userMap.get(order.getUserId()),
                                                order,
                                                posTradeMap.getOrDefault(
                                                        order.getId(), Collections.emptyList()),
                                                readData.dateTo)
                                                .stream());

        return posTrades
                .sorted(
                        Comparator.comparing(ReportOrderSlipStatement::orderIdLong) // ユーザIDの昇順
                                .thenComparing(ReportOrderSlipStatement::orderIdLong)
                                .thenComparing(ReportOrderSlipStatement::currencyPair)
                                .thenComparing(ReportOrderSlipStatement::date))
                .toList();
    }

    private List<ReportOrderSlipStatement> createWriteDtoForPos(
            Symbol symbol, User user, PosOrder posOrder, List<PosTrade> posTrades, Date dateTo) {

        final var writeDtos = new ArrayList<ReportOrderSlipStatement>();

        writeDtos.add(ReportOrderSlipStatement.createNewOrder(symbol, user, posOrder));

        // 約定
        writeDtos.addAll(
                posTrades.stream()
                        .filter(symbols -> Objects.equals(symbols.getSymbolId(), symbol.getId()))
                        .map(
                                trade ->
                                        ReportOrderSlipStatement.createPosTrade(
                                                symbol, user, posOrder, trade))
                        .toList());

        // キャンセル
        if (posOrder.getUpdatedAt().before(dateTo)) {
            writeDtos.add(ReportOrderSlipStatement.createCancelOrder(symbol, user, posOrder));
        }
        return writeDtos;
    }

    public record ReadData(
            List<Symbol> symbols,
            List<User> users,
            List<PosOrder> posOrders,
            List<PosTrade> posTrades,
            Date dateTo) {}
}
