package point.admin.controller;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.admin.service.AdminUserService;
import point.common.component.CsvDownloadManager;
import point.common.component.CustomLogger;
import point.common.component.DataSourceManager;
import point.common.constant.ErrorCode;
import point.common.constant.FiatWithdrawalStatus;
import point.common.constant.ReportLabel;
import point.common.constant.ViewVariables;
import point.common.entity.FiatWithdrawal;
import point.common.exception.CustomException;
import point.common.model.request.FiatWithdrawalCsvForm;
import point.common.model.request.FiatWithdrawalInputForm;
import point.common.model.request.FiatWithdrawalPutForm;
import point.common.model.response.FiatWithdrawalReportData;
import point.common.model.response.PageData;
import point.common.service.BankAccountService;
import point.common.service.FiatWithdrawalService;
import point.common.service.UserService;
import point.common.util.DateUnit;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/fiat-withdrawal")
public class V1FiatWithdrawalRestController extends ExchangeAdminController {

    private final AdminUserService adminUserService;
    private final UserService userService;
    private final BankAccountService bankAccountService;
    private final FiatWithdrawalService fiatWithdrawalService;
    private final DataSourceManager dataSourceManager;

    private static final CustomLogger log =
            new CustomLogger(ExchangeAdminController.class.getName());

    @Autowired private final CsvDownloadManager<FiatWithdrawalReportData> downloadManager;

    private final String reportPreFix = "日本円出金履歴_";

    @GetMapping
    @PreAuthorize("@auth.check('jpy-withdrawal')")
    public ResponseEntity<PageData<FiatWithdrawal>> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "fiatWithdrawalStatus", required = false)
                    String fiatWithdrawalStatus,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {

        Long targetDateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;
        return ResponseEntity.ok(
                fiatWithdrawalService.findByConditionPageData(
                        userId,
                        dateFrom,
                        targetDateTo,
                        FiatWithdrawalStatus.valueOfName(fiatWithdrawalStatus),
                        number,
                        size));
    }

    @GetMapping("/{id}")
    public ResponseEntity<FiatWithdrawal> get(
            @AuthenticationPrincipal AdminUser adminUser, @PathVariable Long id) throws Exception {

        return ResponseEntity.ok(fiatWithdrawalService.findOne(id));
    }

    @GetMapping("/download")
    public void download(
            HttpServletResponse response,
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "fiatWithdrawalStatus", required = false)
                    String fiatWithdrawalStatus,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {
        Long targetDateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;
        String sql =
                """
            SELECT DISTINCT
                fw.id,
                fw.created_at,
                CASE WHEN u.user_info_id IS NOT NULL THEN CONCAT(ui.last_name, ' ', ui.first_name) END,
                fw.user_id,
                fw.amount,
                fw.amount - fw.fee as tatal_amount,
                fw.fee,
                b.bank_name,
                b.branch_name,
                ba.account_type,
                ba.account_number,
                ba.account_name,
                fw.updated_by,
                fw.updated_at,
                fw.fiat_withdrawal_status,
                fw.comment
            FROM
                fiat_withdrawal fw
            LEFT JOIN
              fiat_withdrawal_audit fwa ON fwa.fiat_withdrawal_id = fw.id
            LEFT JOIN
              bank_account ba ON fw.bank_account_id = ba.id
            LEFT JOIN
              bank b ON ba.bank_id = b.id
            LEFT JOIN
              user u ON fw.user_id = u.id
            LEFT JOIN
              user_info ui ON ui.id = u.user_info_id
            WHERE 1 = 1
                and fw.created_at >= :from
                and fw.created_at <= :to
        """;

        // dynamic sql
        if (userId != null) sql += " and fw.user_id = :uid ";
        if (fiatWithdrawalStatus != null)
            sql += "and fw.fiat_withdrawal_status = :fiatWithdrawalStatus";

        sql += " ORDER BY fw.id, fw.created_at ";

        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
        List<FiatWithdrawalReportData> statements = new ArrayList<>();

        try {
            Query query = entityManager.createNativeQuery(sql);

            // parameter for dynamic sql
            if (userId != null) query.setParameter("uid", userId);
            if (fiatWithdrawalStatus != null)
                query.setParameter("fiatWithdrawalStatus", fiatWithdrawalStatus);

            DateTimeFormatter formatter =
                    DateTimeFormatter.ofPattern("yyyy-MM-dd")
                            .withLocale(Locale.JAPAN)
                            .withZone(ZoneId.systemDefault());
            query.setParameter(
                    "from",
                    dateFrom != null
                            ? formatter.format(Instant.ofEpochMilli(dateFrom))
                            : "2000-01-01");
            query.setParameter(
                    "to",
                    dateTo != null
                            ? formatter.format(
                                    Instant.ofEpochMilli(dateTo).plus(1, ChronoUnit.DAYS))
                            : "2100-01-01");

            @SuppressWarnings("unchecked")
            List<Object[]> list = query.getResultList();
            NumberFormat numberFormat = NumberFormat.getNumberInstance();
            for (int i = 0; i < list.size(); i++) {
                FiatWithdrawalReportData statement = new FiatWithdrawalReportData();
                Object[] rec = list.get(i);
                statement.setId(String.valueOf(rec[0]));
                statement.setCreatedAt(
                        FormatUtil.formatJst(
                                FormatUtil.parse(
                                        String.valueOf(rec[1]),
                                        FormatPattern.YYYY_MM_DD_HH_MM_SS_S),
                                FormatPattern.YYYY_MM_DD_HH_MM_SS_S));
                statement.setUsername(String.valueOf(rec[2]));
                statement.setUserId(String.valueOf(rec[3]));
                statement.setAmount(
                        toStrigForReportNotByCurrency(
                                new BigDecimal(String.valueOf(rec[4])).stripTrailingZeros(),
                                numberFormat));
                statement.setTotalAmount(
                        toStrigForReportNotByCurrency(
                                new BigDecimal(String.valueOf(rec[5])).stripTrailingZeros(),
                                numberFormat));
                statement.setFee(
                        toStrigForReportNotByCurrency(
                                new BigDecimal(String.valueOf(rec[6])).stripTrailingZeros(),
                                numberFormat));
                statement.setBankName(String.valueOf(rec[7]));
                statement.setBranchName(String.valueOf(rec[8]));
                statement.setBankAccountType(String.valueOf(rec[9]));
                statement.setBankAccountNumber(String.valueOf(rec[10]));
                statement.setBankAccountName(String.valueOf(rec[11]));
                if (!String.valueOf(rec[12]).equals("null")) {
                    statement.setApprovedBy1(String.valueOf(rec[12]));
                    statement.setApprovedAt1(
                            FormatUtil.formatJst(
                                    FormatUtil.parse(
                                            String.valueOf(rec[13]),
                                            FormatPattern.YYYY_MM_DD_HH_MM_SS_S),
                                    FormatPattern.YYYY_MM_DD_HH_MM_SS_S));
                }
                statement.setStatus(
                        ReportLabel.FiatWithdrawalStatus.valueOfName(String.valueOf(rec[14]))
                                .getLabel());
                statement.setComment(String.valueOf(rec[15]));
                statements.add(statement);
            }
        } finally {
            entityManager.clear();
            entityManager.close();
        }

        downloadManager.download(response, statements, reportPreFix, true);
    }

    private static String toStrigForReportNotByCurrency(
            BigDecimal value, NumberFormat numberFormat) {
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue = value.toPlainString();
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return numberFormat.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }

    @PostMapping("/formregist")
    public ResponseEntity<FiatWithdrawal> post(
            @AuthenticationPrincipal AdminUser adminUser,
            @Valid @RequestBody FiatWithdrawalInputForm form)
            throws Exception {

        if (userService.findOne(form.getUserId()) == null) {
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }
        if (bankAccountService.findOne(form.getBankAccountId()) == null) {
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }
        FiatWithdrawal bankAccountWithdrawal = new FiatWithdrawal();
        bankAccountWithdrawal.setUserId(form.getUserId());
        bankAccountWithdrawal.setBankAccountId(form.getBankAccountId());
        bankAccountWithdrawal.setAmount(form.getAmount());
        bankAccountWithdrawal.setFee(form.getFee());
        bankAccountWithdrawal.setFiatWithdrawalStatus(
                FiatWithdrawalStatus.valueOf(form.getFiatWithdrawalStatus()));
        bankAccountWithdrawal.setComment(form.getComment());

        return ResponseEntity.ok(fiatWithdrawalService.save(bankAccountWithdrawal));
    }

    @PostMapping("/csvregist")
    public ResponseEntity<Serializable> post(
            @AuthenticationPrincipal AdminUser adminUser,
            @Valid @RequestBody FiatWithdrawalCsvForm[] res)
            throws Exception {

        // userId,bankAccountId exist?
        List<Long> inputUserIds = new ArrayList<Long>();
        List<Long> inputBankAccountIds = new ArrayList<Long>();
        for (FiatWithdrawalCsvForm form : res) {
            if (!inputUserIds.contains(form.getUserId())) {
                inputUserIds.add(form.getUserId());
                if (userService.findOne(form.getUserId()) == null) {
                    throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
                }
            }
            if (!inputBankAccountIds.contains(form.getBankAccountId())) {
                inputBankAccountIds.add(form.getBankAccountId());
                if (bankAccountService.findOne(form.getBankAccountId()) == null) {
                    throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
                }
            }
        }

        ArrayList<FiatWithdrawal> bankAccountWithdrawals = new ArrayList<>();

        for (FiatWithdrawalCsvForm form : res) {
            FiatWithdrawal bankAccountWithdrawal = new FiatWithdrawal();
            bankAccountWithdrawal.setUserId(form.getUserId());
            bankAccountWithdrawal.setBankAccountId(form.getBankAccountId());
            bankAccountWithdrawal.setAmount(form.getAmount());
            bankAccountWithdrawal.setFee(form.getFee());
            bankAccountWithdrawal.setFiatWithdrawalStatus(
                    FiatWithdrawalStatus.valueOf(form.getFiatWithdrawalStatus()));
            bankAccountWithdrawal.setComment(form.getComment());

            bankAccountWithdrawals.add(fiatWithdrawalService.save(bankAccountWithdrawal));
        }

        return ResponseEntity.ok(bankAccountWithdrawals);
    }

    @PutMapping
    public ResponseEntity<FiatWithdrawal> put(
            @AuthenticationPrincipal AdminUser adminUser,
            @Valid @RequestBody FiatWithdrawalPutForm form)
            throws Exception {

        FiatWithdrawal fiatWithdrawal = fiatWithdrawalService.findOne(form.getId());

        // 拒否の場合、ロック資産を資産に戻す。。
        if (FiatWithdrawalStatus.valueOf(form.getFiatWithdrawalStatus())
                == FiatWithdrawalStatus.REJECTED) {
            return ResponseEntity.ok(
                    fiatWithdrawalService.rejectWithDrawal(
                            fiatWithdrawal.getId(), form.getComment()));
        }

        // 現状が承認済で、リクエストが処理済の場合ロック資産が減少する。
        if (FiatWithdrawalStatus.valueOf(form.getFiatWithdrawalStatus())
                == FiatWithdrawalStatus.DONE) {
            return ResponseEntity.ok(fiatWithdrawalService.applyWithDrawal(fiatWithdrawal.getId()));
        }

        fiatWithdrawal.setFiatWithdrawalStatus(
                FiatWithdrawalStatus.valueOf(form.getFiatWithdrawalStatus()));
        fiatWithdrawal.setComment(form.getComment());

        return ResponseEntity.ok(fiatWithdrawalService.save(fiatWithdrawal));
    }

    @DeleteMapping
    public ResponseEntity<FiatWithdrawal> delete(
            @AuthenticationPrincipal AdminUser adminUser, @RequestParam(value = "id") Long id)
            throws Exception {

        return ResponseEntity.ok(fiatWithdrawalService.cancel(id));
    }
}
