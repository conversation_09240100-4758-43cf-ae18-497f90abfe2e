package point.admin.controller;

import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import point.admin.model.response.PointTransferPageData;
import point.admin.model.response.PointTransferStatusHistoryData;
import point.common.constant.PointTransferStatusEnum;
import point.common.constant.PointTransferTypeEnum;
import point.common.constant.UserIdType;
import point.common.constant.ViewVariables;
import point.common.entity.PointTransfer;
import point.common.entity.PointTransferStatusHistory;
import point.common.model.response.PageData;
import point.common.service.PointTransferService;
import point.common.service.PointTransferStatusHistoryService;

@RestController
@RequestMapping("/admin/v1/point-transfer")
@RequiredArgsConstructor
public class V1PointTransferRestController {

    private final PointTransferService pointTransferService;

    private final PointTransferStatusHistoryService pointTransferStatusHistoryService;

    @GetMapping("page")
    @PreAuthorize("@auth.check('transfer-list')")
    public ResponseEntity<PageData<PointTransferPageData>> get(
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "partnerId", required = false) Long partnerId,
            @RequestParam(value = "partnerName", required = false) String partnerName,
            @RequestParam(value = "partnerMemberId", required = false) String partnerMemberId,
            @RequestParam(value = "transferType", required = false)
                    PointTransferTypeEnum transferType,
            @RequestParam(value = "status", required = false) PointTransferStatusEnum status,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size) {
        PageData<PointTransferPageData> pointTransferPageDataResponse =
                new PageData<>(number, size, 0, null);
        PageData<PointTransfer> pointTransferPageData =
                pointTransferService.findByConditionPage(
                        userId,
                        partnerName,
                        partnerId,
                        partnerMemberId,
                        transferType,
                        status,
                        dateFrom,
                        dateTo,
                        UserIdType.Operate,
                        number,
                        size);
        pointTransferPageData
                .getContent()
                .forEach(
                        pointTransfer -> {
                            pointTransferPageDataResponse
                                    .getContent()
                                    .add(
                                            PointTransferPageData.builder()
                                                    .id(pointTransfer.getId())
                                                    .userId(pointTransfer.getUserId())
                                                    .partnerNumber(
                                                            pointTransfer
                                                                    .getPointPartner()
                                                                    .getPartnerNumber())
                                                    .partnerName(
                                                            pointTransfer
                                                                    .getPointPartner()
                                                                    .getName())
                                                    .partnerMemberId(
                                                            pointTransfer
                                                                    .getPointUser()
                                                                    .getPartnerMemberId())
                                                    .amount(pointTransfer.getAmount())
                                                    .fee(pointTransfer.getFee())
                                                    .status(pointTransfer.getStatus().name())
                                                    .createdAt(pointTransfer.getCreatedAt())
                                                    .build());
                        });
        if (CollectionUtils.isEmpty(pointTransferPageData.getContent())) {
            return ResponseEntity.ok().body(pointTransferPageDataResponse);
        }
        return ResponseEntity.ok()
                .body(
                        new PageData<>(
                                number,
                                size,
                                pointTransferPageData.getTotalElements(),
                                pointTransferPageDataResponse.getContent()));
    }

    @GetMapping("/list/{id}")
    @PreAuthorize("@auth.check('transfer-list')")
    public ResponseEntity<List<PointTransferStatusHistoryData>> getDetail(
            @PathVariable("id") Long id) {
        List<PointTransferStatusHistoryData> pointTransferStatusHistoryDataList = new ArrayList<>();
        PointTransfer pointTransfer = pointTransferService.findOne(id);
        List<PointTransferStatusHistory> pointTransferStatusHistoryList =
                pointTransferStatusHistoryService.findByTransferId(id);
        pointTransferStatusHistoryList.forEach(
                pointTransferStatusHistory -> {
                    pointTransferStatusHistoryDataList.add(PointTransferStatusHistoryData.builder()
                            .id(pointTransferStatusHistory.getId())
                            .userId(pointTransfer.getUserId())
                            .partnerNumber(
                                    pointTransfer.getPointPartner().getPartnerNumber())
                            .partnerName(pointTransfer.getPointPartner().getName())
                            .amount(pointTransfer.getAmount())
                            .fee(pointTransfer.getFee())
                            .previousStatus(pointTransferStatusHistory.getPreviousStatus())
                            .currentStatus(pointTransferStatusHistory.getCurrentStatus())
                            .createdAt(pointTransferStatusHistory.getCreatedAt())
                            .remarks(PointTransferStatusEnum.FAILED.getCode().equals(pointTransferStatusHistory.getCurrentStatus())
                                    ? pointTransferStatusHistory.getRemarks()
                                    : null)
                            .build());
                });
        return ResponseEntity.ok().body(pointTransferStatusHistoryDataList);
    }
}
