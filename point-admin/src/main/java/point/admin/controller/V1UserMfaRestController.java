package point.admin.controller;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.common.constant.MfaType;
import point.common.entity.UserMfa;
import point.common.service.UserMfaService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/user-mfa")
public class V1UserMfaRestController extends ExchangeAdminController {

    private final UserMfaService userMfaService;

    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    record UserMfaResponse(Long userId, String mfaType, boolean authenticated) {}

    @GetMapping
    public ResponseEntity<UserMfaResponse> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "mfaType") MfaType mfaType,
            @RequestParam(value = "userId") Long userId)
            throws Exception {

        UserMfa userMfa = userMfaService.findByCondition(userId, mfaType);
        if (userMfa == null) {
            return ResponseEntity.ok(null);
        }

        return ResponseEntity.ok(
                new UserMfaResponse(
                        userMfa.getId(),
                        userMfa.getMfaType().toString(),
                        userMfa.isAuthenticated()));
    }

    @DeleteMapping
    public ResponseEntity<Object> delete(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "mfaType") MfaType mfaType,
            @RequestParam(value = "userId") Long userId)
            throws Exception {

        UserMfa userMfa = userMfaService.findByCondition(userId, mfaType);
        userMfaService.delete(userMfa);
        return ResponseEntity.ok().build();
    }
}
