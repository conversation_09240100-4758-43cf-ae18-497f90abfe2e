package point.admin.component;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.*;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;
import point.admin.entity.AdminUser;
import point.admin.service.AdminUserLoginAttemptService;
import point.admin.service.AdminUserService;
import point.common.constant.ErrorCode;
import point.common.exception.LoginExcepiton;
import point.common.model.response.ErrorData;

@RequiredArgsConstructor
@Component
public class AuthenticationFailureHandlerImpl implements AuthenticationFailureHandler {

    private final AdminUserService adminUserService;

    private final AdminUserLoginAttemptService adminUserLoginAttemptService;

    @Override
    public void onAuthenticationFailure(
            HttpServletRequest request,
            HttpServletResponse response,
            AuthenticationException exception)
            throws IOException, ServletException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        ErrorCode errorCode = ErrorCode.REQUEST_ERROR_UNAUTHORIZED;

        if (exception instanceof AccountExpiredException) {
            errorCode = ErrorCode.REQUEST_ERROR_USER_ACCOUNT_NON_EXPIRED;
        }
        if (exception.getCause() instanceof LockedException) {
            // account locked
            errorCode = ErrorCode.REQUEST_ERROR_UNAUTHORIZED_ACCOUNT_LOCKED;
        }
        if (exception instanceof CredentialsExpiredException) {
            errorCode = ErrorCode.REQUEST_ERROR_USER_CREDENTIALS_EXPIRED;
        }
        if (exception instanceof DisabledException) {
            errorCode = ErrorCode.REQUEST_ERROR_USER_DISABLED;
        }
        if (exception.getCause() instanceof LoginExcepiton) {
            // custom exception
            errorCode = ErrorCode.ADMIN_USER_ROLE_NOT_FOUND;
        }
        response.getWriter().write(new ObjectMapper().writeValueAsString(new ErrorData(errorCode)));

        if (exception.getClass() == BadCredentialsException.class) {
            AdminUser adminUser =
                    adminUserService.loadUserByUsername(request.getParameter("email"));

            if (adminUser != null) {
                if (adminUserLoginAttemptService.countUp(adminUser.getId())) {
                    adminUser.setAccountNonLocked(false);
                    adminUserService.save(adminUser);
                }
            }
        }
    }
}
