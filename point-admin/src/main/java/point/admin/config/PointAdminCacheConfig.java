package point.admin.config;

import java.util.Locale;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;
import point.admin.entity.AdminUser;
import point.admin.entity.AdminUserAuthority;
import point.common.component.CustomRedisTemplate;
import point.common.config.CacheConfig;

@Configuration
@EnableCaching
public class PointAdminCacheConfig {

    @Bean
    public CustomRedisTemplate<AdminUserAuthority> adminUserAuthorityRedisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, AdminUserAuthority> redisTemplate = new RedisTemplate<>();
        CacheConfig.setRedisTemplateParameters(redisTemplate, redisConnectionFactory);
        return new CustomRedisTemplate<>(redisTemplate);
    }

    @Bean
    public CustomRedisTemplate<AdminUser> adminUserRedisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, AdminUser> redisTemplate = new RedisTemplate<>();
        CacheConfig.setRedisTemplateParameters(redisTemplate, redisConnectionFactory);
        return new CustomRedisTemplate<>(redisTemplate);
    }

    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver slr = new SessionLocaleResolver();
        slr.setDefaultLocale(Locale.ENGLISH);
        return slr;
    }
}
