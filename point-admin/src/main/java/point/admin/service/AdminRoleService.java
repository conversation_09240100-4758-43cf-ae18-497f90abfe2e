package point.admin.service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.admin.model.response.AdminMenuData;
import point.admin.model.response.AdminRoleData;
import point.admin.model.response.AdminRoleMenuData;
import point.common.constant.ErrorCode;
import point.common.entity.AdminMenu;
import point.common.entity.AdminRole;
import point.common.exception.CustomException;
import point.common.repos.AdminMenuRepository;
import point.common.repos.AdminRoleRepository;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdminRoleService {

    public final AdminRoleRepository adminRoleRepository;

    public final AdminMenuRepository adminMenuRepository;

    public List<AdminRoleData> searchAll() {
        List<AdminRole> list = adminRoleRepository.findAllByEnableTrueOrderByIdAsc();
        return list.stream().map(AdminRoleData::new).collect(Collectors.toList());
    }

    public void roleEnableSwitch(Long id, boolean enable) throws CustomException {
        AdminRole role =
                adminRoleRepository
                        .findById(id)
                        .orElseThrow(
                                () -> new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR));
        role.setEnable(enable);
        adminRoleRepository.save(role);
    }

    public AdminRoleMenuData getRoleMenu(Long id) throws CustomException {
        AdminRoleMenuData data = new AdminRoleMenuData();

        List<AdminMenu> list = adminMenuRepository.findAll();

        AdminRole role =
                adminRoleRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new CustomException(
                                                ErrorCode.REQUEST_ERROR_ROLE_DISABLE_OR_NOT_FOUND));
        List<AdminMenu> ownMenu = role.getMenus();
        data.setRoleName(role.getRoleName());
        List<Long> ownedIds = ownMenu.stream().map(AdminMenu::getId).toList();
        List<AdminMenuData> result = new ArrayList<>();
        for (AdminMenu menu : list) {
            AdminMenuData menuData = new AdminMenuData(menu);
            menuData.setOwned(ownedIds.contains(menuData.getId()));
            result.add(menuData);
        }
        data.setMenus(result);
        return data;
    }

    public void updateRole(String roleName, Long id, List<Long> menus) throws CustomException {
        AdminRole adminRole = adminRoleRepository.findByRoleName(roleName);
        if (adminRole != null) {
            if (!adminRole.getId().equals(id)) {
                throw new CustomException(ErrorCode.REQUEST_ERROR_ROLE_DUPLICATE);
            }
        }
        AdminRole role =
                adminRoleRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new CustomException(
                                                ErrorCode.REQUEST_ERROR_ROLE_DISABLE_OR_NOT_FOUND));
        List<AdminMenu> list = adminMenuRepository.findByIdIn(menus);
        role.setMenus(list);
        role.setRoleName(roleName);
        adminRoleRepository.save(role);
    }

    public void saveRole(String roleName, List<Long> menus) throws CustomException {
        AdminRole adminRole = adminRoleRepository.findByRoleName(roleName);
        if (adminRole != null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_ROLE_DUPLICATE);
        }
        AdminRole role = new AdminRole();
        List<AdminMenu> list = adminMenuRepository.findByIdIn(menus);
        role.setMenus(list);
        role.setRoleName(roleName);
        role.setEnable(true);
        adminRoleRepository.save(role);
    }

    public List<String> getMenuKey(Long roleId) {
        AdminRole role = adminRoleRepository.findById(roleId).orElse(new AdminRole());
        if (role.getId() == null) {
            return new ArrayList<>();
        }
        return role.getMenus().stream().map(AdminMenu::getMenuKey).collect(Collectors.toList());
    }
}
