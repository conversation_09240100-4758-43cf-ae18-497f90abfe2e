package point.admin.service;

import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.admin.model.response.AdminMenuPageData;
import point.common.entity.AdminMenu;
import point.common.repos.AdminMenuRepository;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdminMenuService {

    public final AdminMenuRepository adminMenuRepository;

    public List<AdminMenuPageData> getMenus() {
        List<AdminMenu> list = adminMenuRepository.findAll();
        return list.stream().map(AdminMenuPageData::new).collect(Collectors.toList());
    }
}
