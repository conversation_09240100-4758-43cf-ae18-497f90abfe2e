package point.admin.model.response;

import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

public class ReportCustomerDepositStatement implements Serializable {

    private static final long serialVersionUID = 1L;

    public static class NewAssetSummary implements Serializable {

        private static final long serialVersionUID = 5051383513706533061L;

        @Getter @Setter private String userid;

        @Getter @Setter private String username;

        @Getter @Setter private String balance;

        @Getter @Setter private String applyamount;

        @Getter @Setter private String btcamount;

        @Getter @Setter private String ethamount;

        @Getter @Setter private String xrpamount;

        @Getter @Setter private String applyethamount;
    }

    @Getter @Setter private List<NewAssetSummary> NewAssetSummaryList;

    @Getter @Setter private String balancetotal;

    @Getter @Setter private String btcamounttotal;

    @Getter @Setter private String ethamounttotal;

    @Getter @Setter private String xrpamounttotal;
}
