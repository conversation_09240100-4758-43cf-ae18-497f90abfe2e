package point.admin.model.response;

import java.math.BigDecimal;
import java.util.Date;
import lombok.*;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PointTransferStatusHistoryData {
    private Long id;
    private Long userId;
    private String partnerNumber;
    private String partnerName;
    private String partnerMemberId;
    private BigDecimal amount;
    private BigDecimal fee;
    private String previousStatus;
    private String currentStatus;
    private String remarks;
    private Date createdAt;
    private Date updatedAt;
}
