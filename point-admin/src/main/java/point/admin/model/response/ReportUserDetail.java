package point.admin.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import point.common.constant.*;
import point.common.entity.*;
import point.common.exception.CustomException;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

@JsonPropertyOrder({
    "ユーザーID",
    "ユーザータイプ",
    "ユーザーステータス",
    "EKYCステータス",
    "メールアドレス",
    "ユーザー名",
    "ユーザー名カナ",
    "郵便番号",
    "住所",
    "EKYCステータス更新日時",
    "内部者該当",
    "自社口座",
    "在留資格",
    "在留カード期限",
    "生年月日（設立日）",
    "決算月",
    "性別",
    "電話番号",
    "職業",
    "勤務先（または屋号）",
    "部署・役職",
    "事業内容",
    "収入源",
    "年商・年収",
    "金融資産額",
    "主なご利用目的",
    "投資目的",
    "投資経験(仮想通貨)",
    "投資経験(FX)",
    "投資経験(株式)",
    "投資経験(投資信託)",
    "申込経緯",
    "経由",
    "UUID"
})
@Slf4j
public class ReportUserDetail {

    @Getter
    @Setter
    @JsonProperty("ユーザーID")
    private String userId = "";

    @Getter
    @Setter
    @JsonProperty("ユーザータイプ")
    private String userAuthority = "";

    @Getter
    @Setter
    @JsonProperty("ユーザーステータス")
    private String userStatus = "";

    @Getter
    @Setter
    @JsonProperty("経由")
    private String affiliateFrom = "";

    @Getter
    @Setter
    @JsonProperty("UUID")
    private String uuid = "";

    @Getter
    @Setter
    @JsonProperty("EKYCステータス")
    private String kycStatus = "";

    @Getter
    @Setter
    @JsonProperty("メールアドレス")
    private String email = "";

    @Getter
    @Setter
    @JsonProperty("ユーザー名")
    private String userName = "";

    @Getter
    @Setter
    @JsonProperty("ユーザー名カナ")
    private String userKana = "";

    @Getter
    @Setter
    @JsonProperty("郵便番号")
    private String zipCode = "";

    @Getter
    @Setter
    @JsonProperty("住所")
    private String address = "";

    @Getter
    @Setter
    @JsonProperty("EKYCステータス更新日時")
    private String kycStatusUpdatedAt = "";

    @Getter
    @Setter
    @JsonProperty("内部者該当")
    private String insider = "";

    @Getter
    @Setter
    @JsonProperty("自社口座")
    private String insideAccountFlg = "";

    @Getter
    @Setter
    @JsonProperty("在留資格")
    private String residenceStatus = StringUtils.EMPTY;

    @Getter
    @Setter
    @JsonProperty("在留カード期限")
    private String residenceCardExpiredAt = "";

    @Getter
    @Setter
    @JsonProperty("生年月日（設立日）")
    private String birthdayOrEstablishedDay = "";

    @Getter
    @Setter
    @JsonProperty("性別")
    private String gender = "";

    @Getter
    @Setter
    @JsonProperty("電話番号")
    private String phoneNumber = "";

    @Getter
    @Setter
    @JsonProperty("職業")
    private String occupation = "";

    @Getter
    @Setter
    @JsonProperty("業種")
    private String industry = "";

    @Getter
    @Setter
    @JsonProperty("勤務先（または屋号）")
    private String workPlace = "";

    @Getter
    @Setter
    @JsonProperty("部署・役職")
    private String position = "";

    @Getter
    @Setter
    @JsonProperty("収入源")
    private String priceFrom = "";

    @Getter
    @Setter
    @JsonProperty("年商・年収")
    private String incomeOrSales = "";

    @Getter
    @Setter
    @JsonProperty("金融資産額")
    private String financialAssets = "";

    @Getter
    @Setter
    @JsonProperty("主なご利用目的")
    private String purpose = "";

    @Getter
    @Setter
    @JsonProperty("投資目的")
    private String investmentPurpose = "";

    @Getter
    @Setter
    @JsonProperty("投資経験(仮想通貨)")
    private String cryptoExperience = "";

    @Getter
    @Setter
    @JsonProperty("投資経験(FX)")
    private String fxExperience = "";

    @Getter
    @Setter
    @JsonProperty("投資経験(株式)")
    private String stocksExperience = "";

    @Getter
    @Setter
    @JsonProperty("投資経験(投資信託)")
    private String fundExperience = "";

    @Getter
    @Setter
    @JsonProperty("申込経緯")
    private String applicationHistory = "";

    @Getter
    @Setter
    @JsonProperty("申込経緯(その他)")
    private String applicationHistoryOther = "";

    public ReportUserDetail(User user) throws CustomException {
        this.userId = user.getId().toString();
        String authority = "";
        try {
            authority = user.getAuthorities().get(0).getAuthority();
        } catch (Exception e) {
            log.error("authority not found!" + user.getId().toString());
            throw e;
        }
        this.userAuthority = ReportLabel.Authority.valueOfName(authority).getLabel();
        this.userStatus =
                ReportLabel.UserStatus.valueOfName(user.getUserStatus().toString()).getLabel();
        this.affiliateFrom =
                user.getAffiliateInfo() != null ? user.getAffiliateInfo().getAffiliateName() : "";
        this.uuid = user.getUuid() != null ? user.getUuid() : "";
        this.kycStatus =
                ReportLabel.KycStatus.valueOfName(user.getKycStatus().toString()).getLabel();
        this.email = user.getEmail();
        this.insider = user.isInsider() ? "有" : "無";
        this.insideAccountFlg = user.isInsideAccountFlg() ? "自社" : "非自社";

        // 名前・住所情報等
        if (authority == Authority.PERSONAL.toString()) {
            // 個人ユーザー
            UserInfo userInfo = user.getUserInfo();
            if (userInfo != null) {
                this.userName = userInfo.getLastName() + userInfo.getFirstName();
                this.userKana = userInfo.getLastKana() + userInfo.getFirstKana();
                this.zipCode = userInfo.getZipCode();
                String building = userInfo.getBuilding();
                this.address =
                        Prefecture.valueOfCode(userInfo.getPrefecture()).getKanjiName()
                                + userInfo.getCity()
                                + userInfo.getAddress()
                                + (building == null ? "" : building);
                this.birthdayOrEstablishedDay = userInfo.getBirthday();
                this.gender = Gender.valueOfCode(userInfo.getGender()).getKanjiName();
                this.phoneNumber = userInfo.getPhoneNumber();
                this.occupation =
                        ReportLabel.Occupation.valueOfCode(userInfo.getOccupation()).getKanjiName();
                if (userInfo.getIndustry() != null) {
                    this.industry =
                            ReportLabel.Industry.valueOfCode(userInfo.getIndustry()).getKanjiName();
                }
                if (userInfo.getWorkPlace() != null) {
                    this.workPlace = userInfo.getWorkPlace();
                }
                if (userInfo.getPosition() != null) {
                    this.position = userInfo.getPosition();
                }
                if (userInfo.getPriceFrom() != null) {
                    this.priceFrom =
                            Objects.requireNonNull(
                                            ReportLabel.PriceFrom.valueOfCode(
                                                    userInfo.getPriceFrom()))
                                    .getKanjiName();
                }
                this.incomeOrSales =
                        ReportLabel.Income.valueOfCode(userInfo.getIncome()).getKanjiName();
                this.financialAssets =
                        ReportLabel.Income.valueOfCode(userInfo.getFinancialAssets())
                                .getKanjiName();
                this.purpose =
                        ReportLabel.Purpose.valueOfCode(userInfo.getPurpose()).getKanjiName();
                this.investmentPurpose =
                        ReportLabel.InvestmentPurpose.valueOfCode(userInfo.getInvestmentPurposes())
                                .getKanjiName();
                this.cryptoExperience =
                        ReportLabel.Experience.valueOfCode(userInfo.getCryptoExperience())
                                .getKanjiName();
                this.fxExperience =
                        ReportLabel.Experience.valueOfCode(userInfo.getFxExperience())
                                .getKanjiName();
                this.stocksExperience =
                        ReportLabel.Experience.valueOfCode(userInfo.getStocksExperience())
                                .getKanjiName();
                this.fundExperience =
                        ReportLabel.Experience.valueOfCode(userInfo.getFundExperience())
                                .getKanjiName();
                this.applicationHistory =
                        ReportLabel.ApplicationHistory.valueOfCode(userInfo.getApplicationHistory())
                                .getKanjiName();
                this.applicationHistoryOther = userInfo.getApplicationHistoryOther();
                this.residenceStatus = userInfo.getResidenceStatus();
                this.residenceCardExpiredAt =
                        FormatUtil.formatJst(
                                userInfo.getResidenceCardExpiredAt(),
                                FormatPattern.YYYY_MM_DD_SLASH);
            }
        }
        // KYC情報
        UserKyc userKyc = user.getUserKyc();
        if (userKyc != null) {
            this.kycStatusUpdatedAt =
                    FormatUtil.formatJst(
                            userKyc.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
        }
    }
}
