package point.admin.model.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Map;
import point.common.constant.*;
import point.common.entity.*;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.pos.entity.PosTrade;

@JsonPropertyOrder({
    "運用口座ID", "通貨", "顧客ID", "顧客名", "売付、買付", "約定年月日", "暗号資産の数量", "暗号資産の単価", "入金又は出金", "入出金日", "入出金額",
    "約定代金", "手数料", "差引残高", "取引タイプ"
})
public record ReportOwnStatement(
        @JsonProperty("運用口座ID") String systemUserId,
        @JsonProperty("顧客ID") String userId,
        @JsonProperty("顧客名") String userName,
        @JsonProperty("通貨") String currency,
        @JsonProperty("売付、買付") String orderSide,
        @JsonProperty("約定年月日") String tradedAt,
        @JsonProperty("暗号資産の数量") String amount,
        @JsonProperty("暗号資産の単価") String unitPrice,
        @JsonProperty("入金又は出金") String depositOrWithdrawal,
        @JsonProperty("入出金日") String transactionDate,
        @JsonProperty("入出金額") String totalPrice,
        @JsonProperty("約定代金") String agreedAmount,
        @JsonProperty("手数料") String fee,
        @JsonProperty("差引残高") String assetAmount,
        @JsonProperty("取引タイプ") String tradeType, // 取引タイプ
        @JsonIgnore BigDecimal balanceBigDecimal,
        @JsonIgnore Long date, // 作成日時
        @JsonIgnore int order, // 順番
        @JsonIgnore Long orderUserId // user順番
        ) {

    // 約定Base
    public static ReportOwnStatement createBase(
            Map<Long, Symbol> symbolMap,
            User user,
            PosTrade posTradeBase,
            BigDecimal balance,
            Map<Long, User> userMap) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        final var currency =
                symbolMap.get(posTradeBase.getSymbolId()).getCurrencyPair().getBaseCurrency();
        // 取引した顧客
        User transactionUser = userMap.get(posTradeBase.getUserId());
        String tradeTypeName = "";
        String systemUserId = "";
        String orderSide = "";
        tradeTypeName = PosConstants.INVEST_TRADE_TYPE_NAME;
        systemUserId = "-";
        orderSide = posTradeBase.getOrderSide().toString() == OrderSide.SELL.name() ? "買" : "売";
        return new ReportOwnStatement(
                systemUserId, // 運用口座ID
                transactionUser == null
                        ? ""
                        : transactionUser.getAuthorities().get(0).getUserId().toString(), // 顧客ID
                transactionUser == null
                        ? ""
                        : transactionUser.getUserInfo().getLastName()
                                + " "
                                + transactionUser.getUserInfo().getFirstName(), // 取引した顧客の氏名
                currency.name(), // 通貨
                orderSide, // 売付、買付
                FormatUtil.formatJst(
                        posTradeBase.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 約定年月日
                toStrigForReport(currency, posTradeBase.getAmount(), numberFormat), // 暗号資産の数量
                toStrigForReport(currency, posTradeBase.getPrice(), numberFormat), // 暗号資産の単価
                "-", // 入金又は出金
                "-", // 入出金日
                "-", // 入出金額
                "-", // 約定代金
                "-", // 手数料
                symbolMap.get(posTradeBase.getSymbolId()).getTradeType() == TradeType.INVEST
                        ? "-"
                        : toStrigForReport(
                                currency,
                                posTradeBase.ownNextBaseBalance(balance),
                                numberFormat), // 差引残高
                tradeTypeName, // 取引タイプ
                posTradeBase.ownNextBaseBalance(balance), // balanceのBigDecimal
                posTradeBase.getCreatedAt().getTime(), // 作成日時
                1, // 順番
                posTradeBase.getUserId() // user順番
                );
    }

    // 約定Quote
    public static ReportOwnStatement createQuote(
            Map<Long, Symbol> symbolMap,
            User user,
            PosTrade posTradeQuote,
            BigDecimal balance,
            Map<Long, User> userMap) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        // 取引した顧客
        User transactionUser = userMap.get(posTradeQuote.getUserId());
        final var currency =
                symbolMap.get(posTradeQuote.getSymbolId()).getCurrencyPair().getQuoteCurrency();
        String tradeTypeName = "";
        String systemUserId = "";
        String orderSide = "";

        tradeTypeName = PosConstants.INVEST_TRADE_TYPE_NAME;
        systemUserId = "-";
        orderSide = posTradeQuote.getOrderSide().toString() == OrderSide.SELL.name() ? "売" : "買";

        return new ReportOwnStatement(
                systemUserId, // 運用口座ID
                transactionUser == null
                        ? ""
                        : transactionUser.getAuthorities().get(0).getUserId().toString(), // 顧客ID
                transactionUser == null
                        ? ""
                        : transactionUser.getUserInfo().getLastName()
                                + " "
                                + transactionUser.getUserInfo().getFirstName(), // 取引した顧客の氏名
                currency.name(), // 通貨
                orderSide, // 売付、買付
                FormatUtil.formatJst(
                        posTradeQuote.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 約定年月日
                "-", // 暗号資産の数量
                "-", // 暗号資産の単価
                "-", // 入金又は出金
                "-", // 入出金日
                "-", // 入出金額
                toStrigForReport(currency, posTradeQuote.getAssetAmount(), numberFormat), // 約定代金
                posTradeQuote.getFee() == null
                                || posTradeQuote.getFee().compareTo(BigDecimal.ZERO) == 0
                        ? "-"
                        : toStrigForReport(currency, posTradeQuote.getFee(), numberFormat), // 手数料
                symbolMap.get(posTradeQuote.getSymbolId()).getTradeType() == TradeType.INVEST
                        ? "-"
                        : toStrigForReport(
                                currency,
                                posTradeQuote.ownNextQuoteBalance(balance),
                                numberFormat), // 差引残高
                tradeTypeName, // 取引タイプ
                posTradeQuote.ownNextQuoteBalance(balance), // balanceのBigDecimal
                posTradeQuote.getCreatedAt().getTime(), // 作成日時
                2, // 順番
                posTradeQuote.getUserId() // user順番
                );
    }

    // 日本円入金
    public static ReportOwnStatement create(
            User user, FiatDeposit fiatDeposit, BigDecimal balance) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        final var currency = Currency.JPY;
        return new ReportOwnStatement(
                user.getId().toString(), // 運用口座ID
                "-", // 顧客ID
                "-", // 取引した顧客の氏名
                currency.name(), // 通貨
                "-", // 売付、買付
                "-", // 約定年月日
                "-", // 暗号資産の数量
                "-", // 暗号資産の単価
                "入金", // 入金又は出金
                FormatUtil.formatJst(
                        fiatDeposit.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 入出金日
                toStrigForReport(
                        currency,
                        fiatDeposit.getAmount().subtract(fiatDeposit.getFee()),
                        numberFormat), // 入出金額
                "-", // 約定代金
                fiatDeposit.getFee() == null || fiatDeposit.getFee().compareTo(BigDecimal.ZERO) == 0
                        ? "-"
                        : toStrigForReport(currency, fiatDeposit.getFee(), numberFormat), // 手数料
                toStrigForReport(
                        currency, fiatDeposit.ownNextBalance(balance), numberFormat), // 差引残高
                "", // 取引タイプ
                fiatDeposit.ownNextBalance(balance), // balanceのBigDecimal
                fiatDeposit.getUpdatedAt().getTime(), // 作成日時
                3, // 順番
                fiatDeposit.getUserId() // user順番
                );
    }

    // 日本円出金
    public static ReportOwnStatement create(
            User user, FiatWithdrawal fiatWithdrawal, BigDecimal balance) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        final var currency = Currency.JPY;
        return new ReportOwnStatement(
                user.getId().toString(), // 運用口座ID
                "-", // 顧客ID
                "-", // 取引した顧客の氏名
                currency.name(), // 通貨
                "-", // 売付、買付
                "-", // 約定年月日
                "-", // 暗号資産の数量
                "-", // 暗号資産の単価
                "出金", // 入金又は出金
                FormatUtil.formatJst(
                        fiatWithdrawal.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 入出金日
                toStrigForReport(
                        currency,
                        fiatWithdrawal.getAmount().subtract(fiatWithdrawal.getFee()),
                        numberFormat), // 入出金額
                "-", // 約定代金
                fiatWithdrawal.getFee() == null
                                || fiatWithdrawal.getFee().compareTo(BigDecimal.ZERO) == 0
                        ? "-"
                        : toStrigForReport(currency, fiatWithdrawal.getFee(), numberFormat), // 手数料
                toStrigForReport(
                        currency, fiatWithdrawal.ownNextBalance(balance), numberFormat), // 差引残高
                "", // 取引タイプ
                fiatWithdrawal.ownNextBalance(balance), // balanceのBigDecimal
                fiatWithdrawal.getUpdatedAt().getTime(), // 作成日時
                3, // 順番
                fiatWithdrawal.getUserId() // user順番
                );
    }

    private static String toStrigForReport(
            Currency currency, BigDecimal value, NumberFormat numberFormat) {
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue =
                currency.getScaledAmount(value, RoundingMode.FLOOR)
                        .stripTrailingZeros()
                        .toPlainString();
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return numberFormat.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }
}
