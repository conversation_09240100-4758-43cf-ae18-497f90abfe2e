package point.admin.model.response;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import java.util.List;
import lombok.*;
import point.common.entity.User;
import point.common.model.dto.UserAgreementDTO;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserDetailWrapper {

    @JsonUnwrapped private User user;

    /** the note of user_note */
    private String note;

    private List<UserAgreementDTO> userAgreements;
}
