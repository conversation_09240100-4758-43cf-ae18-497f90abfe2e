package point.admin.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import point.common.constant.*;
import point.common.entity.Symbol;
import point.common.entity.User;
import point.pos.entity.PosTrade;

@JsonPropertyOrder({
    "約定年月日",
    "ユーザID",
    "利用者の氏名又は名称",
    "自己又は取次ぎの別",
    "売付け・買付け又は他の暗号資産との交換の別",
    "暗号資産の名称",
    "暗号資産の数量",
    "約定単価",
    "約定金額",
    "相手方の氏名又は名称（取次ぎの場合）",
    "取引に関して受け取る手数料、報酬その他の対価の額（取次ぎの場合）",
    "送金状態",
    "取引タイプ"
})
public class ReportTradesStatement {

    @Getter
    @Setter
    @JsonProperty("約定年月日")
    private String tradeDate;

    @Getter
    @Setter
    @JsonProperty("ユーザID")
    private String userId;

    @Getter
    @Setter
    @JsonProperty("利用者の氏名又は名称")
    private String userName;

    @Getter
    @Setter
    @JsonProperty("自己又は取次ぎの別")
    private String insiderFlg;

    @Getter
    @Setter
    @JsonProperty("売付け・買付け又は他の暗号資産との交換の別")
    private String posTradeType;

    @Getter
    @Setter
    @JsonProperty("暗号資産の名称")
    private String currencyPair;

    @Getter
    @Setter
    @JsonProperty("暗号資産の数量")
    private String tradeAmount;

    @Getter
    @Setter
    @JsonProperty("約定単価")
    private String tradePrice;

    @Getter
    @Setter
    @JsonProperty("約定金額")
    private String tradePricetotal;

    @Getter
    @Setter
    @JsonProperty("相手方の氏名又は名称（取次ぎの場合）")
    private String tradeName;

    @Getter
    @Setter
    @JsonProperty("取引に関して受け取る手数料、報酬その他の対価の額（取次ぎの場合）")
    private String fee;

    @Getter
    @Setter
    @JsonProperty("送金状態")
    private String transferStatus = "";

    @Getter
    @Setter
    @JsonProperty("取引タイプ")
    private String tradeTypeName;

    public ReportTradesStatement setProperties(
            PosTrade posTrade,
            CurrencyPair currencyPair,
            User user,
            User targetUser,
            String marketMakerUserIdList[],
            Symbol symbol) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        this.setTradeDate(
                (new DateTime(posTrade.getCreatedAt()).plusHours(9))
                        .toString("yyyy/MM/dd HH:mm:ss"));

        String tradeTypeName =
                TradeType.INVEST.equals(symbol.getTradeType())
                        ? PosConstants.INVEST_TRADE_TYPE_NAME
                        : PosConstants.OPERATE_TRADE_TYPE_NAME;

        // 取引した顧客
        User transactionUser = targetUser;

        // 顧客ID
        String transactionUserId = "";
        // 利用者の氏名又は名称
        String personalUserName = "";
        if (symbol.getTradeType() == TradeType.INVEST) {
            transactionUserId = posTrade.getUserId().toString();
            if (user.getUserInfoId() != null) {
                personalUserName =
                        user.getUserInfo().getLastName() + " " + user.getUserInfo().getFirstName();
            }
        } else {
            // 取引所の場合は、相手方顧客登録アカウント氏名を表示。
            if (transactionUser.getUserInfo() != null) {
                personalUserName =
                        transactionUser.getUserInfo().getLastName()
                                + " "
                                + transactionUser.getUserInfo().getFirstName();
            }
            if (transactionUser.getAuthorities() != null) {
                transactionUserId = transactionUser.getAuthorities().get(0).getUserId().toString();
            }
        }

        // 利用者のユーザID
        this.setUserId(transactionUserId); // 顧客ID
        this.setUserName(personalUserName); // 取引した顧客の氏名

        this.setInsiderFlg("自己");

        ReportLabel.OrderSide tradeTypeOrderSide =
                ReportLabel.OrderSide.valueOfName(posTrade.getOrderSide().name());
        ReportLabel.OrderSide posTradeTypeOrderSide = null;

        // 1.「（販売所）売付け・買付け又は他の仮想通貨との交換の別」を追加して、顧客が出した「買い」「売り」を表示してください。
        if (tradeTypeOrderSide == ReportLabel.OrderSide.SELL) {
            posTradeTypeOrderSide = ReportLabel.OrderSide.BUY;
        } else {
            posTradeTypeOrderSide = ReportLabel.OrderSide.SELL;
        }
        // ・E列は不要
        // ・F列の情報を表示
        // ・F列の項目名を「売付け・買付け又は他の暗号資産との交換の別」に変更
        this.setPosTradeType(posTradeTypeOrderSide.getLabel());
        if (symbol.getTradeType() == TradeType.INVEST) {
            this.setCurrencyPair(currencyPair.getName());
        } else {
            this.setCurrencyPair(CurrencyPairName.getCurrencyPairNameById(symbol.getId()));
        }

        if (symbol.getTradeType() == TradeType.INVEST) {
            this.setTradeAmount(
                    toStrigForReportNotByCurrency(
                            currencyPair.getPosScaledAmount(posTrade.getAmount()), numberFormat));
        } else {
            this.setTradeAmount(
                    toStrigForReportNotByCurrency(
                            currencyPair.getScaledAmount(posTrade.getAmount()), numberFormat));
        }
        this.setTradePrice(
                toStrigForReportNotByCurrency(
                        currencyPair.getScaledPrice(posTrade.getPrice()), numberFormat));
        BigDecimal bigDecimal1 =
                currencyPair
                        .getScaledAmount(posTrade.getAmount())
                        .multiply(currencyPair.getScaledPrice(posTrade.getPrice()))
                        .setScale(0, RoundingMode.DOWN);
        BigDecimal bigDecimal2 =
                currencyPair
                        .getScaledAmount(posTrade.getAmount())
                        .multiply(currencyPair.getScaledPrice(posTrade.getPrice()))
                        .subtract(bigDecimal1);
        if (bigDecimal2.compareTo(new BigDecimal("0.1")) < 0) {
            this.setTradePricetotal(toStrigForReport(Currency.JPY, bigDecimal1, numberFormat));
        } else {
            this.setTradePricetotal(
                    toStrigForReport(Currency.JPY, bigDecimal1.add(BigDecimal.ONE), numberFormat));
        }

        this.setTradeName("");
        this.setFee(
                (posTrade.getFee().intValue() < 0
                                ? BigDecimal.valueOf(
                                        Math.abs(
                                                currencyPair
                                                        .getScaledPrice(posTrade.getFee())
                                                        .intValue()))
                                : BigDecimal.valueOf(
                                        currencyPair.getScaledPrice(posTrade.getFee()).intValue()))
                        .toPlainString());
        this.setTransferStatus("なし");
        this.setTradeTypeName(tradeTypeName);

        return this;
    }

    private static String toStrigForReport(
            Currency currency, BigDecimal value, NumberFormat numberFormat) {
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue =
                currency.getScaledAmount(value, RoundingMode.FLOOR)
                        .stripTrailingZeros()
                        .toPlainString();
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return numberFormat.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }

    private static String toStrigForReportNotByCurrency(
            BigDecimal value, NumberFormat numberFormat) {
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue = value.toPlainString();
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return numberFormat.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }
}
