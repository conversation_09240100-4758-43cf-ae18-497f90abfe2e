package point.admin.model.request;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AdminForm {

    public static boolean isIncludeRequiresChar(String password) {
        Pattern patternEng = Pattern.compile("[A-Za-z]");
        Pattern patternNum = Pattern.compile("[0-9]");
        Pattern patternSymbol = Pattern.compile("[!\"#$%&'*+-/=?@^_`{};:|~]");

        Matcher matcherEng = patternEng.matcher(password);
        Matcher matcherNum = patternNum.matcher(password);
        Matcher matcherSymbol = patternSymbol.matcher(password);

        if (matcherEng.find() && matcherNum.find() && matcherSymbol.find()) {
            // 半角英数字記号のすべてを含むもののみOK
            return true;
        } else {
            return false;
        }
    }
}
