package point.admin.model.request;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@NotNull
public class BulkTransferCsvForm {

    // 振替元ユーザーID
    private Long masterUserId;
    // 振替先ユーザーID
    private Long userId;
    // 通货
    private String currency;
    // 数量
    private BigDecimal amount;
    // コメント
    private String comment;
}
