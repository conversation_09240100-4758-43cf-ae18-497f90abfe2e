package point.admin.model.request;

import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

public class BankAccountPutForm {

    @Getter @Setter @NotNull private Long id;

    @Getter @Setter @NotNull private Long userId;

    @Getter @Setter @NotNull private Long bankId;

    @Getter @Setter @NotNull private String accountType;

    @Getter @Setter @NotNull private String accountNumber;

    @Getter @Setter @NotNull private String accountName;
}
