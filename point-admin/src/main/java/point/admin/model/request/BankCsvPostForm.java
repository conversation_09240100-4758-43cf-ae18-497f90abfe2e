package point.admin.model.request;

import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

public class BankCsvPostForm {

    @Getter @Setter @NotNull private String bankCode;

    @Getter @Setter @NotNull private String branchCode;

    @Getter @Setter @NotNull private String bankNameKana;

    @Getter @Setter @NotNull private String bankName;

    @Getter @Setter @NotNull private String branchNameKana;

    @Getter @Setter @NotNull private String branchName;

    @Getter @Setter @NotNull private String rankCode;
}
