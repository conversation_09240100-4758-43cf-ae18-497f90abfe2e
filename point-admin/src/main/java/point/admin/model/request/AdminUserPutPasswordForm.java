package point.admin.model.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

public class AdminUserPutPasswordForm extends AdminForm {

    @Getter @Setter @NotNull private String oldPassword;

    @Getter
    @Setter
    @NotNull
    @Length(min = 8, message = "NewPasswordが8文字未満です。")
    private String newPassword;

    @JsonIgnore
    @AssertTrue(message = "NewPasswordがOldPasswordと同じです。")
    public boolean isDifferent() {
        if (oldPassword.equals(newPassword)) {
            return false;
        }
        return true;
    }

    @JsonIgnore
    @AssertTrue(message = "NewPasswordは英数字と次の記号が必要です。!\"#$%&'*+-/=?@^_`{};:|~")
    public boolean isIncludeRequiresChar() {
        return isIncludeRequiresChar(newPassword);
    }
}
