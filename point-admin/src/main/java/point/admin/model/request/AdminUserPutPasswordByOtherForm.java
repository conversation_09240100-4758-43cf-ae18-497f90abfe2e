package point.admin.model.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

public class AdminUserPutPasswordByOtherForm extends AdminForm {

    @Getter @Setter @NotNull private Long adminUserId;

    @Getter
    @Setter
    @NotNull
    @Length(min = 8, message = "Passwordが8文字未満です。")
    private String password;

    @JsonIgnore
    @AssertTrue(message = "Passwordは英数字と次の記号が必要です。!\"#$%&'*+-/=?@^_`{};:|~")
    public boolean isIncludeRequiresChar() {
        return isIncludeRequiresChar(password);
    }
}
