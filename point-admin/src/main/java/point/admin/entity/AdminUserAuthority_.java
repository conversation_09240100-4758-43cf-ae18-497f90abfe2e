package point.admin.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.entity.AbstractEntity_;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(AdminUserAuthority.class)
public abstract class AdminUserAuthority_ extends AbstractEntity_ {

    public static volatile SingularAttribute<AdminUserAuthority, Long> adminUserId;
    public static volatile SingularAttribute<AdminUserAuthority, String> authority;

    public static final String ADMIN_USER_ID = "adminUserId";
    public static final String AUTHORITY = "authority";
}
