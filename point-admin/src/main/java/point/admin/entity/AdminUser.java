package point.admin.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.springframework.security.core.CredentialsContainer;
import org.springframework.security.core.userdetails.UserDetails;
import point.admin.model.request.AdminUserPutForm;
import point.common.entity.AbstractEntity;
import point.common.entity.AdminRole;

@Entity
@NoArgsConstructor
@Table(name = "admin_user")
@ToString(callSuper = true, doNotUseGetters = true)
public class AdminUser extends AbstractEntity implements UserDetails, CredentialsContainer {

    private static final long serialVersionUID = -8213917601269364082L;

    @Getter @Setter @Transient @JsonIgnore private List<String> menus;

    @Getter @Setter @Transient @JsonIgnore private Long roleId;

    @Getter
    @Setter
    @Column(name = "email", nullable = false)
    private String email;

    @JsonIgnore
    @Getter
    @Setter
    @Column(name = "password")
    private String password;

    @Getter
    @Setter
    @Column(name = "account_non_expired", nullable = false)
    private boolean accountNonExpired = true;

    @Getter
    @Setter
    @Column(name = "account_non_locked", nullable = false)
    private boolean accountNonLocked = true;

    @Getter
    @Setter
    @Column(name = "credentials_non_expired", nullable = false)
    private boolean credentialsNonExpired = true;

    @Getter
    @Setter
    @Column(name = "enabled", nullable = false)
    private boolean enabled = true;

    @Getter
    @Setter
    @Column(name = "password_force_change", nullable = false)
    private boolean passwordForceChange = true;

    @Getter
    @Setter
    @OneToMany(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinColumn(
            name = "admin_user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private List<AdminUserAuthority> authorities;

    @ManyToOne()
    @Getter
    @Setter
    @JoinTable(
            name = "admin_user_authority",
            joinColumns = {@JoinColumn(name = "admin_user_id", referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "authority", referencedColumnName = "id")})
    @NotFound(action = NotFoundAction.IGNORE)
    private AdminRole role;

    public AdminUser(String email, String password) {
        this.email = email;
        this.password = password;
    }

    @Override
    public void eraseCredentials() {
        password = null;
    }

    @Override
    public String getUsername() {
        return email;
    }

    public AdminUser setParams(AdminUserPutForm form) {
        email = form.getEmail();
        accountNonExpired = form.isAccountNonExpired();
        accountNonLocked = form.isAccountNonLocked();
        credentialsNonExpired = form.isCredentialsNonExpired();
        enabled = form.isEnabled();
        return this;
    }

    @Override
    public boolean equals(Object rhs) {
        if (!(rhs instanceof AdminUser)) {
            return false;
        }
        return this.email.equals(((AdminUser) rhs).getEmail());
    }

    @Override
    public int hashCode() {
        return this.email.hashCode();
    }
}
