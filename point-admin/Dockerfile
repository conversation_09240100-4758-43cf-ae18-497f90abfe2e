FROM gradle:7.0.0-jdk16 as builder
COPY --chown=gradle:gradle ./build.gradle /home/<USER>/
COPY --chown=gradle:gradle ./src /home/<USER>/src
RUN gradle build -Pbuilddir=build -x test -i

FROM openjdk:16
ARG ENVIRONMENT
ENV ENVIRONMENT $ENVIRONMENT
RUN mkdir -p /app/log
WORKDIR /app
# 安装字体管理工具
RUN apt-get update && apt-get install -y fontconfig
# 添加字体文件
COPY fonts /usr/share/fonts/
# 更新字体缓存
RUN fc-cache -f -v
COPY --from=builder /home/<USER>/build/libs/point-admin.jar /app
ENTRYPOINT [ "sh", "-c", "java -jar /app/point-admin.jar --spring.profiles.active=$ENVIRONMENT" ]
