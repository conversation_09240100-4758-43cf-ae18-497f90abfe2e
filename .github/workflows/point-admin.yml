name: On push for point-admin

on:
  push:
    branches: [dev, dev2, dev3, dev-admin, stg, stg-admin, prd, prd-admin]
    paths:
      - "point-common/**"
      - "point-admin/**"
      - ".github/workflows/point-admin.yml"
      - ".github/workflows/internal_cicd.yml"
      - "scripts/**"
      - "ponta/**"

jobs:
  call-workflow:
    uses: ./.github/workflows/internal_cicd.yml
    with:
      project: point-admin
      target: ${{ github.ref_name }}
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.CICD_AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.CICD_AWS_SECRET_ACCESS_KEY }}
