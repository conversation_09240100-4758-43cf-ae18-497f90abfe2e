package point.api.config;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import point.api.model.UserLog;
import point.common.component.RedisManager;
import point.common.service.LogService;
import point.common.service.UserService;
import point.common.util.JsonUtil;

@Component
@RequiredArgsConstructor
@Slf4j
public class HandlerInterceptorImpl implements HandlerInterceptor {

    private static final long REQUEST_SPAN = 60;

    private static final List<String> UNAUTHORIZED_URIS =
            Arrays.asList("/favicon.ico", "/healthcheck");

    public static final String START_DURATION = "startDuration";

    private static String getNonceKey(String apiKey) {
        return "nonce:" + apiKey;
    }

    private final PointApiConfig pointApiConfig;

    private final RedisManager redisManager;

    private final UserService userService;

    private final LogService logService;

    private boolean authenticate(HttpServletRequest request) throws Exception {
        for (String unauthorizedUris : UNAUTHORIZED_URIS) {
            if (request.getRequestURI().contains(unauthorizedUris)) {
                return true;
            }
        }

        return true;
    }

    @Override
    public boolean preHandle(
            HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        request.setAttribute(START_DURATION, new Date().getTime());
        response.setContentType(ContentType.APPLICATION_JSON.toString());

        logService.logRequestStart(request);

        return authenticate(request);
    }

    @Override
    public void postHandle(
            HttpServletRequest request,
            HttpServletResponse response,
            Object handler,
            ModelAndView modelAndView) {}

    @Override
    public void afterCompletion(
            HttpServletRequest request, HttpServletResponse response, Object handler, Exception e)
            throws IOException {
        final var requestUri = request.getRequestURI();
        // whiteListのPathはログを出力しない
        final var isWhiteListContain = UNAUTHORIZED_URIS.stream().anyMatch(requestUri::contains);
        if (requestUri.startsWith("/api/") && !isWhiteListContain) {
            log.info(JsonUtil.encode(new UserLog(request, response)));
        }
        log.info(
                "*** end "
                        + request.getRequestURL().toString()
                        + " "
                        + (new Date().getTime() - (long) request.getAttribute(START_DURATION))
                        + " ***");
    }
}
