#!/bin/bash

until mysqladmin ping -h mysql --silent; do
    echo 'waiting for mysqld to be connectable...'
    sleep 3
done

locations="filesystem:./sql"
if [ ! -z "${EXCHANGE_DOCKER_TESTDATA}" ] ; then
    echo "load test data"
    for datadir in ${EXCHANGE_DOCKER_TESTDATA} ; do
        echo "add test data directory: ${datadir}"
        locations="${locations},filesystem:${datadir}"
    done
fi

flyway info -locations=${locations}
flyway migrate -locations=${locations}
