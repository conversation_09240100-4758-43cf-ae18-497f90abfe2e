SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin_menu
-- ----------------------------
DROP TABLE IF EXISTS `admin_menu`;
CREATE TABLE `admin_menu`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `pid` bigint NULL DEFAULT NULL,
  `menu_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `menu_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `enable` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `menu_link` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `menu_group` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for admin_role
-- ----------------------------
DROP TABLE IF EXISTS `admin_role`;
CREATE TABLE `admin_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `enable` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for admin_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `admin_role_menu`;
CREATE TABLE `admin_role_menu`  (
  `role_id` bigint NOT NULL,
  `menu_id` bigint NOT NULL,
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for admin_user
-- ----------------------------
DROP TABLE IF EXISTS `admin_user`;
CREATE TABLE `admin_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'メールアドレス',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'パスワード',
  `account_non_expired` bit(1) NOT NULL COMMENT '期限切れでない',
  `account_non_locked` bit(1) NOT NULL COMMENT 'ロックされてない',
  `credentials_non_expired` bit(1) NOT NULL COMMENT '認証期限切れでない',
  `enabled` bit(1) NOT NULL COMMENT '有効',
  `account_non_locked_by_user` bit(1) NOT NULL DEFAULT b'1',
  `password_force_change` bit(1) NOT NULL DEFAULT b'1' COMMENT 'パスワード強制変更',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for admin_user_authority
-- ----------------------------
DROP TABLE IF EXISTS `admin_user_authority`;
CREATE TABLE `admin_user_authority`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_user_id` bigint NOT NULL COMMENT '管理者ユーザーID',
  `authority` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '権限',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for affiliate_info
-- ----------------------------
DROP TABLE IF EXISTS `affiliate_info`;
CREATE TABLE `affiliate_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `affiliate_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '経由先名称',
  `identify` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '経由先識別子',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for app_configuration
-- ----------------------------
DROP TABLE IF EXISTS `app_configuration`;
CREATE TABLE `app_configuration`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `key_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `value` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `version` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for asset
-- ----------------------------
DROP TABLE IF EXISTS `asset`;
CREATE TABLE `asset`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `currency` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通貨',
  `onhand_amount` decimal(34, 20) NOT NULL COMMENT '保有数量',
  `locked_amount` decimal(34, 20) NOT NULL COMMENT 'ロック数量',
  `eval_profit_loss_amt` decimal(34, 20) NULL DEFAULT NULL COMMENT '評価損益額',
  `eval_profit_loss_amt_rate` decimal(34, 20) NULL DEFAULT NULL COMMENT '評価損益額率',
  `avg_acq_unit_price` decimal(34, 20) NULL DEFAULT NULL COMMENT '平均取得単価',
  `asset_total` decimal(34, 20) NULL DEFAULT NULL COMMENT '総価格',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_user_id_currency`(`user_id` ASC, `currency` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for asset_summary
-- ----------------------------
DROP TABLE IF EXISTS `asset_summary`;
CREATE TABLE `asset_summary`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `target_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '対象日時',
  `currency` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通貨',
  `current_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '現保有数量',
  `jpy_conversion` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '円貨換算レート',
  `deposit_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '入金額',
  `deposit_amount_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '入金額(円貨換算)',
  `deposit_fee` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '入金手数料',
  `deposit_fee_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '入金手数料(円貨換算)',
  `withdrawal_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '出金額',
  `withdrawal_amount_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '出金額(円貨換算)',
  `withdrawal_fee` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '出金手数料',
  `withdrawal_fee_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '出金手数料(円貨換算)',
  `transaction_fee` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT 'トランザクション手数料',
  `transaction_fee_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT 'トランザクション手数料(円貨換算)',
  `pos_trade_buy_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '約定数量（販売所）（買い）',
  `pos_trade_buy_amount_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '約定数量（販売所）（買い）（円貨換算）',
  `pos_trade_sell_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '約定数量（販売所）（売り）',
  `pos_trade_sell_amount_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '約定数量（販売所）（売り）（円貨換算）',
  `pos_trade_fee` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '約定手数料（販売所）',
  `pos_trade_fee_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '約定手数料（販売所）（円貨換算）',
  `expiration_not_continue_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '期間満了金額',
  `expiration_not_continue_reward` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '報酬（期間満了）金額',
  `expiration_continue_reward` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '報酬（継続）金額',
  `cancel_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '解除金額',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `asset_summary_index_user_id`(`user_id` ASC, `target_at` ASC) USING BTREE,
  INDEX `asset_summary_target_at_index`(`target_at` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for balance_notify_config
-- ----------------------------
DROP TABLE IF EXISTS `balance_notify_config`;
CREATE TABLE `balance_notify_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `currency` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通貨',
  `default_balance` decimal(34, 20) NOT NULL COMMENT '初期残高',
  `limit_balance_percent` decimal(34, 20) NOT NULL COMMENT '残高閾値(%)',
  `target_pos` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '対象販売所',
  `mail_to` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '警告メール送付先',
  `enabled` bit(1) NOT NULL COMMENT '有効',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bank
-- ----------------------------
DROP TABLE IF EXISTS `bank`;
CREATE TABLE `bank`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `bank_code` smallint NOT NULL COMMENT '銀行コード',
  `branch_code` smallint NOT NULL COMMENT '支店コード',
  `bank_name_kana` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '銀行名(カナ)',
  `bank_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '銀行名',
  `branch_name_kana` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '支店名(カナ)',
  `branch_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '支店名',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `bank_index_bank_code_branch_code`(`bank_code` ASC, `branch_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bank_account
-- ----------------------------
DROP TABLE IF EXISTS `bank_account`;
CREATE TABLE `bank_account`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `bank_id` bigint NOT NULL COMMENT '銀行ID',
  `account_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '口座タイプ',
  `account_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '口座番号',
  `account_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '口座名義人',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '削除済み',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bank_account_deposit
-- ----------------------------
DROP TABLE IF EXISTS `bank_account_deposit`;
CREATE TABLE `bank_account_deposit`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `bank_account_id` bigint NOT NULL COMMENT '銀行口座ID',
  `amount` decimal(34, 20) NOT NULL COMMENT '数量',
  `fee` decimal(34, 20) NOT NULL COMMENT '手数料',
  `bank_account_deposit_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '銀行入金ステータス',
  `comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'コメント',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cover_order_config
-- ----------------------------
DROP TABLE IF EXISTS `cover_order_config`;
CREATE TABLE `cover_order_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `symbol_id` bigint NOT NULL COMMENT 'シンボルID',
  `exchange` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '贩卖所',
  `range_price_percent` decimal(34, 20) NOT NULL COMMENT '価格の乖離幅の%',
  `min_order_amount` decimal(34, 20) NOT NULL COMMENT '最小注文数量',
  `order_amount_percent` decimal(32, 20) NULL DEFAULT NULL,
  `enabled` bit(1) NOT NULL COMMENT '有効',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  `trade_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for currency_config
-- ----------------------------
DROP TABLE IF EXISTS `currency_config`;
CREATE TABLE `currency_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `trade_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '売買区分',
  `currency` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通貨',
  `deposit_fee` decimal(34, 20) NOT NULL COMMENT '入金手数料',
  `withdrawal_fee` decimal(34, 20) NOT NULL COMMENT '出金手数料',
  `transaction_fee` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT 'トランザクション手数料',
  `max_order_amount_per_day` decimal(24, 10) NOT NULL COMMENT '日次の最大注文数量',
  `min_deposit_amount` decimal(34, 20) NOT NULL COMMENT '最小入金額',
  `min_withdrawal_amount` decimal(34, 20) NOT NULL COMMENT '最小出金額',
  `depositable` bit(1) NOT NULL COMMENT '入金可能',
  `withdrawable` bit(1) NOT NULL COMMENT '出金可能',
  `enabled` bit(1) NOT NULL COMMENT '有効',
  `firstSetFlg` bit(1) NOT NULL DEFAULT b'0' COMMENT '新規フラグ',
  `visible` tinyint(1) NOT NULL DEFAULT 1 COMMENT '表示フラグ（1: 表示, 0: 非表示）',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for currency_pair_config
-- ----------------------------
DROP TABLE IF EXISTS `currency_pair_config`;
CREATE TABLE `currency_pair_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `trade_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '取引タイプ',
  `currency_pair` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通貨ペア',
  `min_order_amount` decimal(34, 20) NULL DEFAULT NULL COMMENT '最小注文数量',
  `max_order_amount` decimal(34, 20) NULL DEFAULT NULL COMMENT '最大注文数量',
  `max_active_order_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000,
  `limit_price_range_rate` decimal(34, 20) NULL DEFAULT NULL COMMENT '指値注文価格指定可能割合',
  `market_price_range_rate` decimal(34, 20) NULL DEFAULT NULL COMMENT '成行注文可能数量算出価格割合',
  `market_amount_range_rate` decimal(34, 20) NULL DEFAULT NULL COMMENT '成行注文可能数量算出割合',
  `maker_trade_fee_percent` decimal(34, 20) NOT NULL COMMENT 'メイカー約定手数料%',
  `taker_trade_fee_percent` decimal(34, 20) NOT NULL COMMENT 'テイカー約定手数料%',
  `tradable` bit(1) NOT NULL COMMENT '取引可能',
  `enabled` bit(1) NOT NULL COMMENT '有効',
  `circuit_break_updated_at` timestamp(3) NULL DEFAULT NULL COMMENT 'サーキットブレーカー発動日時',
  `circuit_break_percent` decimal(34, 20) NULL DEFAULT NULL COMMENT 'サーキットブレーカー発動%',
  `circuit_break_check_timespan` bigint NULL DEFAULT NULL COMMENT 'サーキットブレーカー確認期間',
  `circuit_break_stop_timespan` bigint NULL DEFAULT NULL COMMENT 'サーキットブレーカー停止間隔',
  `simple_market_spread_percent` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '販売所スプレッド%',
  `simple_market_fee_percent` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '販売所手数料%',
  `spike_percent` decimal(34, 20) NULL DEFAULT NULL COMMENT '実勢価格乖離検知%',
  `spike_minutes` int NULL DEFAULT NULL COMMENT '実勢価格乖離検知期間',
  `spike_count` int NULL DEFAULT NULL COMMENT '実勢価格乖離検知数カウント閾値',
  `wash_trading_check_span_hours` int NOT NULL DEFAULT 12 COMMENT '仮装・馴れ合い売買検知確認期間',
  `wash_trading_percent_threshold` decimal(34, 20) NOT NULL DEFAULT 0.25000000000000000000 COMMENT '仮装・馴れ合い売買検知閾値',
  `same_ip_check_span_hours` int NOT NULL DEFAULT 1 COMMENT '類似者検知確認期間',
  `same_ip_threshold` int NOT NULL DEFAULT 3 COMMENT '類似者検知閾値',
  `high_value_trader_check_span_hours` int NOT NULL DEFAULT 12 COMMENT '高額取引者検知確認期間',
  `high_value_trader_pos_order_limit_amount_threshold` decimal(34, 20) NOT NULL DEFAULT 1000000.00000000000000000000 COMMENT '高額取引者検知指値注文数量閾値',
  `high_value_trader_pos_trade_market_amount_threshold` decimal(34, 20) NOT NULL DEFAULT 1000000.00000000000000000000 COMMENT '高額取引者検知成行注文約定数量閾値',
  `high_value_trader_count_threshold` int NOT NULL DEFAULT 3 COMMENT '高額取引者検知カウント閾値',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  `pos_spread_percent` decimal(34, 20) NULL DEFAULT NULL COMMENT 'pos スプレッド',
  `pos_slippage_percent` decimal(34, 20) NULL DEFAULT NULL COMMENT 'pos スリッページ',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for exchange_summary
-- ----------------------------
DROP TABLE IF EXISTS `exchange_summary`;
CREATE TABLE `exchange_summary`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `target_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '対象日',
  `currency` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通貨',
  `current_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '保有数量',
  `jpy_conversion` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '円貨換算額',
  `deposit_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '入金額',
  `deposit_amount_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '入金額（円貨換算）',
  `deposit_fee` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '入金手数料',
  `deposit_fee_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '入金手数料（円貨換算）',
  `withdrawal_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '出金額',
  `withdrawal_amount_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '出金額（円貨換算）',
  `withdrawal_fee` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '出金手数料',
  `withdrawal_fee_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '出金手数料（円貨換算）',
  `transaction_fee` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT 'トランザクション手数料',
  `transaction_fee_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT 'トランザクション手数料（円貨換算）',
  `simple_market_pos_trade_buy_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '約定数量（販売所）（買い）',
  `simple_market_pos_trade_buy_amount_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '約定数量（販売所）（買い）（円貨換算）',
  `simple_market_pos_trade_sell_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '約定数量（販売所）（売り）',
  `simple_market_pos_trade_sell_amount_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '約定数量（販売所）（売り）（円貨換算）',
  `simple_market_pos_trade_fee` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '約定手数料（販売所）',
  `simple_market_pos_trade_fee_jpy` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '約定手数料（販売所）（円貨換算）',
  `expiration_not_continue_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '期間満了金額',
  `expiration_not_continue_reward` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '報酬（期間満了）金額',
  `expiration_continue_reward` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '報酬（継続）金額',
  `cancel_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '解除金額',
  `trade_type` varchar(255) NULL DEFAULT NULL COMMENT 'ユーザータイプ：INVEST| OPERATE',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fiat_deposit
-- ----------------------------
DROP TABLE IF EXISTS `fiat_deposit`;
CREATE TABLE `fiat_deposit`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `bank_account_id` bigint NULL DEFAULT NULL COMMENT '銀行口座ID',
  `onetime_bank_account_id` bigint NULL DEFAULT NULL COMMENT 'ワンタイム口座ID',
  `amount` decimal(34, 20) NOT NULL COMMENT '数量',
  `fee` decimal(34, 20) NOT NULL COMMENT '手数料',
  `fiat_deposit_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日本円入金ステータス',
  `fiat_deposit_sub_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日本円入金サブステータス',
  `comment` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  constraint fk_onetime_bank_account foreign key  (onetime_bank_account_id) references onetime_bank_account(id)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fiat_withdrawal
-- ----------------------------
DROP TABLE IF EXISTS `fiat_withdrawal`;
CREATE TABLE `fiat_withdrawal`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `bank_account_id` bigint NOT NULL COMMENT '銀行口座ID',
  `amount` decimal(34, 20) NOT NULL COMMENT '数量',
  `fee` decimal(34, 20) NOT NULL COMMENT '手数料',
  `fiat_withdrawal_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '日本円出金ステータス',
  `comment` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `apply_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `item_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '総合振込明細番号',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作成者',
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fiat_withdrawal_audit
-- ----------------------------
DROP TABLE IF EXISTS `fiat_withdrawal_audit`;
CREATE TABLE `fiat_withdrawal_audit`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `fiat_withdrawal_id` bigint NOT NULL COMMENT '日本円出金ID',
  `status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日本円出金ステータス',
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '作成者',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_fiat_withdrawal_audit_01`(`fiat_withdrawal_id` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for financial_assets_deviation_user
-- ----------------------------
DROP TABLE IF EXISTS `financial_assets_deviation_user`;
CREATE TABLE `financial_assets_deviation_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `trade_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'POS' COMMENT '取引タイプ',
  `over_trades` int NOT NULL COMMENT '約定数(閾値以上)',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gmo_deposit
-- ----------------------------
DROP TABLE IF EXISTS `gmo_deposit`;
CREATE TABLE `gmo_deposit`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `item_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '明細キー  口座ID毎に設定される明細キー  ',
  `fiat_deposit_id` bigint NULL DEFAULT NULL,
  `transaction_date` date NOT NULL COMMENT '取引日',
  `va_account_name_kana` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '口座名義カナ ',
  `remitter_name_kana` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '振込依頼人名カナ  汇款人姓名假名',
  `payment_bank_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '仕向金融機関名カナ',
  `payment_branch_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '仕向支店名カナ',
  `partner_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'サービス企業名，振込入金口座契約サービス企業名',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '摘要内容 該当データがない場合は項目自体を設定しません',
  `deposit_amount` int NOT NULL COMMENT '入金金額',
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `va_id` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '振込入金口座を識別するID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `gmo_deposit_pk`(`item_key` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for high_value_trader
-- ----------------------------
DROP TABLE IF EXISTS `high_value_trader`;
CREATE TABLE `high_value_trader`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `trade_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'POS' COMMENT '取引タイプ',
  `currency_pair` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '通貨ペア',
  `pos_order_limits` int NOT NULL COMMENT '指定額以上の指値注文数',
  `pos_trade_markets` int NOT NULL COMMENT '指定額以上の成行注文約定数',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for investment_purpose_deviation_user
-- ----------------------------
DROP TABLE IF EXISTS `investment_purpose_deviation_user`;
CREATE TABLE `investment_purpose_deviation_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `trade_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'POS' COMMENT '取引タイプ',
  `trades` int NOT NULL COMMENT '約定数',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mail_noreply
-- ----------------------------
DROP TABLE IF EXISTS `mail_noreply`;
CREATE TABLE `mail_noreply`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `mail_noreply_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '自動送信メールタイプ',
  `from_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '送信元アドレス',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'タイトル',
  `contents` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'コンテンツ',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for news
-- ----------------------------
DROP TABLE IF EXISTS `news`;
CREATE TABLE `news`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `news_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ニュースタイプ',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'タイトル',
  `contents` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'コンテンツ',
  `link` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'リンク',
  `date` date NOT NULL COMMENT '日付',
  `enabled` bit(1) NOT NULL COMMENT '有効',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for onetime_bank_account
-- ----------------------------
DROP TABLE IF EXISTS `onetime_bank_account`;
CREATE TABLE `onetime_bank_account`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT 'ユーザーID',
  `branch_code` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '支店コード vaBranchCode',
  `branch_name` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '支店名カナ  vaBranchNameKana',
  `account_number` char(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '口座番号 vaAccountNumber',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  `va_type_code` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '振込入金口座　種類コード   1=期限型  2=継続型',
  `va_type_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '振込入金口座　種類名  振込入金口座　種類コードの名称',
  `va_holder_name_kana` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '振込入金口座名義カナ',
  `va_id` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '振込入金口座を識別するID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `onetime_bank_account_index_account_number_vaid`(`account_number` ASC, `va_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for operate_aggregated_order
-- ----------------------------
DROP TABLE IF EXISTS `operate_aggregated_order`;
CREATE TABLE `operate_aggregated_order`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `symbol_id` bigint NOT NULL COMMENT 'シンボルID',
  `order_side` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '売買',
  `order_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '注文タイプ',
  `amount` decimal(34, 20) NOT NULL COMMENT '数量',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `operate_aggregated_order_index`(`symbol_id` ASC, `order_side` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for operate_cover_order
-- ----------------------------
DROP TABLE IF EXISTS `operate_cover_order`;
CREATE TABLE `operate_cover_order`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `symbol_id` bigint NOT NULL COMMENT 'シンボルID',
  `aggregated_order_id` bigint NOT NULL COMMENT '関連するpoint_order_aggregated表のid',
  `order_side` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '売買',
  `order_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '注文タイプ',
  `price` decimal(34, 20) NOT NULL COMMENT '価格',
  `usdt_price` decimal(34, 20) NULL DEFAULT NULL COMMENT 'USDT価格',
  `amount` decimal(34, 20) NOT NULL COMMENT '数量',
  `remaining_amount` decimal(34, 20) NOT NULL COMMENT '未約定数量',
  `fee` decimal(34, 20) NOT NULL COMMENT '手数料',
  `mm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'カバー先取引所',
  `mm_order_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'カバー先注文ID',
  `order_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '注文状態',
  `is_offset`           bit          default b'0'                 null COMMENT 'オフセット注文です。',
  `client_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'クライアントID',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for point_charge
-- ----------------------------
DROP TABLE IF EXISTS `point_charge`;
CREATE TABLE `point_charge`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'user.id',
  `point_user_id` bigint NOT NULL COMMENT 'point_user.id',
  `partner_id` bigint NOT NULL COMMENT 'point_partner.id',
  `amount` decimal(15, 2) NOT NULL COMMENT '数量',
  `charge_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `point_user_id`(`point_user_id` ASC) USING BTREE,
  INDEX `partner_id`(`partner_id` ASC) USING BTREE,
  INDEX `point_charge_index`(`user_id` ASC, `point_user_id` ASC, `partner_id` ASC, `charge_time` ASC) USING BTREE,
  CONSTRAINT `point_charge_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `point_charge_ibfk_2` FOREIGN KEY (`point_user_id`) REFERENCES `point_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `point_charge_ibfk_3` FOREIGN KEY (`partner_id`) REFERENCES `point_partner` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for point_partner
-- ----------------------------
DROP TABLE IF EXISTS `point_partner`;
CREATE TABLE `point_partner`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `partner_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '事業者番号',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '事業者契約名',
  `effective_date` timestamp NOT NULL COMMENT '利用開始日時',
  `expiry_date` timestamp NOT NULL COMMENT '利用終了日時',
  `status` enum('ACTIVE','INACTIVE', 'ABOLISH') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'ACTIVE' COMMENT 'ACTIVE/INACTIVE',
  `show_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '事業者表示名',
  `responsible_person` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '担当者',
  `phone_num` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '電話番号',
  `mail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'メールアドレス',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '住所',
  `scope` int NULL DEFAULT NULL COMMENT '1:運用 2:投資 3:運用／投資',
  `website_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '事業者ウェブサイトURL',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '備考',
  `created_at` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作成者',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `point_partner_index`(`name` ASC, `status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for point_partner_credential
-- ----------------------------
DROP TABLE IF EXISTS `point_partner_credential`;
CREATE TABLE `point_partner_credential`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `point_partner_id` bigint NOT NULL COMMENT 'point_partner.id',
  `api_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `secret_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `expired_at` timestamp(3) NULL DEFAULT NULL COMMENT '有効期限',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `point_partner_credential_index`(`point_partner_id` ASC) USING BTREE,
  CONSTRAINT `point_partner_credential_ibfk_1` FOREIGN KEY (`point_partner_id`) REFERENCES `point_partner` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for point_transfer
-- ----------------------------
DROP TABLE IF EXISTS `point_transfer`;
CREATE TABLE `point_transfer`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'user_identity.id',
  `partner_id` bigint NOT NULL COMMENT 'point_partner.id',
  `id_type` enum('Invest','Operate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'ユーザータイプ：Invest | Operate',
  `transfer_type` enum('IN','OUT') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '取引タイプ：IN（入金），OUT（出金）',
  `amount` decimal(34, 20) NOT NULL COMMENT '数量',
  `fee` decimal(34, 20)  NULL COMMENT '手数料',
  `status` enum('PROCESSING','COMPLETED','FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ステータス： PROCESSING（処理中），COMPLETED（完了），FAILED（失敗）',
  `upload_status` enum('PENDING','PROCESSING','COMPLETED','FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'アップロード状態',
  `request_time` timestamp NULL DEFAULT NULL COMMENT '請求時間',
  `transfer_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '取引時間',
  `upload_time` timestamp NULL DEFAULT NULL COMMENT 'アップロード時間',
  `trade_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '取引番号',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `partner_id`(`partner_id` ASC) USING BTREE,
  INDEX `point_transfer_index`(`user_id` ASC, `partner_id` ASC, `transfer_type` ASC, `created_at` ASC) USING BTREE,
  INDEX `trade_number_index`(`id_type` ASC, `transfer_type` ASC, `status` ASC, `trade_number` ASC) USING BTREE,
  UNIQUE INDEX `unique_trade_number`(`trade_number` ASC) USING BTREE,
  CONSTRAINT `point_transfer_ibfk_1` FOREIGN KEY (`partner_id`) REFERENCES `point_partner` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `point_transfer_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `user_identity` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for point_transfer_status_history
-- ----------------------------
DROP TABLE IF EXISTS `point_transfer_status_history`;
CREATE TABLE `point_transfer_status_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `transfer_id` bigint NOT NULL COMMENT 'point_transfer.id (ポイント転送ID)',
  `previous_status` enum('PROCESSING','COMPLETED','FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '変更前の状態',
  `current_status` enum('PROCESSING','COMPLETED','FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '変更後の状態',
  `changed_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '状態変更日時',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '備考',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `transfer_id`(`transfer_id` ASC) USING BTREE,
  CONSTRAINT `point_transfer_status_history_ibfk_1` FOREIGN KEY (`transfer_id`) REFERENCES `point_transfer` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;
-- ----------------------------
-- Table structure for point_user
-- ----------------------------
DROP TABLE IF EXISTS `point_user`;
CREATE TABLE `point_user`  (
  `id` bigint NOT NULL COMMENT 'ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT 'user.id',
  `partner_id` bigint NOT NULL COMMENT 'point_partner.id',
  `partner_member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '第三者システムのユーザー識別子（例：会員ID）',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  UNIQUE INDEX `point_user_uik`(`partner_member_id` ASC) USING BTREE,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `partner_id`(`partner_id` ASC) USING BTREE,
  INDEX `point_user_index`(`user_id` ASC, `partner_id` ASC, `partner_member_id` ASC) USING BTREE,
  CONSTRAINT `point_user_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `point_user_ibfk_2` FOREIGN KEY (`partner_id`) REFERENCES `point_partner` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `point_user_ibfk_3` FOREIGN KEY (`id`) REFERENCES `user_identity` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for point_user_partner_credential
-- ----------------------------
DROP TABLE IF EXISTS `point_user_partner_credential`;
CREATE TABLE `point_user_partner_credential`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'point_user.id',
  `access_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'アクセストークン',
  `refresh_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'リフレッシュトークン',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `point_user_partner_credential_index`(`user_id` ASC) USING BTREE,
  CONSTRAINT `point_user_partner_credential_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user_identity` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pos_candlestick
-- ----------------------------
DROP TABLE IF EXISTS `pos_candlestick`;
CREATE TABLE `pos_candlestick`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `symbol_id` bigint NOT NULL,
  `candlestick_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `target_at` timestamp NOT NULL,
  `open` decimal(34, 20) NULL DEFAULT NULL,
  `high` decimal(34, 20) NULL DEFAULT NULL,
  `low` decimal(34, 20) NULL DEFAULT NULL,
  `close` decimal(34, 20) NULL DEFAULT NULL,
  `volume` decimal(34, 20) NULL DEFAULT NULL,
  `fixed` bit(1) NOT NULL,
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pos_candlestick_symbol_id_index`(`symbol_id` ASC, `candlestick_type` ASC, `target_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pos_cover_order
-- ----------------------------
DROP TABLE IF EXISTS `pos_cover_order`;
CREATE TABLE `pos_cover_order`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `symbol_id` bigint NOT NULL COMMENT 'シンボルID',
  `order_id` bigint NOT NULL,
  `order_side` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '売買',
  `order_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '注文タイプ',
  `price` decimal(34, 20) NOT NULL COMMENT '価格',
  `average_price` decimal(34, 20) NOT NULL COMMENT '平均約定価格',
  `usdt_price` decimal(34, 20) NULL DEFAULT NULL COMMENT 'USDT価格',
  `amount` decimal(34, 20) NOT NULL COMMENT '数量',
  `remaining_amount` decimal(34, 20) NOT NULL COMMENT '未約定数量',
  `fee` decimal(34, 20) NOT NULL COMMENT '手数料',
  `strategy` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `mm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'カバー先取引所',
  `mm_order_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'カバー先注文ID',
  `order_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `average_price_manual` decimal(34, 20) NOT NULL COMMENT '平均約定価格_手動',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pos_cover_order_order_id_symbol_id_index`(`order_id` ASC, `symbol_id` ASC, `mm_order_id` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pos_market_maker_config
-- ----------------------------
DROP TABLE IF EXISTS `pos_market_maker_config`;
CREATE TABLE `pos_market_maker_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `symbol_id` bigint NOT NULL,
  `mm` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `enabled` bit(1) NULL DEFAULT NULL,
  `quantity` decimal(34, 20) NULL DEFAULT 0.00000000000000000000,
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pos_mm_config_symbol_id_index`(`symbol_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pos_order
-- ----------------------------
DROP TABLE IF EXISTS `pos_order`;
CREATE TABLE `pos_order`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `symbol_id` bigint NOT NULL COMMENT 'シンボルID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `id_type` enum('Invest','Operate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'ユーザータイプ：Invest | Operate',
  `order_side` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '売買',
  `order_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '注文タイプ',
  `order_channel` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'UNKNOWN' COMMENT '注文チャネル',
  `mm_price` decimal(34, 20) NOT NULL COMMENT 'MM価格',
  `price` decimal(34, 20) NOT NULL COMMENT 'MM price + spread + slippage',
  `amount` decimal(34, 20) NOT NULL COMMENT '数量',
  `covered` bit(1) NOT NULL COMMENT 'is covered',
  `remaining_amount` decimal(34, 20) NOT NULL COMMENT '未約定数量',
  `order_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '注文ステータス',
  `order_operator` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '発注者',
  `notes` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名前',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `symbol_id_user_id_order_status_order_side_order_type_index`(`symbol_id` ASC, `user_id` ASC, `order_status` ASC, `order_side` ASC, `order_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pos_trade
-- ----------------------------
DROP TABLE IF EXISTS `pos_trade`;
CREATE TABLE `pos_trade`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `symbol_id` bigint NOT NULL COMMENT 'シンボルID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `id_type` enum('Invest','Operate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'ユーザータイプ：Invest | Operate',
  `order_side` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '売買',
  `order_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '注文タイプ',
  `order_channel` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'UNKNOWN' COMMENT '注文チャネル',
  `price` decimal(34, 20) NOT NULL COMMENT '価格',
  `amount` decimal(34, 20) NOT NULL COMMENT '数量',
  `jpy_conversion` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '円貨換算レート',
  `trade_action` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '約定アクション',
  `order_id` bigint NOT NULL COMMENT '注文ID',
  `fee` decimal(34, 20) NOT NULL COMMENT '手数料',
  `asset_amount` decimal(34, 20) NOT NULL DEFAULT 0.00000000000000000000 COMMENT '総額',
  `eval_profit_loss_amt` decimal(34, 20) NULL DEFAULT NULL COMMENT '評価損益額',
  `eval_profit_loss_amt_rate` decimal(34, 20) NULL DEFAULT NULL COMMENT '評価損益額率',
  `avg_acq_unit_price` decimal(34, 20) NULL DEFAULT NULL COMMENT '平均取得単価',
  `income` decimal(34, 20) NULL DEFAULT NULL COMMENT '収益額',
  `experience_points` int NULL COMMENT '経験值(EXP)',
  `notes` varchar(128) DEFAULT NULL COMMENT '名前',
  `user_growth_stage_id` int default 0 NULL COMMENT '作物成長段階ID',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pos_trade_symbol_id_user_id_index`(`symbol_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for same_ip_user
-- ----------------------------
DROP TABLE IF EXISTS `same_ip_user`;
CREATE TABLE `same_ip_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'IPアドレス',
  `user_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ユーザーIDs',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for symbol
-- ----------------------------
DROP TABLE IF EXISTS `symbol`;
CREATE TABLE `symbol`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `trade_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '取引タイプ',
  `currency_pair` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通貨ペア',
  `visible` tinyint(1) NOT NULL DEFAULT 1 COMMENT '表示フラグ（1: 表示, 0: 非表示）',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `currency` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '通貨',
  `currency_pair` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '通貨ペア',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '設定名',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '設定値',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` bigint NOT NULL COMMENT 'ID',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'メールアドレス',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'パスワード',
  `anti_phishing_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'アンチフィッシングコード',
  `account_non_expired` bit(1) NOT NULL COMMENT '期限切れでない',
  `account_non_locked` bit(1) NOT NULL COMMENT 'ロックされてない',
  `credentials_non_expired` bit(1) NOT NULL COMMENT '認証期限切れでない',
  `enabled` bit(1) NOT NULL COMMENT '有効',
  `user_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'ACTIVE' COMMENT 'ユーザーステータス',
  `kyc_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'NONE' COMMENT 'KYCステータス',
  `user_kyc_id` bigint NULL DEFAULT NULL COMMENT 'ユーザーKYCID',
  `level` int NOT NULL DEFAULT 1 COMMENT 'レベル',
  `user_info_id` bigint NULL DEFAULT NULL COMMENT '個人ユーザー情報ID',
  `old_user_id` int NULL DEFAULT NULL COMMENT '旧ユーザーID',
  `inside_account_flg` bit(1) NOT NULL DEFAULT b'0' COMMENT '自社口座フラグ',
  `trade_uncapped` bit(1) NOT NULL DEFAULT b'0' COMMENT '取引制限解除',
  `insider` bit(1) NOT NULL DEFAULT b'0' COMMENT '内部者',
  `risker` bit(1) NOT NULL DEFAULT b'0' COMMENT '高リスカー',
  `session_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'セッションID',
  `affiliate_info_id` bigint NULL DEFAULT NULL COMMENT 'アフィリエイト経由ID',
  `uuid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '識別子',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  CONSTRAINT `user_ibfk_1` FOREIGN KEY (`id`) REFERENCES `user_identity` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_agreement
-- ----------------------------
DROP TABLE IF EXISTS `user_agreement`;
CREATE TABLE `user_agreement`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `user_agreement_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '同意書類タイプ',
  `version` int NOT NULL COMMENT 'バージョン',
  `enabled` bit(1) NOT NULL COMMENT '有効',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_agreement_user_id_user_agreement_type`(`user_id` ASC, `user_agreement_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_agreement_file
-- ----------------------------
DROP TABLE IF EXISTS `user_agreement_file`;
CREATE TABLE `user_agreement_file`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_agreement_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '同意書類タイプ',
  `version` int NOT NULL COMMENT 'バージョン',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_antisocial_check
-- ----------------------------
DROP TABLE IF EXISTS `user_antisocial_check`;
CREATE TABLE `user_antisocial_check`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '管理番号',
  `user_id` bigint NOT NULL COMMENT '口座番号',
  `kyc_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '反社データ有無',
  `reference_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'reference',
  `check_group_key` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '反社チェックグルーピングキー',
  `created_at` datetime NOT NULL COMMENT '登録日時',
  `updated_at` datetime NOT NULL COMMENT '更新日時',
  `user_info_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `antisocial_customer_id_fk`(`user_id` ASC) USING BTREE,
  CONSTRAINT `antisocial_customer_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_authority
-- ----------------------------
DROP TABLE IF EXISTS `user_authority`;
CREATE TABLE `user_authority`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `authority` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '権限',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_ekyc
-- ----------------------------
DROP TABLE IF EXISTS `user_ekyc`;
CREATE TABLE `user_ekyc`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `doc_type` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `reference_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `applicant_id` bigint NULL DEFAULT NULL,
  `user_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_userid_referenceid_applicantid`(`reference_id` ASC, `applicant_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_ekyc_batch_request
-- ----------------------------
DROP TABLE IF EXISTS `user_ekyc_batch_request`;
CREATE TABLE `user_ekyc_batch_request`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `headers` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `request_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `response_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `user_id` bigint NULL DEFAULT NULL,
  `applicant_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_ekyc_request_history
-- ----------------------------
DROP TABLE IF EXISTS `user_ekyc_request_history`;
CREATE TABLE `user_ekyc_request_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `request_time` datetime NULL DEFAULT NULL,
  `user_id` bigint NULL DEFAULT NULL,
  `response_header_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `response_entity_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `request_header_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `request_entity_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `success` tinyint(1) NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_ekyc_request_history_user_fk`(`user_id` ASC) USING BTREE,
  CONSTRAINT `user_ekyc_request_history_user_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_ekyc_status_change_history
-- ----------------------------
DROP TABLE IF EXISTS `user_ekyc_status_change_history`;
CREATE TABLE `user_ekyc_status_change_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `applicant_id` bigint NULL DEFAULT NULL,
  `before_status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `after_status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_ekyc_status_change_history_user_fk`(`user_id` ASC) USING BTREE,
  CONSTRAINT `user_ekyc_status_change_history_user_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_identity
-- ----------------------------
DROP TABLE IF EXISTS `user_identity`;
CREATE TABLE `user_identity`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `id_type` enum('Invest','Operate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'ユーザータイプ：Invest | Operate',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_identity_id_type`(`id_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_info
-- ----------------------------
DROP TABLE IF EXISTS `user_info`;
CREATE TABLE `user_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名前',
  `last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名字',
  `first_kana` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名前(カナ)',
  `last_kana` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名字(カナ)',
  `nationality` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '国籍',
  `zip_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '郵便番号',
  `prefecture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '都道府県',
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '市区町村',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '町名番地',
  `building` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '建物名',
  `birthday` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '生年月日',
  `gender` int NULL DEFAULT NULL COMMENT '性別',
  `phone_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '電話番号',
  `occupation` int NOT NULL COMMENT '職業',
  `industry` int NULL DEFAULT NULL COMMENT '業種',
  `work_place` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '勤務先',
  `position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部署・役職',
  `pricefrom` int NULL DEFAULT NULL COMMENT '収入源',
  `income` int NOT NULL COMMENT '年収',
  `financial_assets` int NOT NULL COMMENT '金融資産',
  `purpose` int NOT NULL COMMENT '主なご利用目的',
  `investment_purposes` int NOT NULL COMMENT '投資目的',
  `crypto_experience` int NOT NULL COMMENT '投資経験(暗号資産)',
  `fx_experience` int NOT NULL COMMENT '投資経験(FX)',
  `stocks_experience` int NOT NULL COMMENT '投資経験(株式投資)',
  `fund_experience` int NOT NULL COMMENT '投資経験(投資信託)',
  `application_history` int NOT NULL COMMENT '申込経緯',
  `application_history_other` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申込経緯(その他)',
  `foreign_peps` bit(1) NOT NULL COMMENT '外国peps',
  `country` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '国',
  `antisocial_status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'refinitiv',
  `residence_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '在留資格',
  `residence_card_expired_at` datetime NULL DEFAULT NULL COMMENT '在留カード期限日',
  `insider` bit(1) NOT NULL DEFAULT b'0' COMMENT '内部者',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_kyc
-- ----------------------------
DROP TABLE IF EXISTS `user_kyc`;
CREATE TABLE `user_kyc`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `kyc_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'NONE' COMMENT 'KYCタイプ',
  `kyc_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'NONE' COMMENT 'KYCステータス',
  `kyc_mail_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'NONE' COMMENT '住所確認ハガキステータス',
  `mail_send_at` timestamp(3) NULL DEFAULT NULL COMMENT '住所確認ハガキ送付日時',
  `judging_comment` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `aml_cft_comment` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `antisocial_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'NONE' COMMENT '反社チェックステータス',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  `operator` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `user_info_id` bigint NULL DEFAULT NULL COMMENT '個人ユーザー情報ID',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'メールアドレス(変更前)',
  `change_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '変更種類(01:メール変更 02:電話変更 03:BO変更 以外:変更なし)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_kyc_sub
-- ----------------------------
DROP TABLE IF EXISTS `user_kyc_sub`;
CREATE TABLE `user_kyc_sub`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `kyc_id` bigint NOT NULL COMMENT 'KYC ID',
  `kyc_sub_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'KYC Sub Status',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_login_info
-- ----------------------------
DROP TABLE IF EXISTS `user_login_info`;
CREATE TABLE `user_login_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IPアドレス',
  `in_japan` bit(1) NOT NULL COMMENT '日本国内',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_mail_notices_off
-- ----------------------------
DROP TABLE IF EXISTS `user_mail_notices_off`;
CREATE TABLE `user_mail_notices_off`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NULL DEFAULT NULL,
  `notices_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `notices_enabled` tinyint NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_mfa
-- ----------------------------
DROP TABLE IF EXISTS `user_mfa`;
CREATE TABLE `user_mfa`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `mfa_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'MFAタイプ',
  `secret_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'GA用シークレットキー',
  `authenticated` bit(1) NOT NULL DEFAULT b'0' COMMENT '認証済み',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_news
-- ----------------------------
DROP TABLE IF EXISTS `user_news`;
CREATE TABLE `user_news`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `read_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '既読ニュースID',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_note
-- ----------------------------
DROP TABLE IF EXISTS `user_note`;
CREATE TABLE `user_note`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL,
  `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'メモ内容',
  `note_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'メモのコンテンツ タイプ',
  `current_kyc_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `operator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '担当者ID',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '登録日時',
  `updated_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `note_user_id_fk`(`user_id` ASC) USING BTREE,
  CONSTRAINT `note_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_permission
-- ----------------------------
DROP TABLE IF EXISTS `user_permission`;
CREATE TABLE `user_permission`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `resource` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'リソース名（例：メニュー、機能）',
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作（例：read, write, execute）',
  `id_type` enum('Invest','Operate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_permission_index`(`id_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_status_history
-- ----------------------------
DROP TABLE IF EXISTS `user_status_history`;
CREATE TABLE `user_status_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NULL DEFAULT NULL,
  `user_info_id` bigint NULL DEFAULT NULL,
  `before_status` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `after_status` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `staff_id` bigint NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_status_history_user_id_fk`(`user_id` ASC) USING BTREE,
  INDEX `user_status_history_user_info_id_fk`(`user_info_id` ASC) USING BTREE,
  CONSTRAINT `user_status_history_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `user_status_history_user_info_id_fk` FOREIGN KEY (`user_info_id`) REFERENCES `user_info` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_summary
-- ----------------------------
DROP TABLE IF EXISTS `user_summary`;
CREATE TABLE `user_summary`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `target_at` timestamp(3) NULL DEFAULT NULL COMMENT '対象日時',
  `users` int NOT NULL COMMENT '全ユーザー数',
  `user_kyc_account_opening_done` int NOT NULL COMMENT '口座開設完了',
  `user_kyc_done` int NOT NULL COMMENT '承認（変更完了）',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wash_trader
-- ----------------------------
DROP TABLE IF EXISTS `wash_trader`;
CREATE TABLE `wash_trader`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `trades` int NOT NULL COMMENT '約定数',
  `targetUserIds` int NOT NULL COMMENT '約定相手数',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for worker_master
-- ----------------------------
DROP TABLE IF EXISTS `worker_master`;
CREATE TABLE `worker_master`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `environment` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '環境',
  `bean_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'Bean名',
  `trade_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '取引タイプ',
  `currency_pair` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '通貨ペア',
  `interval_millis` bigint NULL DEFAULT NULL COMMENT '起動間隔',
  `enabled` bit(1) NOT NULL COMMENT '有効',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for yearly_report_create_info
-- ----------------------------
DROP TABLE IF EXISTS `yearly_report_create_info`;
CREATE TABLE `yearly_report_create_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `year` int NOT NULL COMMENT '年度',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '作成状態',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '失敗原因',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作成者',
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  `download_flg` bit(1) NOT NULL COMMENT 'ダウンロードされたか',
  `latest_download_time` timestamp(3) NULL DEFAULT NULL COMMENT '直近ダウンロードTime',
  `s3_file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'S３ファイル名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zip_code
-- ----------------------------
DROP TABLE IF EXISTS `zip_code`;
CREATE TABLE `zip_code`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '全国地方公共団体コード',
  `old_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '（旧）郵便番号（5桁）',
  `zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '郵便番号（7桁)',
  `prefectures` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '都道府県名　…………　漢字（コード順に掲載）',
  `prefectures_kana` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '都道府県名　…………　半角カタカナ（コード順に掲載）',
  `city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '市区町村名　…………　漢字（コード順に掲載）',
  `city_kana` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '市区町村名　…………　半角カタカナ（コード順に掲載）',
  `town_area` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '町域名　………………　漢字（五十音順に掲載）　',
  `town_area_kana` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '町域名　………………　半角カタカナ（五十音順に掲載）',
  `has_another_area` tinyint(1) NOT NULL COMMENT '一町域が二以上の郵便番号で表される場合の表示　',
  `has_sub_code` tinyint(1) NOT NULL COMMENT '小字毎に番地が起番されている町域の表示　',
  `has_area_number` tinyint(1) NOT NULL COMMENT '丁目を有する町域の場合の表示',
  `has_same_zip_code_area` tinyint(1) NOT NULL COMMENT '一つの郵便番号で二以上の町域を表す場合の表示',
  `update_type` int NULL DEFAULT NULL COMMENT '更新の表示',
  `update_reason` int NULL DEFAULT NULL COMMENT '変更理由',
  `version` int NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `zip_code_zip_code_index`(`zip_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bc_currency_config
-- ----------------------------
DROP TABLE IF EXISTS `bc_currency_config`;
CREATE TABLE `bc_currency_config`  (
   `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
   `currency_data` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通貨',
   `active` enum('ACTIVE','ABOLISHED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'ACTIVE' COMMENT 'ステータス：運用中、廃止',
   `currency_type` enum('Balance','Active') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Balance' COMMENT 'コースタイプ',
   `created_at` timestamp NOT NULL COMMENT '作成日時',
   `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
   `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
   `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '動的な通貨とその重みを記録します' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bc_order_currency_split
-- ----------------------------
DROP TABLE IF EXISTS `bc_order_currency_split`;
CREATE TABLE `bc_order_currency_split`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `symbol_id` bigint NOT NULL COMMENT '通貨コード',
    `amount` decimal(34, 20) NOT NULL COMMENT '数量',
    `order_id` bigint NOT NULL COMMENT '注文ID',
    `trade_id` bigint NOT NULL COMMENT '約定ID',
    `created_at` timestamp NOT NULL COMMENT '作成日時',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '記録注文内の他通貨の分割情報' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for choice_activity
-- ----------------------------
DROP TABLE IF EXISTS `choice_activity`;
CREATE TABLE `choice_activity`  (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'アクティビティID',
    `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'アクティビティ名称',
    `activity_type` enum('BTC_PRICE','OTHER_ASSET') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'アクティビティタイプ',
    `vote_start_time` timestamp NOT NULL COMMENT '投票開始時間',
    `vote_end_time` timestamp NOT NULL COMMENT '投票終了時間',
    `lock_start_time` timestamp NOT NULL COMMENT 'ロック開始時間',
    `lock_end_time` timestamp NOT NULL COMMENT 'ロック終了時間',
    `base_price_voting_day` decimal(34, 20) NULL DEFAULT NULL COMMENT '基準価格(投票日)',
    `base_price_election_day` decimal(34, 20) NULL DEFAULT NULL COMMENT '基準価格(当選日)',
    `reward_pool` bigint NULL DEFAULT NULL COMMENT '賞金総額(pt)',
    `symbol` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'JPY' COMMENT '通貨単位',
    `total_vote_power_up` bigint NULL DEFAULT 0 COMMENT 'UP方向の総投票パワー',
    `total_vote_power_down` bigint NULL DEFAULT 0 COMMENT 'DOWN方向の総投票パワー',
    `vote_power_limit` bigint NOT NULL DEFAULT 10000 COMMENT 'ユーザー投票パワー上限',
    `status` enum('INACTIVE','VOTING','CALCULATING_POWER','DISTRIBUTING_REWARDS','COMPLETED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'アクティビティステータス',
    `total_vote_users_up` bigint NULL DEFAULT 0 COMMENT 'UP方向の投票総人数',
    `total_vote_users_down` bigint NULL DEFAULT 0 COMMENT 'DOWN方向の投票総人数',
    `vote_result` enum('UP','DOWN','DRAW') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '投票結果',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '作成時間',
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'アクティビティ表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for choice_activity_template
-- ----------------------------
DROP TABLE IF EXISTS `choice_activity_template`;
CREATE TABLE `choice_activity_template`  (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
     `activity_type` enum('BTC_PRICE','OTHER_ASSET') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'アクティビティタイプ',
     `vote_start_time` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '投票開始時間',
     `vote_end_time` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '投票終了時間',
     `start_time` timestamp NOT NULL COMMENT '開始時間',
     `end_time` timestamp NOT NULL COMMENT '終了時間',
     `vote_power_limit` bigint NOT NULL DEFAULT 1 COMMENT 'ユーザー投票パワー上限',
     `status` enum('ACTIVE','ABOLISH') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'ACTIVE' COMMENT 'ステータス',
     `auto_cycle` enum('DAILY','MONTHLY') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'DAILY' COMMENT '自動生成サイクル',
     `power_total` bigint NOT NULL DEFAULT 10000 COMMENT '賞金総額',
     `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '作成時間',
     `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作成者',
     `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
     `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'アクティビティテンプレート表' ROW_FORMAT = Dynamic;



-- ----------------------------
-- Table structure for choice_power
-- ----------------------------
DROP TABLE IF EXISTS `choice_power`;
CREATE TABLE `choice_power`  (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
     `amount` bigint NOT NULL COMMENT '数量',
     `choice_p_amount` decimal(34, 5) NULL DEFAULT NULL COMMENT 'choice_p',
     `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成時間',
     `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新時間',
     PRIMARY KEY (`id`) USING BTREE,
     INDEX `idx_amount`(`amount` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'ユーザーパワー' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for choice_power_transfer
-- ----------------------------
DROP TABLE IF EXISTS `choice_power_transfer`;
CREATE TABLE `choice_power_transfer`  (
      `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
      `user_id` bigint NOT NULL COMMENT 'ユーザーID',
      `amount` bigint NOT NULL COMMENT '数量',
      `transfer_type` enum('IN','OUT') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '取引タイプ：IN（入金），OUT（出金）',
      `choice_activity_rule_id` bigint NOT NULL COMMENT '取引理由(choice_activity_rule.id)',
      `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '取引の詳細説明',
      `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成時間',
      `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新時間',
      PRIMARY KEY (`id`) USING BTREE,
      INDEX `choice_power_transfer_ibfk_1`(`user_id` ASC) USING BTREE,
      CONSTRAINT `choice_power_transfer_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user_identity` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'パワー取引記録表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for choice_power_user_rel
-- ----------------------------
DROP TABLE IF EXISTS `choice_power_user_rel`;
CREATE TABLE `choice_power_user_rel`  (
      `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
      `choice_power_id` bigint NOT NULL COMMENT 'choice_powerのID',
      `user_id` bigint NOT NULL COMMENT 'ユーザーID',
      `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成時間',
      `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新時間',
      PRIMARY KEY (`id`) USING BTREE,
      UNIQUE INDEX `choice_power_id`(`choice_power_id` ASC, `user_id` ASC) USING BTREE,
      INDEX `choice_power_user_rel_ibfk_2`(`user_id` ASC) USING BTREE,
      INDEX `idx_choice_power_id`(choice_power_id ASC) USING BTREE,
      CONSTRAINT `choice_power_user_rel_ibfk_1` FOREIGN KEY (`choice_power_id`) REFERENCES `choice_power` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
      CONSTRAINT `choice_power_user_rel_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `user_identity` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'ユーザーとパワー関係表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for choice_reward
-- ----------------------------
DROP TABLE IF EXISTS `choice_reward`;
CREATE TABLE `choice_reward`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '報酬ID',
  `user_id` bigint NOT NULL COMMENT 'ユーザーID',
  `activity_id` bigint NULL DEFAULT NULL COMMENT 'アクティビティID',
  `amount` decimal(15, 3) NOT NULL COMMENT '報酬額',
  `remaining_amount` decimal(15, 3) COMMENT '引出し後の残り金額',
  `reward_type` enum('CHOICE_P') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'CHOICE_P' COMMENT '報酬タイプ(CHOICE_P)',
  `reward_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '報酬時間',
  `expiry_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '期限切れ日',
  `withdraw_status` enum('FULLY_WITHDRAWN','PARTIALLY_WITHDRAWN','FULLY_EXPIRED','PARTIALLY_EXPIRED','PENDING') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '引き出しステータス(WITHDRAWN,PENDING)',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成時間',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `activity_id`(`activity_id` ASC) USING BTREE,
  CONSTRAINT `choice_reward_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user_identity` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `choice_reward_ibfk_2` FOREIGN KEY (`activity_id`) REFERENCES `choice_activity` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '報酬表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for choice_reward_withdrawal
-- ----------------------------
DROP TABLE IF EXISTS `choice_p_withdrawal`;
CREATE TABLE `choice_p_withdrawal`  (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '報酬ID',
     `user_id` bigint NOT NULL COMMENT '現在ログイン中のユーザー ID',
     `target_user_id` bigint NOT NULL COMMENT '引き出し元口座のユーザー ID',
     `amount` decimal(15, 2) NOT NULL COMMENT '引き出し数量',
     `withdraw_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '引き出し時間',
     `withdraw_type` enum('POINT','JPY') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '引き出しタイプ(POINT,JPY)',
     `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '取引の詳細説明',
     `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成時間',
     `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新時間',
     PRIMARY KEY (`id`) USING BTREE,
     INDEX `user_id`(`user_id` ASC) USING BTREE,
     CONSTRAINT `choice_reward_withdrawal_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user_identity` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
     CONSTRAINT `choice_reward_withdrawal_ibfk_2` FOREIGN KEY (`target_user_id`) REFERENCES `user_identity` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '報酬引き出し表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for choice_vote
-- ----------------------------
DROP TABLE IF EXISTS `choice_vote`;
CREATE TABLE `choice_vote`  (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '投票ID',
    `user_id` bigint NOT NULL COMMENT 'ユーザーID',
    `activity_id` bigint NOT NULL COMMENT 'アクティビティID',
    `vote_result` enum('WIN','LOSE','PENDING') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'PENDING' COMMENT '投票結果(WIN,LOSE,PENDING)',
    `vote_direction` enum('UP','DOWN') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '投票方向（UP/DOWN）',
    `vote_power` bigint NOT NULL COMMENT '消費したパワー',
    `vote_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '投票時間',
    `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '詳細説明',
    `withdrawal_allowed` int NOT NULL DEFAULT 0 COMMENT '引き出し有無（0: 無, 1: 有）',
    `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成時間',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新時間',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `user_id`(`user_id` ASC) USING BTREE,
    INDEX `choice_vote_activity_id_vote_direction_index`(`activity_id` ASC, `vote_direction` ASC) USING BTREE,
    INDEX `activity_id`(`activity_id` ASC) USING BTREE,
    CONSTRAINT `choice_vote_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user_identity` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '投票表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for point_user_summary
-- ----------------------------
DROP TABLE IF EXISTS `point_user_summary`;
CREATE TABLE `point_user_summary`  (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `target_at` timestamp(3) NULL DEFAULT NULL COMMENT '対象日時',
    `users` int NOT NULL COMMENT '全ユーザー数',
    `point_user_partner_active` int NULL DEFAULT NULL,
    `point_user_partner_date` int NULL DEFAULT NULL,
    `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
    `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crop_growth_stage
-- ----------------------------
DROP TABLE IF EXISTS `crop_growth_stage`;
CREATE TABLE `crop_growth_stage`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `trade_type` VARCHAR(128) NOT NULL COMMENT '取引タイプ',
    `growth_stage_id` int NOT NULL COMMENT 'GROWTH_STAGE_ID',
    `growth_stage` VARCHAR(255) NULL COMMENT '作物成長段階',
    `evaluation_from` bigint NOT NULL COMMENT '畑ごとの評価値From',
    `evaluation_to` bigint NOT NULL COMMENT '畑ごとの評価値To',
    `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
    `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
    `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '作物成長段階と評価値のテーブル' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for choice_activity_rule
-- ----------------------------
DROP TABLE IF EXISTS `choice_activity_rule`;
CREATE TABLE `choice_activity_rule` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `activity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'キャンペーン名称',
    `activity_function` enum('LOGIN_OPERATION_COURSE','PASS_QUIZ','LOGIN_INVESTMENT_COURSE','PASS_INVESTMENT_QUIZ','OPEN_INVESTMENT_ACCOUNT','FARM','BUY_SELL_TRADE','ELECTION_RESULTS_OF_VOTE','CROP_HARVESTED_SHARED') NOT NULL COMMENT 'アクティビティタイプ',
    `ordered` int NOT NULL DEFAULT '1',
    `id_type` enum('Invest','Operate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '種別：Invest | Operate',
    `obtain_frequency` enum('DAILY','ONCE','MONTHLY','EACH_DAY','EACH_TIME') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '獲得頻度',
    `get_Type` enum('FIXED','PROPORTIONAL') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '獲得タイプ',
    `power_amount` bigint DEFAULT NULL COMMENT '獲得パワー数',
    `power_amount_rate` decimal(10,2) DEFAULT NULL COMMENT '獲得パワー数(比例)',
    `effective_date` timestamp NOT NULL COMMENT '開始日',
    `expiry_date` timestamp NOT NULL COMMENT '終了日',
    `status` enum('ACTIVE','ABOLISH') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ステータス',
    `created_at` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
    `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '作成者',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
    `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for crop_growth_stage_history
-- ----------------------------
DROP TABLE IF EXISTS `crop_growth_stage_history`;
CREATE TABLE `crop_growth_stage_history` (
     `id` INT NOT NULL AUTO_INCREMENT COMMENT 'ID',
     `trade_type` VARCHAR(128) NOT NULL COMMENT '取引タイプ',
     `befor_growth_stage` VARCHAR ( 255 ) NULL COMMENT '変更前作物成長段階',
     `befor_evaluation` VARCHAR ( 255 ) NULL COMMENT '変更前畑ごとの評価値',
     `after_growth_stage` VARCHAR ( 255 ) NULL COMMENT '変更後作物成長段階',
     `after_evaluation` VARCHAR ( 255 ) NULL COMMENT '変更後畑ごとの評価値',
     `created_at` TIMESTAMP ( 3 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 3 ) COMMENT '作成日時',
     `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作成者',
     `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
     PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = InnoDB CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '作物成長段階と評価値の履歴表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for choice_power_sync
-- ----------------------------
DROP TABLE IF EXISTS `choice_power_sync`;
CREATE TABLE `choice_power_sync`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT 'user.id',
  `choice_activity_rule_id` bigint NOT NULL COMMENT '機能アクション(choice_activity_rule.id)',
  `activity_function` enum('LOGIN_OPERATION_COURSE','PASS_QUIZ','LOGIN_INVESTMENT_COURSE','OPEN_INVESTMENT_ACCOUNT','FARM','BUY_SELL_TRADE','ELECTION_RESULTS_OF_VOTE','CROP_HARVESTED_SHARED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'アクティビティタイプ',
  `obtain_frequency` enum('DAILY','ONCE','MONTHLY','EACH_DAY','EACH_TIME') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '獲得頻度',
  `get_Type` enum('FIXED','PROPORTIONAL') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '獲得タイプ',
  `power_amount` bigint NOT NULL COMMENT '獲得パワー数',
  `synchronize_flag` int NULL DEFAULT NULL COMMENT '同期フラグ(0非同期,1同期済み)',
  `created_at` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作成者',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id_index` (`user_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for monster_growth_rule
-- ----------------------------
DROP TABLE IF EXISTS `monster_growth_rule`;
CREATE TABLE monster_growth_rule (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT 'Rank',
    `total_profit_loss_from` int COMMENT '売却時の合計損益From',
    `total_profit_loss_to` int COMMENT '売却時の合計損益To',
    `conversion_rate` decimal(10, 2) COMMENT '变换割合',
    `experience_gain` varchar(255) COMMENT '獲得できる経験值(EXP)',
    `created_at` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
    `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作成者',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
    `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'モンスター䛾成長' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for monster_base
-- ----------------------------
DROP TABLE IF EXISTS `monster_base`;
CREATE TABLE monster_base (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name` varchar(255) NOT NULL COMMENT 'モンスター名称',
    `id_type` enum('Invest','Operate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '種別：Invest | Operate',
    `img_url` varchar(255) NULL COMMENT 'モンスターIMG-URL',
    `max_level` int NOT NULL COMMENT 'レベル',
    `created_at` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
    `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作成者',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
    `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'モンスター基本情報' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_monster_info
-- ----------------------------
DROP TABLE IF EXISTS `user_monster_info`;
CREATE TABLE user_monster_info (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_id` int NOT NULL COMMENT 'ユーザーID',
    `monster_id` int NOT NULL COMMENT 'モンスターID',
    `id_type` enum('Invest','Operate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '種別：Invest | Operate',
    `level` int NOT NULL COMMENT 'レベル',
    `current_experience` int NOT NULL COMMENT '現在経験値',
    `next_level_experience` int NOT NULL COMMENT '次のレベルの経験値 ',
    `creation_date_time` DATETIME NOT NULL COMMENT '生成日時',
    `monthly_power` int NOT NULL COMMENT '月間報酬',
    `created_at` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
    `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作成者',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
    `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE,
    CONSTRAINT `user_monster_info_ibfk_1` FOREIGN KEY (`monster_id`) REFERENCES `monster_base` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
    INDEX `user_id_index` (`user_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'ユーザーモンスター情報' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for monster_growth_level
-- ----------------------------
DROP TABLE IF EXISTS `monster_growth_level`;
CREATE TABLE `monster_growth_level` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'level_ID',
    `required_experience` int NOT NULL COMMENT '必要な経験',
    `rule_id` bigint NOT NULL COMMENT 'choice_activity_rule.id',
    `created_at` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
    `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '作成者',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
    `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `monster_growth_level_ruleidpk_idx` (`rule_id`),
    CONSTRAINT `monster_growth_level_ruleidpk` FOREIGN KEY (`rule_id`) REFERENCES `choice_activity_rule` (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'モンスターレベル情報' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for monster_food
-- ----------------------------
DROP TABLE IF EXISTS `monster_food`;
CREATE TABLE monster_food (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'level_ID',
    `type` enum('Invest','Operate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '種別：Invest | Operate',
    `currency` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通貨',
    `symbol_id` int NOT NULL COMMENT 'シンボルID',
    `year` int NOT NULL COMMENT '年',
    `week` int NOT NULL COMMENT '週',
    `created_at` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
    `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作成者',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
    `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'モンスターフード情報' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for monster_growth_history
-- ----------------------------
DROP TABLE IF EXISTS `monster_growth_history`;
CREATE TABLE monster_growth_history (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_id` int NOT NULL COMMENT 'ユーザーID',
    `monster_id` int NOT NULL COMMENT 'モンスターID',
    `symbol_id` int NOT NULL COMMENT 'シンボルID',
    `income` decimal(34, 20) NULL DEFAULT NULL COMMENT '売却損益',
    `conversion_rate` decimal(34, 20) NULL DEFAULT NULL COMMENT '変換割合',
    `bonus_rate` decimal(34, 20) NULL DEFAULT NULL COMMENT 'ボーナス割合',
    `week_food` varchar(128) NOT NULL COMMENT '今週食べたいもの',
    `experience_earned` int NOT NULL COMMENT '獲得経験値',
    `current_experience` int NOT NULL COMMENT '現在経験値',
    `next_level_experience` int NOT NULL COMMENT '次のレベルの経験値 ',
    `growth_rate` decimal(34, 20) NULL DEFAULT NULL COMMENT '成長比率',
    `level_before` int NOT NULL COMMENT 'レベル(食前)',
    `level_after` int NOT NULL COMMENT 'レベル(食後)',
    `created_at` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
    `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作成者',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
    `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE,
    CONSTRAINT `monster_growth_history_ibfk_1` FOREIGN KEY (`monster_id`) REFERENCES `monster_base` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'モンスターの成長履歴' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `quiz_question`;
CREATE TABLE `quiz_question`  (
      `id` bigint NOT NULL AUTO_INCREMENT,
      `quiz_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
      `title` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
      `answer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
      `correct_answer_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
      `option_A_content` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
      `option_B_content` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
      `option_C_content` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
      `option_D_content` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
      `option_A_wrong_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
      `option_B_wrong_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
      `option_C_wrong_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
      `option_D_wrong_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
      `published_at` timestamp NULL DEFAULT NULL,
      `created_at` timestamp NULL DEFAULT NULL,
      `updated_at` timestamp NULL DEFAULT NULL,
      `enable` tinyint NOT NULL,
      `created_user` varchar(200) NULL DEFAULT NULL,
      `updated_user` varchar(200) NULL DEFAULT NULL,
      PRIMARY KEY (`id`) USING BTREE,
      INDEX `quiz_question_published_at_index`(`published_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `quiz_question_published_record`;
CREATE TABLE `quiz_question_published_record`  (
       `id` bigint NOT NULL AUTO_INCREMENT,
       `quiz_question_id` bigint NOT NULL,
       `published_date` timestamp NOT NULL,
       `created_at` timestamp(3) NOT NULL,
       `updated_at` timestamp(3) NULL DEFAULT NULL,
       PRIMARY KEY (`id`) USING BTREE,
       INDEX `quiz_question_published_record_published_date_index`(`published_date` ASC) USING BTREE,
       INDEX `quiz_question_published_record_quiz_question_id_fk`(`quiz_question_id` ASC) USING BTREE,
       CONSTRAINT `quiz_question_published_record_quiz_question_id_fk` FOREIGN KEY (`quiz_question_id`) REFERENCES `quiz_question` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `quiz_user_answer`;
CREATE TABLE `quiz_user_answer`  (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `quiz_id` bigint NOT NULL,
     `answer_date` timestamp NOT NULL,
     `user_answer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
     `correct_answer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
     `quiz_title` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
     `quiz_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
     `user_id` bigint NULL DEFAULT NULL,
     `created_at` timestamp NULL DEFAULT NULL,
     `updated_at` timestamp NULL DEFAULT NULL,
     PRIMARY KEY (`id`) USING BTREE,
     INDEX `quiz_user_answer_user_id_index`(`user_id` ASC) USING BTREE,
     INDEX `quiz_user_answer_quiz_question_id_fk`(`quiz_id` ASC) USING BTREE,
     CONSTRAINT `quiz_user_answer_quiz_question_id_fk` FOREIGN KEY (`quiz_id`) REFERENCES `quiz_question` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
     CONSTRAINT `quiz_user_answer_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user_identity` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `shared_hist`;
CREATE TABLE `shared_hist` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
   `record_id` bigint NOT NULL COMMENT 'record id',
   `record_type` enum('INVEST_TRADE_HIST_SHARED','OPERATE_TRADE_HIST_SHARED','REWARD_HIST_SHARED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'target',
   `user_id` bigint NOT NULL COMMENT 'ユーザーID',
   `shared_d` timestamp NOT NULL COMMENT 'apply日',
   `rule_id` bigint DEFAULT NULL COMMENT 'choice_activity_rule.id',
   `choice_activity_function` enum('LOGIN_OPERATION_COURSE','PASS_QUIZ','LOGIN_INVESTMENT_COURSE','OPEN_INVESTMENT_ACCOUNT','FARM','BUY_SELL_TRADE','ELECTION_RESULTS_OF_VOTE','CROP_HARVESTED_SHARED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'choice_activity_rule.activity_function',
   `power_amount` bigint DEFAULT NULL,
   `target_type` enum('TWITTER_X') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'target',
   `created_at` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
   `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新日時',
   PRIMARY KEY (`id`) USING BTREE,
   KEY `shared_hist_record` (`record_id`,`record_type`,`shared_d` DESC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

DROP TABLE IF EXISTS `choice_vote_history`;
CREATE TABLE `choice_vote_history`
(
    `id`                 bigint auto_increment comment 'ID'
        primary key,
    `vote_id`            bigint                                                       NOT NULL COMMENT '投票',
    `user_id`            bigint                                                       NOT NULL COMMENT 'ユーザーID',
    `activity_id`        bigint                                                       NOT NULL COMMENT 'アクティビティID',
    `before_vote_power`  bigint                                                       NOT NULL COMMENT '投票前のパワー',
    `after_vote_power`   bigint                                                       NOT NULL COMMENT '投票後のパワー',
    `created_at`         timestamp(3)                    DEFAULT CURRENT_TIMESTAMP(3) NOT NULL COMMENT '作成時間',
    `updated_at`         timestamp                                                    NULL COMMENT '更新時間',
    constraint choice_vote_history_ibfk_1
        foreign key (user_id) references user_identity (id)
            on delete cascade,
    INDEX `choice_vote_history_vote_id_activity_index` (`vote_id`, `activity_id`),
    INDEX `user_id_index` (`user_id`)
) COMMENT '投票履歴' ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for point_huflt_task_status
-- ----------------------------
DROP TABLE IF EXISTS `point_hulft_task_status`;
CREATE TABLE `point_hulft_task_status`  (
    `id` int NOT NULL AUTO_INCREMENT,
    `task_name` varchar(180) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `parsing_status` enum('PENDING','SUCCESS','FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'pending, success, failed',
    `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
    `send_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
    `parsing_start_time` timestamp NULL DEFAULT NULL,
    `parsing_end_time` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for user_crop_status
-- ----------------------------
DROP TABLE IF EXISTS `user_crop_status`;
CREATE TABLE `user_crop_status` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'プライマリキーID',
    `user_id` bigint NOT NULL COMMENT 'ユーザーID',
    `asset_id` bigint NOT NULL COMMENT '資産ID（assetテーブルのプライマリキー）',
    `trade_type` enum('INVEST','OPERATE') NOT NULL COMMENT '取引種別（INVEST:投資, OPERATE:運用）',
    `growth_stage_id` int NOT NULL COMMENT '作物成長段階ID（crop_growth_stage.growth_stage_idと連携）',
    `profit_loss_amount` int NOT NULL COMMENT '評価損益額（JPY）',
    `created_at` timestamp(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT 'レコード作成日時',
    `updated_at` timestamp NULL COMMENT 'レコード更新日時',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_asset_trade` (`user_id`, `asset_id`, `trade_type`),
    KEY `idx_growth_stage` (`growth_stage_id`)
) ENGINE=InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT='ユーザー作物成長状態管理テーブル（現在の状態を保持）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_crop_status_history
-- ----------------------------
DROP TABLE IF EXISTS `user_crop_status_history`;
CREATE TABLE `user_crop_status_history` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'プライマリキーID',
    `user_id` bigint NOT NULL COMMENT 'ユーザーID',
    `asset_id` bigint NOT NULL COMMENT '資産ID（assetテーブルのプライマリキー）',
    `trade_type` enum('INVEST','OPERATE') NOT NULL COMMENT '取引種別（INVEST:投資, OPERATE:運用）',
    `before_growth_stage_id` int DEFAULT NULL COMMENT '変更前成長段階ID',
    `after_growth_stage_id` int NOT NULL COMMENT '変更後成長段階ID',
    `profit_loss_amount` int NOT NULL COMMENT '変更トリガーとなった損益額（JPY）',
    `created_at` timestamp(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT 'レコード作成日時',
    `updated_at` timestamp NULL COMMENT 'レコード更新日時',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT='ユーザー作物成長状態変更履歴テーブル' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ponta_convert_worker
-- ----------------------------
DROP TABLE IF EXISTS `ponta_convert_worker`;
CREATE TABLE `ponta_convert_worker`  (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
     `user_id` bigint NOT NULL COMMENT 'ユーザーID',
     `trade_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '取引番号',
     `point_amount` decimal(34, 20) NOT NULL COMMENT 'ポイント',
     `contents` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'コンテンツ',
     `flag` bit(1) NOT NULL COMMENT '同期フラグ(0非同期,1同期済み)',
     `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
     `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;