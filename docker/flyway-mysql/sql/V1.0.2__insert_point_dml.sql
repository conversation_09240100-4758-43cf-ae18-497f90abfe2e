SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Records of admin_role
-- ----------------------------
INSERT INTO `admin_role` VALUES (1, 'super', 1, now(), now());
INSERT INTO `admin_role` VALUES (2, 'admin', 1, now(), now());
-- ----------------------------
-- Records of admin_user
-- ----------------------------
INSERT INTO `admin_user` VALUES (1, '<EMAIL>', '$2a$10$qFBYExnKXOZezg1UFPEIRuKB9QdlE38w84D/GJ.fssapXEK/ExWIa', b'1', b'1', b'1', b'1', b'1', b'0', now(), now());

-- ----------------------------
-- Records of admin_user_authority
-- ----------------------------
INSERT INTO `admin_user_authority` VALUES (1, 1, '1', now(), now());

-- ----------------------------
-- Records of affiliate_info
-- ----------------------------
INSERT INTO `affiliate_info` VALUES (1, 'INTERSPACE', '_atnct', now(), now());
INSERT INTO `affiliate_info` VALUES (2, 'CERES', 'adm_adtr_xuid', now(), now());

-- ----------------------------
-- Records of app_configuration
-- ----------------------------
INSERT INTO `app_configuration` VALUES (1, 'point.common.config.GmoAuthorizationTokenConfiguration', '{\"accessToken\":\"accessToken\",\"refreshToken\":\"refreshToken\",\"scope\":\"private:transfer private:bulk-transfer private:virtual-account private:account\",\"tokenType\":\"Bearer\",\"expiresIn\":\"expiresIn\",\"sessionId\":\"sessionId\",\"hashKey\":\"hashKey\",\"state\":\"state\",\"expireAt\":**************}', now(), now(), 0);
INSERT INTO `app_configuration` VALUES (2, 'point.common.config.ChoiceVoteConfiguration', '{\"maxVotePowerPerUser\":10000}', now(), now(), 0);

-- ----------------------------
-- Records of balance_notify_config
-- ----------------------------
INSERT INTO `balance_notify_config` VALUES (1, 'BTC', ********.00000000000000000000, 50.00000000000000000000, 'AMBER', '<EMAIL>', b'0', now(), now());
INSERT INTO `balance_notify_config` VALUES (2, 'ETH', ********.00000000000000000000, 50.00000000000000000000, 'AMBER', '<EMAIL>', b'0', now(), now());

INSERT INTO `balance_notify_config` VALUES (4, 'USDC',********.00000000000000000000, 50.00000000000000000000, 'AMBER', '<EMAIL>', b'0', now(), now());
INSERT INTO `balance_notify_config` VALUES (5, 'SOL', ********.00000000000000000000, 50.00000000000000000000, 'AMBER', '<EMAIL>', b'0', now(), now());
INSERT INTO `balance_notify_config` VALUES (6, 'DOGE', ********.00000000000000000000, 50.00000000000000000000, 'AMBER', '<EMAIL>', b'0', now(), now());
INSERT INTO `balance_notify_config` VALUES (7, 'AVAX', ********.00000000000000000000, 50.00000000000000000000, 'AMBER', '<EMAIL>', b'0', now(), now());
INSERT INTO `balance_notify_config` VALUES (8, 'ADA', ********.00000000000000000000, 50.00000000000000000000, 'AMBER', '<EMAIL>', b'0', now(), now());
-- ----------------------------
-- Records of cover_order_config
-- ----------------------------
INSERT INTO `cover_order_config` VALUES (1, 101, 'AMBER', 0.20000000000000000000, 0.00********0000000000, 100.00000000000000000000, b'1', NOW(), NOW(), 'INVEST');
INSERT INTO `cover_order_config` VALUES (2, 102, 'AMBER', 0.20000000000000000000, 0.0********00000000000, 100.00000000000000000000, b'1', NOW(), NOW(), 'INVEST');
INSERT INTO `cover_order_config` VALUES (3, 103, 'AMBER', 0.20000000000000000000, 1.00000000000000000000, 100.00000000000000000000, b'0', NOW(), NOW(), 'INVEST');
INSERT INTO `cover_order_config` VALUES (4, 202, 'AMBER', 0.20000000000000000000, 0.00********0000000000, 100.00000000000000000000, b'1', NOW(), NOW(), 'OPERATE');
INSERT INTO `cover_order_config` VALUES (5, 204, 'AMBER', 0.20000000000000000000, 1.00000000000000000000, 100.00000000000000000000, b'1', NOW(), NOW(), 'OPERATE');
INSERT INTO `cover_order_config` VALUES (6, 205, 'AMBER', 0.20000000000000000000, 0.05000000000000000000, 100.00000000000000000000, b'1', NOW(), NOW(), 'OPERATE');
INSERT INTO `cover_order_config` VALUES (7, 206, 'AMBER', 0.20000000000000000000, 5.00000000000000000000, 100.00000000000000000000, b'1', NOW(), NOW(), 'OPERATE');
INSERT INTO `cover_order_config` VALUES (8, 207, 'AMBER', 0.20000000000000000000, 10.00000000000000000000, 100.00000000000000000000, b'1', NOW(), NOW(), 'OPERATE');
INSERT INTO `cover_order_config` VALUES (9, 208, 'AMBER', 0.20000000000000000000, 2.00000000000000000000, 100.00000000000000000000, b'1', NOW(), NOW(), 'OPERATE');
INSERT INTO `cover_order_config` VALUES (10, 209, 'AMBER', 0.20000000000000000000, 20.00000000000000000000, 100.00000000000000000000, b'1', NOW(), NOW(), 'OPERATE');
INSERT INTO `cover_order_config` VALUES (11, 210, 'AMBER', 0.20000000000000000000, 0.0********00000000000, 100.00000000000000000000, b'1', NOW(), NOW(), 'OPERATE');
-- ----------------------------
-- Records of currency_config
-- ----------------------------
INSERT INTO `currency_config` VALUES (1, 'INVEST', 'JPY', 1.00000000000000000000, 2.00000000000000000000, 0.00000000000000000000, 300000000.0000000000, 0.00000000000000000000, 0.00000000000000000000, b'1', b'1', b'1', b'0', 1, now(), now());
INSERT INTO `currency_config` VALUES (2, 'INVEST', 'BTC', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'1', b'0', 1, now(), now());
INSERT INTO `currency_config` VALUES (3, 'INVEST', 'ETH', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'1', b'0', 1, now(), now());
INSERT INTO `currency_config` VALUES (4, 'INVEST', 'XRP', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'1', b'0', 1, now(), now());
INSERT INTO `currency_config` VALUES (5, 'INVEST', 'POINT', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'0', b'1', b'0', 0, now(), now());
INSERT INTO `currency_config` VALUES (6, 'OPERATE', 'JPY', 1.00000000000000000000, 2.00000000000000000000, 0.00000000000000000000, 300000000.0000000000, 0.00000000000000000000, 0.00000000000000000000, b'1', b'1', b'1', b'0', 1, now(), now());
INSERT INTO `currency_config` VALUES (7, 'OPERATE', 'BALC', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'1', b'0', 1, now(), now());
INSERT INTO `currency_config` VALUES (8, 'OPERATE', 'BTC', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'1', b'0', 1, now(), now());
INSERT INTO `currency_config` VALUES (9, 'OPERATE', 'ACTC', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'1', b'0', 1, now(), now());
INSERT INTO `currency_config` VALUES (10, 'OPERATE', 'USDC', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'1', b'0', 1, now(), now());
INSERT INTO `currency_config` VALUES (11, 'OPERATE', 'SOL', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'0', b'0', 0, now(), now());
INSERT INTO `currency_config` VALUES (12, 'OPERATE', 'DOGE', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'0', b'0', 0, now(), now());
INSERT INTO `currency_config` VALUES (13, 'OPERATE', 'XRP', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'0', b'0', 0, now(), now());
INSERT INTO `currency_config` VALUES (14, 'OPERATE', 'AVAX', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'0', b'0', 0, now(), now());
INSERT INTO `currency_config` VALUES (15, 'OPERATE', 'ADA', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'0', b'0', 0, now(), now());
INSERT INTO `currency_config` VALUES (16, 'OPERATE', 'ETH', 0.00000000000000000000, 0.00000000000000000000, 0.00000000000000000000, 999999999.0000000000, 500.00000000000000000000, 1.00000000000000000000, b'1', b'1', b'0', b'0', 0, now(), now());
-- ----------------------------
-- Records of currency_pair_config
-- ----------------------------
INSERT INTO `currency_pair_config` VALUES (1, 'INVEST', 'BTC_JPY', 0.00********0000000000, 1.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'1', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
INSERT INTO `currency_pair_config` VALUES (2, 'INVEST', 'ETH_JPY', 0.0********00000000000, 20.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'1', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
INSERT INTO `currency_pair_config` VALUES (3, 'INVEST', 'XRP_JPY', 10.00000000000000000000, 50000.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'1', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
INSERT INTO `currency_pair_config` VALUES (4, 'OPERATE', 'BALC_JPY', 0.00000000000000000001, 50000.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'1', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
INSERT INTO `currency_pair_config` VALUES (5, 'OPERATE', 'BTC_JPY', 0.00000000000000000001, 180.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'1', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
INSERT INTO `currency_pair_config` VALUES (6, 'OPERATE', 'ACTC_JPY', 0.00000000000000000001, 50000.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'1', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
INSERT INTO `currency_pair_config` VALUES (7, 'OPERATE', 'USDC_JPY', 0.00000000000000000001, 50000.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'1', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
INSERT INTO `currency_pair_config` VALUES (8, 'OPERATE', 'SOL_JPY', 0.00000000000000000001, 50000.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'0', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
INSERT INTO `currency_pair_config` VALUES (9, 'OPERATE', 'DOGE_JPY', 0.00000000000000000001, 50000.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'0', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
INSERT INTO `currency_pair_config` VALUES (10, 'OPERATE', 'XRP_JPY', 0.00000000000000000001, 50000.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'0', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
INSERT INTO `currency_pair_config` VALUES (11, 'OPERATE', 'AVAX_JPY', 0.00000000000000000001, 50000.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'0', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
INSERT INTO `currency_pair_config` VALUES (12, 'OPERATE', 'ADA_JPY', 0.00000000000000000001, 50000.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'0', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
INSERT INTO `currency_pair_config` VALUES (13, 'OPERATE', 'ETH_JPY', 0.00000000000000000001, 3500.00000000000000000000, 300000.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 1.00000000000000000000, 0.05000000000000000000, 0.20000000000000000000, b'1', b'0', NULL, 100.00000000000000000000, 5, 5, 0.00000000000000000000, 0.00000000000000000000, NULL, NULL, NULL, 12, 0.25000000000000000000, 1, 3, 12, 1000000.00000000000000000000, 1000000.00000000000000000000, 3, now(), now(), 4.00000000000000000000, 1.00000000000000000000);
-- ----------------------------
-- Records of mail_noreply
-- ----------------------------
INSERT INTO `mail_noreply` VALUES (1, 'ACCOUNT_CREATED', '<EMAIL>', '【backseat】パスワード設定のお願い', 'backseat にご登録いただきありがとうございます。\r\n以下のURLよりパスワードを設定してください。\r\nURLの有効期限は{1}です。\r\n\r\n{0}\r\n※お客様専用のURLとなりますので、他者への開示はお控えください。\r\n\r\nパスワードの設定後、ログインいただき、アカウント状況より本人情報登録の\r\nお手続きをお願いいたします。\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたら下記カスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n----------------------------------------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (2, 'PASSWORD_COMPLETE', '<EMAIL>', '【backseat】アカウント登録完了のお知らせ', 'backseat にご登録いただきありがとうございます。\r\n\r\nアカウント登録が完了いたしました。\r\n\r\n次に、アカウント状況より本人情報登録のお手続きをお願いいたします。\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたら下記カスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n----------------------------------------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (3, 'USER_EKYC_STARTED', '<EMAIL>', '【backseat】本人確認書類ご提出のお願い', 'この度は本人情報のご登録をいただきありがとうございます。\r\n\r\n以下のURLより本人確認書類をご提出下さい。\r\nURLの有効期限は{1}です。\r\n\r\n{0}\r\n※Polarify本人認証サービスへ移動します。\r\n※お客様専用のURLです。他者への開示はお控えください。\r\n\r\n本人確認書類のご提出後、当社にて口座開設審査を行い、審査結果はメールで\r\nお送りいたします。\r\n審査の結果、口座開設をお断りさせていただく場合もございますので、\r\nあらかじめご了承ください。\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたら下記カスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n----------------------------------------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (4, 'USER_KYC_STATUS_CHANGED_EKYC_SUCCESS', '<EMAIL>', '【backseat】本人確認書類受付完了のお知らせ', 'この度は本人確認書類のご提出をいただきありがとうございます。\r\n\r\n当社にて口座開設審査を行い、審査結果はメールでお送りいたします。\r\n審査の結果、口座開設をお断りさせていただく場合もございますので、\r\nあらかじめご了承ください。\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたら下記カスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n----------------------------------------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (5, 'EKYC_BPO_FAILED_ERROR', '<EMAIL>', '【backseat】本人情報再提出のお願い', 'backseat。\r\nこの度は本人情報のご登録をいただき、誠にありがとうございます。\r\n以下の内容の不備がございましたので、ご確認のうえ、再申請のお手続きをお願いします。\r\n\r\n{0}\r\n\r\n本人確認書類のみご提出の場合も、再度ご登録情報ページよりお手続きを進めてください。\r\nご登録の情報に修正がない場合はそのままお進みいただくと、再提出用のURLがメールで届きます。\r\nまた、本人情報のみの申請の場合も、再度本人確認書類をご提出ください。\r\n\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n----------------------------------------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (6, 'INFORMATION_REQUIRED', '<EMAIL>', '【backseat】本人確認に関するご連絡', 'backseat。\r\nこの度は本人情報のご登録をいただき、誠にありがとうございます。\r\n以下の内容の不備がございましたので、ご確認のうえ、再申請のお手続きをお願いします。\r\n\r\n{0}\r\n本人確認書類のみご提出の場合も、再度ご登録情報ページよりお手続きを進めてください。\r\nご登録の情報に修正がない場合はそのままお進みいただくと、再提出用のURLがメールで届きます。\r\nまた、本人情報のみの申請の場合も、再度本人確認書類をご提出ください。\r\n\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n----------------------------------------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (7, 'ACCOUNT_OPENING_DONE', '<EMAIL>', '【backseat】口座開設完了のお知らせ', 'backseat。\r\nお客様の口座開設が完了いたしました。\r\n\r\n日本円の入金または暗号資産の送金後、お取引がいただけます。\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n----------------------------------------------------------',now(),now());
INSERT INTO `mail_noreply` VALUES (8, 'DONE', '<EMAIL>', '【backseat】本人情報変更完了のお知らせ', 'backseat。\r\n 本人情報登録の変更が完了いたしました。\r\n\r\n 引き続きよろしくお願いいたします。\r\n\r\n ※このメールアドレスは送信専用のためご返信いただけません。\r\n ご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n----------------------------------------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (9, 'REJECTED', '<EMAIL>', '【backseat】口座開設審査結果のお知らせ', 'backseat。\r\nこの度は本人情報のご登録をいただき、誠にありがとうございます。\r\n\r\nお客様からご登録いただきました情報を基に審査させていただきましたが、\r\n今回は残念ながら取引口座開設を見合わせていただくことになりました。\r\n\r\nお忙しいところお手数をおかけいただいたにもかかわらず、\r\nご期待にそえない結果となってしまったことお詫び申し上げます。\r\n\r\nなお、審査基準につきましてはご回答いたしかねますので、あらかじめご了承ください。\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n----------------------------------------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (10, 'ACCOUNT_CLOSED', '<EMAIL>', '【backseat】口座解約のお知らせ', 'backseat。\r\n\r\n口座解約手続きが完了いたしましたのでご報告いたします。\r\n再度サービスをご希望される場合は、新規登録より行ってください。\r\n\r\nbackseatを利用いただき誠にありがとうございました。\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n----------------------------------------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (11, 'ACCOUNT_DUPLICATION', '<EMAIL>', '【backseat】口座開設に関するお知らせ', 'backseat。\r\nこの度は本人情報のご登録をいただき、誠にありがとうございます。\r\n\r\nお客様からご登録いただきました情報を確認いたしましたところ、\r\n既に同様の内容でご登録いただいておりました。\r\n\r\nどちらかを閉鎖させてだきますので、今後のお手続きに関しましては\r\n当社カスタマーサポートまでご連絡ください。\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n----------------------------------------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (12, 'DEPOSIT_DONE', '<EMAIL>', '【backseat】入金完了のお知らせ', 'backseat、ありがとうございます。\r\n\r\nお客様の口座に以下の入金が完了いたしました。\r\nログイン後、詳細をご確認ください。\r\n\r\n通貨：JPY\r\n金額：{0}\r\n現在のご利用可能残高：{1}\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。\r\n-----------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n-----------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (13, 'FIAT_WITHDRAWAL_APPLY', '<EMAIL>', '【backseat】出金受付完了のお知らせ', 'backseat、ありがとうございます。\r\n\r\n出金のご依頼を承りました。\r\n手続き完了まで、今しばらくお待ちください。\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。\r\n-----------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n-----------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (14, 'FIAT_WITHDRAWAL_DONE', '<EMAIL>', '【backseat】出金完了のお知らせ', 'backseat、ありがとうございます。\r\n\r\nお客様の口座に以下の送金手続きが完了いたしました。\r\nログイン後、詳細をご確認ください。\r\n\r\n通貨：JPY\r\n金額：{0}\r\n現在のご利用可能残高：{1}\r\n※送金手続きから着金まで少しお時間がかかる場合があります。\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。\r\n-----------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n-----------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (19, 'MFA_CODE', '<EMAIL>', '【backseat】 認証コードのお知らせ', 'backseat。\r\n認証コードは以下になります。よろしくお願いします。\r\n\r\n認証コード $code\r\n\r\n\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたら下記カスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n----------------------------------------------------------', now(), now());
INSERT INTO `mail_noreply` VALUES (20, 'SMS_CODE', '<EMAIL>', '【backseat】 認証コードのお知らせ', '確認コードは $code です。5分以内に入力してください。コードは共有しないでください。', now(), now());
INSERT INTO `mail_noreply` VALUES (24, 'POS_ORDER', '<EMAIL>', '【backseat】注文受付のお知らせ', 'backseat、ありがとうございます。\r\n \r\n ご注文を受け付けました。\r\n 詳細は「注文一覧」からご確認ください。\r\n \r\n 受付内容\r\n \r\n 通貨ペア : ${instrumentId}\r\n 売買 : ${side}\r\n 注文方法 : ${orderType}\r\n 注文数量 : ${size}\r\n 注文価格 : ${price}\r\n 注文番号 : ${orderId}\r\n \r\n 詳細は、「取引履歴」よりご確認ください。\r\n ※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\\r\\n---------------------------------------------------------- ', now(), now());
INSERT INTO `mail_noreply` VALUES (25, 'POS_TRADE', '<EMAIL>', '【backseat】約定通知のお知らせ', 'backseat、ありがとうございます。\r\n \r\n 以下のご注文が約定しました。\r\n 約定の詳細は「約定一覧」よりご確認ください。\r\n \r\n ■ 約定内容\r\n通貨ペア : ${instrumentId}\r\n注文番号 : ${orderId}\r\n売買 : ${side}\r\n注文方法 : ${orderType}\r\n注文数量 : ${size}\r\n注文価格 : ${price}\r\n約定数量 : ${trade_size}\r\n約定価格 : ${trade_price}\r\n未約定数量 : ${remaining_amount}\r\n \r\n ご不明な点はカスタマーサポートまでお問い合わせください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\\r\\n---------------------------------------------------------- ', now(), now());
INSERT INTO `mail_noreply` VALUES (26, 'POS_FAILED_ORDER', '<EMAIL>', '【backseat】注文失敗のお知らせ', 'backseat、ありがとうございます。\r\n\r\n ご注文の取消しが完了いたしました。\r\n 詳細は、「注文一覧」からご確認ください。\r\n\r\n 取消し内容\r\n\r\n 通貨ペア : ${instrumentId}\r\n 売買 : ${side}\r\n 注文方法 : ${orderType}\r\n 注文数量 : ${size}\r\n 注文価格 : ${price}\r\n 注文番号 : ${orderId}\r\n 未約定数量 : ${remaining_amount}\r\n 詳細は「全ての注文を表示する」よりご確認ください。\r\n※このメールアドレスは送信専用のためご返信いただけません。\r\nご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。\r\n----------------------------------------------------------\r\n株式会社backseat\r\n〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階\r\n■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。\r\n・登録番号：関東財務局長 第00026号\r\n■お問い合わせ先：backseatカスタマーサポート\r\nhttps://support.backseat.co.jp/hc/ja/requests/new\r\n---------------------------------------------------------- ', now(), now());
INSERT INTO `mail_noreply` VALUES (27, 'BALANCE_NOTIFY', '<EMAIL>', '【backseat】販売所カバー資金不足アラート！', '販売所のカバー資金が不足していますので残高を確認してください！\r\n日時:         {0}\r\n通貨:         {1}\r\n利用可能残高: {2}', now(), now());
insert into mail_noreply
(mail_noreply_type, from_address, title, contents, created_at, updated_at)
    value('VOTE_REWARD_RESULT_NOTIFY','<EMAIL>','【backseat】報酬分配のお知らせ','backseat、ありがとうございます。

報酬分配が完了いたしました。
 詳細は、「報酬履歴画面」からご確認ください。

報酬分配詳細内容
分配区分 : {0}
分配日時 : {1}
分配額 : {2}
ご質問、ご不明点がございましたらカスタマーサポートまでご連絡ください。
----------------------------------------------------------
株式会社backseat
〒107-0052 東京都港区赤坂2-18-14 赤坂STビル2階
■当社は、金融庁・財務局の登録を受けた暗号資産交換業者です。
・登録番号：関東財務局長 第00026号
■お問い合わせ先：backseatカスタマーサポート
https://support.backseat.co.jp/hc/ja/requests/new
---------------------------------------------------------- ',now(),now());

-- ----------------------------
-- Records of pos_market_maker_config
-- ----------------------------
INSERT INTO `pos_market_maker_config` VALUES (1, 101, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
INSERT INTO `pos_market_maker_config` VALUES (2, 102, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
INSERT INTO `pos_market_maker_config` VALUES (3, 103, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
INSERT INTO `pos_market_maker_config` VALUES (4, 201, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
INSERT INTO `pos_market_maker_config` VALUES (5, 202, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
INSERT INTO `pos_market_maker_config` VALUES (6, 203, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
INSERT INTO `pos_market_maker_config` VALUES (7, 204, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
INSERT INTO `pos_market_maker_config` VALUES (8, 205, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
INSERT INTO `pos_market_maker_config` VALUES (9, 206, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
INSERT INTO `pos_market_maker_config` VALUES (10, 207, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
INSERT INTO `pos_market_maker_config` VALUES (11, 208, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
INSERT INTO `pos_market_maker_config` VALUES (12, 209, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
INSERT INTO `pos_market_maker_config` VALUES (13, 210, 'AMBER', b'1', 1.00000000000000000000, NOW(), NOW());
-- ----------------------------
-- Records of symbol
-- ----------------------------
INSERT INTO `symbol` VALUES (101, 'INVEST', 'BTC_JPY', 1, now(), now());
INSERT INTO `symbol` VALUES (102, 'INVEST', 'ETH_JPY', 1, now(), now());
INSERT INTO `symbol` VALUES (103, 'INVEST', 'XRP_JPY', 1, now(), now());
INSERT INTO `symbol` VALUES (201, 'OPERATE', 'BALC_JPY', 1, now(), now());
INSERT INTO `symbol` VALUES (202, 'OPERATE', 'BTC_JPY', 1, now(), now());
INSERT INTO `symbol` VALUES (203, 'OPERATE', 'ACTC_JPY', 1, now(), now());
INSERT INTO `symbol` VALUES (204, 'OPERATE', 'USDC_JPY', 1, now(), now());
INSERT INTO `symbol` VALUES (205, 'OPERATE', 'SOL_JPY', 0, now(), now());
INSERT INTO `symbol` VALUES (206, 'OPERATE', 'DOGE_JPY', 0, now(), now());
INSERT INTO `symbol` VALUES (207, 'OPERATE', 'XRP_JPY', 0, now(), now());
INSERT INTO `symbol` VALUES (208, 'OPERATE', 'AVAX_JPY', 0, now(), now());
INSERT INTO `symbol` VALUES (209, 'OPERATE', 'ADA_JPY', 0, now(), now());
INSERT INTO `symbol` VALUES (210, 'OPERATE', 'ETH_JPY', 0, now(), now());
-- ----------------------------
-- Records of system_config
-- ----------------------------
INSERT INTO `system_config` VALUES (1, NULL, NULL, 'marketMakerUserId', '5', now(), now());
INSERT INTO `system_config` VALUES (2, NULL, NULL, 'FINANCIAL_ASSETS_DEVIATION_CHECK_SPAN_HOURS', '24', now(), now());
INSERT INTO `system_config` VALUES (3, NULL, NULL, 'FINANCIAL_ASSETS_DEVIATION_TRADE_ASSET_AMOUNT', '1000000', now(), now());
INSERT INTO `system_config` VALUES (4, NULL, NULL, 'FINANCIAL_ASSETS_DEVIATION_TRADES_THRESHOLD', '3', now(), now());
INSERT INTO `system_config` VALUES (5, NULL, NULL, 'FINANCIAL_ASSETS_DEVIATION_USER_INFO', '3', now(), now());
INSERT INTO `system_config` VALUES (6, NULL, NULL, 'INVESTMENT_PURPOSE_DEVIATION_CHECK_SPAN_HOURS', '24', now(), now());
INSERT INTO `system_config` VALUES (7, NULL, NULL, 'INVESTMENT_PURPOSE_DEVIATION_TRADES_THRESHOLD', '20', now(), now());
INSERT INTO `system_config` VALUES (8, NULL, NULL, 'INVESTMENT_PURPOSE_DEVIATION_USER_INFO', '1,2,3', now(), now());
INSERT INTO `system_config` VALUES (9, NULL, NULL, 'SAME_IP_CHECK_SPAN_HOURS', '1', now(), now());
INSERT INTO `system_config` VALUES (10, NULL, NULL, 'SAME_IP_THRESHOLD_COUNT', '3', now(), now());

-- ----------------------------
-- Records of user_agreement_file
-- ----------------------------
INSERT INTO `user_agreement_file` VALUES (1, 'USE_API_TOKEN', 1, now(), now());
INSERT INTO `user_agreement_file` VALUES (2, 'TERMS_OF_SERVICE', 1, now(), now());
INSERT INTO `user_agreement_file` VALUES (3, 'DISCLOSURE_STATEMENT', 1, now(), now());
INSERT INTO `user_agreement_file` VALUES (4, 'PRIVACY_POLICY', 1, now(), now());
INSERT INTO `user_agreement_file` VALUES (5, 'TERMS_OF_SERVICE_FOR_OPERATE', 0, now(), now());

-- ----------------------------
-- Records of worker_master
-- ----------------------------
INSERT INTO `worker_master` VALUES (1, 'prd', 'assetSummaryCalculator', NULL, NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (2, 'prd', 'assetSummaryRecalculator', NULL, NULL, NULL, b'1', now(), now());




INSERT INTO `worker_master` VALUES (7, 'prd', 'gmoDepositChecker', 'INVEST', NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (8, 'prd', 'gmoTokenUpdater', 'INVEST', NULL, NULL, b'1', now(), now());




INSERT INTO `worker_master` VALUES (12, 'prd', 'bulkTransferExecutor', 'INVEST', NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (13, 'prd', 'bulkTransferResultChecker', 'INVEST', NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (14, 'prd', 'sameIpChecker', 'INVEST', NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (15, 'prd', 'gmoDepositReconcileExecutor', NULL, NULL, NULL, b'1', now(), now());




INSERT INTO `worker_master` VALUES (20, 'prd', 'balanceNotifyMaker', NULL, NULL, NULL, b'1', now(), now());

INSERT INTO `worker_master` VALUES (21, 'prd', 'posCandlestickMaker', 'INVEST', 'BTC_JPY', 1000, b'1', now(), now());
INSERT INTO `worker_master` VALUES (22, 'prd', 'posCandlestickMaker', 'INVEST', 'ETH_JPY', 1000, b'1', now(), now());
INSERT INTO `worker_master` VALUES (23, 'prd', 'posCandlestickMaker', 'INVEST', 'XRP_JPY', 1000, b'1', now(), now());

INSERT INTO `worker_master` VALUES (24, 'prd', 'posCandlestickMaker', 'OPERATE', 'BALC_JPY', 1000, b'1', now(), now());
INSERT INTO `worker_master` VALUES (25, 'prd', 'posCandlestickMaker', 'OPERATE', 'BTC_JPY', 1000, b'1', now(), now());
INSERT INTO `worker_master` VALUES (26, 'prd', 'posCandlestickMaker', 'OPERATE', 'ACTC_JPY', 1000, b'1', now(), now());
INSERT INTO `worker_master` VALUES (27, 'prd', 'posCandlestickMaker', 'OPERATE', 'USDC_JPY', 1000, b'1', now(), now());

INSERT INTO `worker_master` VALUES (28, 'prd', 'posBestPriceMaker', 'INVEST', 'BTC_JPY', 3000, b'1', now(), now());
INSERT INTO `worker_master` VALUES (29, 'prd', 'posBestPriceMaker', 'INVEST', 'ETH_JPY', 3000, b'1', now(), now());
INSERT INTO `worker_master` VALUES (30, 'prd', 'posBestPriceMaker', 'INVEST', 'XRP_JPY', 3000, b'1', now(), now());

INSERT INTO `worker_master` VALUES (31, 'prd', 'posBestPriceMaker', 'OPERATE', 'BALC_JPY', 3000, b'1', now(), now());
INSERT INTO `worker_master` VALUES (32, 'prd', 'posBestPriceMaker', 'OPERATE', 'BTC_JPY', 3000, b'1', now(), now());
INSERT INTO `worker_master` VALUES (33, 'prd', 'posBestPriceMaker', 'OPERATE', 'ACTC_JPY', 3000, b'1', now(), now());
INSERT INTO `worker_master` VALUES (34, 'prd', 'posBestPriceMaker', 'OPERATE', 'USDC_JPY', 3000, b'1', now(), now());

INSERT INTO `worker_master` VALUES (35, 'prd', 'posCoverAmberMaker', 'INVEST', 'BTC_JPY', 1000, b'0', now(), now());
INSERT INTO `worker_master` VALUES (36, 'prd', 'posCoverAmberMaker', 'INVEST', 'ETH_JPY', 1000, b'0', now(), now());
INSERT INTO `worker_master` VALUES (37, 'prd', 'posCoverAmberMaker', 'INVEST', 'XRP_JPY', 1000, b'0', now(), now());

INSERT INTO `worker_master` VALUES (38, 'prd', 'posOrderArchiver', 'INVEST', 'BTC_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (39, 'prd', 'posOrderArchiver', 'INVEST', 'ETH_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (40, 'prd', 'posOrderArchiver', 'INVEST', 'XRP_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (41, 'prd', 'posTradeArchiver', 'INVEST', 'BTC_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (42, 'prd', 'posTradeArchiver', 'INVEST', 'ETH_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (43, 'prd', 'posTradeArchiver', 'INVEST', 'XRP_JPY', NULL, b'1', now(), now());

INSERT INTO `worker_master` VALUES (44, 'prd', 'posOrderArchiver', 'OPERATE', 'BALC_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (45, 'prd', 'posOrderArchiver', 'OPERATE', 'BTC_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (46, 'prd', 'posOrderArchiver', 'OPERATE', 'ACTC_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (47, 'prd', 'posOrderArchiver', 'OPERATE', 'USDC_JPY', NULL, b'1', now(), now());


INSERT INTO `worker_master` VALUES (48, 'prd', 'posTradeArchiver', 'OPERATE', 'BALC_JPY', NULL, b'1',now(), now());
INSERT INTO `worker_master` VALUES (49, 'prd', 'posTradeArchiver', 'OPERATE', 'BTC_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (50, 'prd', 'posTradeArchiver', 'OPERATE', 'ACTC_JPY', NULL, b'1',now(), now());
INSERT INTO `worker_master` VALUES (51, 'prd', 'posTradeArchiver', 'OPERATE', 'USDC_JPY', NULL, b'1', now(), now());



INSERT INTO `worker_master` VALUES (52, 'prd', 'operateAggregateOrderMaker', 'OPERATE', 'BTC_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (53, 'prd', 'operateAggregateOrderMaker', 'OPERATE', 'USDC_JPY', NULL, b'1', now(), now());

INSERT INTO `worker_master` VALUES (54, 'prd', 'operateCoverOrderBuyMaker', 'OPERATE', 'BTC_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (55, 'prd', 'operateCoverOrderBuyMaker', 'OPERATE', 'USDC_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (56, 'prd', 'operateCoverOrderBuyMaker', 'OPERATE', 'SOL_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (57, 'prd', 'operateCoverOrderBuyMaker', 'OPERATE', 'DOGE_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (58, 'prd', 'operateCoverOrderBuyMaker', 'OPERATE', 'XRP_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (59, 'prd', 'operateCoverOrderBuyMaker', 'OPERATE', 'AVAX_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (60, 'prd', 'operateCoverOrderBuyMaker', 'OPERATE', 'ADA_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (61, 'prd', 'operateCoverOrderBuyMaker', 'OPERATE', 'ETH_JPY', NULL, b'1', now(), now());


INSERT INTO `worker_master` VALUES (62, 'prd', 'operateCoverOrderSellMaker', 'OPERATE', 'BTC_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (63, 'prd', 'operateCoverOrderSellMaker', 'OPERATE', 'USDC_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (64, 'prd', 'operateCoverOrderSellMaker', 'OPERATE', 'SOL_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (65, 'prd', 'operateCoverOrderSellMaker', 'OPERATE', 'DOGE_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (66, 'prd', 'operateCoverOrderSellMaker', 'OPERATE', 'XRP_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (67, 'prd', 'operateCoverOrderSellMaker', 'OPERATE', 'AVAX_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (68, 'prd', 'operateCoverOrderSellMaker', 'OPERATE', 'ADA_JPY', NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (69, 'prd', 'operateCoverOrderSellMaker', 'OPERATE', 'ETH_JPY', NULL, b'1', now(), now());


INSERT INTO `worker_master` VALUES (72, 'prd', 'pointPartnerStatusUpdater', 'OPERATE', NULL, NULL, b'1', now(), now());



INSERT INTO `worker_master` VALUES (76, 'prd', 'pointUserSummaryCalculator', NULL, NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (77, 'prd', 'ChoicePowerCalculator', NULL, NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (78, 'prd', 'ChoicePowerMonthlyCalculator', NULL, NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (79, 'prd', 'ChoiceActivityMaker', NULL, NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (80, 'prd', 'ChoiceActivityStatusMaker', NULL, NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (81, 'prd', 'ChoiceActivityBasePriceUpdater', NULL, NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (82, 'prd', 'QuizQuestionPublishUpdater', NULL, NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (83, 'prd', 'UserVoteRewardCalculator', NULL, NULL, NULL, b'1', now(), now());

INSERT INTO `worker_master` VALUES (84, 'prd', 'pontaGrantPointHulftUpload', NULL, NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (85, 'prd', 'choiceActivityTemplateStatusUpdater', NULL, NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (86, 'prd', 'pontaGrantPointHulftParse', NULL, NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (87, 'prd', 'UserVoteRewardExpireChecker', NULL, NULL, NULL, b'1', now(), now());
INSERT INTO `worker_master` VALUES (88, 'prd', 'pontaConverMaker', 'OPERATE', NULL, 300000, b'1', now(), now());

-- ----------------------------
-- Records of choice_activity_rule
-- ----------------------------
INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (1, '運用コースにログインする', 'LOGIN_OPERATION_COURSE', 1, 'Operate', 'DAILY', 'FIXED', 1, null,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());







INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (3, 'クイズに正解する', 'PASS_QUIZ', 1, 'Operate', 'EACH_TIME', 'FIXED', 10, null, '2025-05-31 00:00:00',
        '2035-05-31 00:00:00', 'ACTIVE', now(), now());







INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (5, 'Pontaビットコイン投票で当選結果を投稿する', 'ELECTION_RESULTS_OF_VOTE', 1, 'Operate', 'DAILY', 'FIXED',
        100, NULL, '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (6, 'Pontaビットコin牧場で収穫した作物をシェアする', 'CROP_HARVESTED_SHARED', 1, 'Operate', 'DAILY', 'FIXED',
        100, NULL, '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (7, '売買取引を行う', 'BUY_SELL_TRADE', 1, 'Operate', 'EACH_TIME', 'PROPORTIONAL', NULL, 0.01,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (8, 'ポンタファームでモンスターを育成する→レベル1', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 30, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (9, 'ポンタファームでモンスターを育成する→レベル2', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 60, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (10, 'ポンタファームでモンスターを育成する→レベル3', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 90, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (11, 'ポンタファームでモンスターを育成する→レベル4', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 120, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (12, 'ポンタファームでモンスターを育成する→レベル5', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 150, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (13, 'ポンタファームでモンスターを育成する→レベル6', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 180, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (14, 'ポンタファームでモンスターを育成する→レベル7', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 210, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (15, 'ポンタファームでモンスターを育成する→レベル8', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 240, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (16, 'ポンタファームでモンスターを育成する→レベル9', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 270, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (17, 'ポンタファームでモンスターを育成する→レベル10', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 300, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (18, 'ポンタファームでモンスターを育成する→レベル11', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 360, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (19, 'ポンタファームでモンスターを育成する→レベル12', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 420, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (20, 'ポンタファームでモンスターを育成する→レベル13', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 480, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (21, 'ポンタファームでモンスターを育成する→レベル14', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 540, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (22, 'ポンタファームでモンスターを育成する→レベル15', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 600, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (23, 'ポンタファームでモンスターを育成する→レベル16', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 720, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (24, 'ポンタファームでモンスターを育成する→レベル17', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 840, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (25, 'ポンタファームでモンスターを育成する→レベル18', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 960, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (26, 'ポンタファームでモンスターを育成する→レベル19', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 1080, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (27, 'ポンタファームでモンスターを育成する→レベル20', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 1200, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (28, 'ポンタファームでモンスターを育成する→レベル21', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 1440, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (29, 'ポンタファームでモンスターを育成する→レベル22', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 1680, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (30, 'ポンタファームでモンスターを育成する→レベル23', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 1920, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (31, 'ポンタファームでモンスターを育成する→レベル24', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 2160, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (32, 'ポンタファームでモンスターを育成する→レベル25', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 2400, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (33, 'ポンタファームでモンスターを育成する→レベル26', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 2880, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (34, 'ポンタファームでモンスターを育成する→レベル27', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 3360, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (35, 'ポンタファームでモンスターを育成する→レベル28', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 3840, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (36, 'ポンタファームでモンスターを育成する→レベル29', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 4320, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());

INSERT INTO `choice_activity_rule` (id, activity_name, activity_function, ordered, id_type, obtain_frequency, get_Type,
                                    power_amount, power_amount_rate, effective_date, expiry_date, status, created_at,
                                    updated_at)
VALUES (37, 'ポンタファームでモンスターを育成する→レベル30', 'FARM', 100, 'Operate', 'MONTHLY', 'FIXED', 4800, NULL,
        '2025-05-31 00:00:00', '2035-05-31 00:00:00', 'ACTIVE', now(), now());
-- ----------------------------
-- Records of crop_growth_stage
-- ----------------------------
INSERT INTO `crop_growth_stage` VALUES (1, 'INVEST', 1, '枯れた状態', -9007199254740991, -100, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (2, 'INVEST', 2, '芽が出た状態', -99, 50, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (3, 'INVEST', 3, '成長過程', 51, 1000, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (4, 'INVEST', 4, '成長終了通常色', 1001, 10000, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (5, 'INVEST', 5, '銀色', 10001, 50000, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (6, 'INVEST', 6, '金色', 50001, 100000, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (7, 'INVEST', 7, '虹色', 100001, 9007199254740991, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (8, 'OPERATE', 1, '枯れた状態', -9007199254740991, -100, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (9, 'OPERATE', 2, '芽が出た状態', -99, 50, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (10,'OPERATE', 3, '成長過程', 51, 300, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (11,'OPERATE', 4, '成長終了通常色', 301, 600, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (12,'OPERATE', 5, '銀色', 601, 1000, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (13,'OPERATE', 6, '金色', 1001, 3000, now(), now(), 'admin-DML');
INSERT INTO `crop_growth_stage` VALUES (14,'OPERATE', 7, '虹色', 3001, 9007199254740991, now(), now(), 'admin-DML');

-- ----------------------------
-- Records of monster_growth_rule
-- ----------------------------
INSERT INTO `monster_growth_rule` VALUES (1, NULL, 4, 0, '0', now(), NULL, now(), NULL);
INSERT INTO `monster_growth_rule` VALUES (2, 5, 50, 0.1, '1-5', now(), NULL, now(), NULL);
INSERT INTO `monster_growth_rule` VALUES (3, 51, 1000, 0.2, '10-200', now(), NULL, now(), NULL);
INSERT INTO `monster_growth_rule` VALUES (4, 1001, 10000, 0.25, '250-2500', now(), NULL, now(), NULL);
INSERT INTO `monster_growth_rule` VALUES (5, 10001, 50000, 0.3, '3000-15000', now(), NULL, now(), NULL);
INSERT INTO `monster_growth_rule` VALUES (6, 50001, 100000, 0.4, '20000-40000', now(), NULL, now(), NULL);
INSERT INTO `monster_growth_rule` VALUES (7, 100001, NULL, 0.5, '50000-', now(), NULL, now(), NULL);

-- ----------------------------
-- Records of monster_base
-- ----------------------------

INSERT INTO `monster_base` VALUES (2, 'フサフサ', 'Operate', NULL, 30, now(), NULL, now(), NULL);

-- ----------------------------
-- Records of monster_growth_level
-- ----------------------------
INSERT INTO `monster_growth_level` VALUES (1, 0, 8, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (2, 100, 9, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (3, 300, 10, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (4, 600, 11, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (5, 1000, 12, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (6, 5000, 13, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (7, 10000, 14, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (8, 15000, 15, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (9, 30000, 16, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (10, 50000, 17, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (11, 75000, 18, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (12, 105000, 19, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (13, 140000, 20, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (14, 180000, 21, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (15, 225000, 22, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (16, 275000, 23, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (17, 330000, 24, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (18, 390000, 25, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (19, 455000, 26, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (20, 525000, 27, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (21, 600000, 28, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (22, 680000, 29, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (23, 765000, 30, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (24, 855000, 31, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (25, 950000, 32, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (26, 1050000, 33, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (27, 1155000, 34, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (28, 1265000, 35, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (29, 1380000, 36, now(), NULL, now(), NULL);
INSERT INTO `monster_growth_level` VALUES (30, 1500000, 37, now(), NULL, now(), NULL);

SET FOREIGN_KEY_CHECKS = 1;